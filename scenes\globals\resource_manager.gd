extends Node

# 资源管理器
# 全局单例，用于管理游戏中的自定义资源

# 资源类型枚举
enum ResourceType {
	HEALTH,
	PLAYER,
	ENEMY_TRIANGLE,
	PLAYER_BULLET,
	ENEMY_BULLET
}

# 资源路径
const CUSTOM_RESOURCES_PATH = "res://custom_resources/"
const DEFAULT_RESOURCES_PATH = "res://scenes/"

# 资源子目录
const RESOURCE_SUBDIRS = {
	ResourceType.HEALTH: "health",
	ResourceType.PLAYER: "player",
	ResourceType.ENEMY_TRIANGLE: "enemies",
	ResourceType.PLAYER_BULLET: "bullets",
	ResourceType.ENEMY_BULLET: "bullets"
}

# 默认资源
const DEFAULT_RESOURCES = {
	ResourceType.HEALTH: "ui/health_icon.tscn",
	ResourceType.PLAYER: "player/player_model.tres",
	ResourceType.ENEMY_TRIANGLE: "enemies/enemy_triangle_model.tres",
	ResourceType.PLAYER_BULLET: "player/player_bullet_model.tres",
	ResourceType.ENEMY_BULLET: "enemies/enemy_bullet_model.tres"
}

# 资源加载器
var resource_loader = preload("res://scenes/globals/resource_loader.gd").new()

# 缓存已加载的资源
var _cached_resources = {}

# 缓存已加载的纹理
var texture_cache = {}

func _ready():
	print("ResourceManager: 初始化...")
	# 初始化时预加载所有资源
	preload_all_resources()
	print("ResourceManager: 初始化完成")

# 预加载所有资源
func preload_all_resources():
	for resource_type in ResourceType.values():
		var resource = get_resource(resource_type)
		print("ResourceManager: 加载资源 ", resource_type, " 完成")

# 获取指定类型的资源
func get_resource(resource_type):
	# 如果已经缓存了该资源，直接返回
	if _cached_resources.has(resource_type):
		return _cached_resources[resource_type]
	
	# 尝试从自定义资源目录加载
	var resource = load_custom_resource(resource_type)
	
	# 如果自定义资源不存在，加载默认资源
	if resource == null:
		resource = load_default_resource(resource_type)
		if resource == null:
			print("ResourceManager: 警告 - 无法加载默认资源 ", resource_type)
			return null
	
	# 缓存并返回资源
	_cached_resources[resource_type] = resource
	return resource

# 加载自定义资源
func load_custom_resource(resource_type):
	var subdir = RESOURCE_SUBDIRS[resource_type]
	var dir_path = CUSTOM_RESOURCES_PATH + subdir
	
	# 使用文件系统工具检查目录是否存在
	if not FileSystemUtils.dir_exists(dir_path):
		print("ResourceManager: 自定义资源目录不存在 ", dir_path)
		return null
	
	# 查找目录中的第一个图像文件
	var image_path = FileSystemUtils.find_first_image_in_directory(dir_path)
	if image_path != "":
		print("ResourceManager: 找到自定义资源 ", image_path)
		var texture = FileSystemUtils.load_image_as_texture(image_path)
		return texture
	
	print("ResourceManager: 未找到自定义资源 ", resource_type)
	return null

# 加载默认资源
func load_default_resource(resource_type):
	var path = DEFAULT_RESOURCES_PATH + DEFAULT_RESOURCES[resource_type]
	print("ResourceManager: 加载默认资源 ", path)
	
	# 检查文件是否存在
	if ResourceLoader.exists(path):
		return load(path)
	else:
		print("ResourceManager: 默认资源不存在 ", path)
		return null

# 重新加载所有资源（可以在运行时调用以刷新资源）
func reload_all_resources():
	print("ResourceManager: 重新加载所有资源...")
	_cached_resources.clear()
	texture_cache.clear()
	preload_all_resources()
	print("ResourceManager: 重新加载完成")

# 获取道具纹理
func get_powerup_texture(powerup_type: String) -> Texture2D:
	# 检查缓存中是否已有该纹理
	if texture_cache.has(powerup_type):
		return texture_cache[powerup_type]
	
	# 尝试加载自定义纹理
	var path = FileSystemUtils.get_custom_resource_path("powerups/" + powerup_type)
	var image_path = FileSystemUtils.find_first_image_in_directory(path)
	
	# 如果找到图像，加载并缓存
	if image_path != "":
		var texture = FileSystemUtils.load_image_as_texture(image_path)
		if texture:
			texture_cache[powerup_type] = texture
			return texture
	
	# 否则尝试加载默认图标
	var default_path = "res://scenes/powerups/icons/" + powerup_type + ".svg"
	if FileSystemUtils.file_exists(default_path):
		var texture = load(default_path)
		texture_cache[powerup_type] = texture
		return texture
	
	# 如果都没有找到，返回null
	return null

# 检查是否有自定义道具纹理
func has_custom_powerup_texture(powerup_type: String) -> bool:
	# 先检查缓存
	if texture_cache.has(powerup_type):
		return true
	
	# 再检查文件系统
	var path = FileSystemUtils.get_custom_resource_path("powerups/" + powerup_type)
	return FileSystemUtils.has_images_in_directory(path)

# 清除纹理缓存
func clear_texture_cache():
	texture_cache.clear()
	print("已清除纹理缓存") 
