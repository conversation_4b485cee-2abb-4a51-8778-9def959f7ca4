extends Node

# 粒子效果管理器
# 集中管理所有游戏中的粒子效果，提供通用的创建和管理方法

enum ParticleType {
	SHIELD_HIT,     # 护盾受击效果
	PLAYER_HIT,     # 玩家受击效果
	ENEMY_HIT,      # 敌人受击效果
	ENEMY_DEATH,    # 敌人死亡效果
	PLAYER_DEATH,   # 玩家死亡效果
	POWERUP_PICKUP, # 道具拾取效果
	BOMB_EXPLOSION, # 炸弹爆炸效果
	THRUSTER       # 推进器效果
}

# 粒子效果预设
var particle_presets = {
	ParticleType.SHIELD_HIT: {
		"amount": 30,
		"lifetime": 0.8,
		"explosiveness": 0.9,
		"direction": Vector2(0, 0),
		"spread": 180,
		"gravity": Vector2(0, 0),
		"initial_velocity_min": 80,
		"initial_velocity_max": 150,
		"scale_amount_min": 2.0,
		"scale_amount_max": 4.0,
		"color": Color(0, 0.5, 1),  # 蓝色
		"one_shot": true
	},
	ParticleType.PLAYER_HIT: {
		"amount": 20,
		"lifetime": 0.6,
		"explosiveness": 0.8,
		"direction": Vector2(0, 0),
		"spread": 180,
		"gravity": Vector2(0, 0),
		"initial_velocity_min": 60,
		"initial_velocity_max": 120,
		"scale_amount_min": 1.5,
		"scale_amount_max": 3.0,
		"color": Color(1, 0.3, 0.3),  # 红色
		"one_shot": true
	},
	ParticleType.ENEMY_HIT: {
		"amount": 8,
		"lifetime": 0.4,
		"explosiveness": 0.9,
		"direction": Vector2(0, 0),
		"spread": 180,
		"gravity": Vector2(0, 0),
		"initial_velocity_min": 50,
		"initial_velocity_max": 100,
		"scale_amount_min": 1.0,
		"scale_amount_max": 2.0,
		"color": Color(1, 0.2, 0.2),  # 红色
		"one_shot": true
	},
	ParticleType.ENEMY_DEATH: {
		"amount": 15,
		"lifetime": 0.6,
		"explosiveness": 1.0,
		"direction": Vector2(0, 0),
		"spread": 180,
		"gravity": Vector2(0, 0),
		"initial_velocity_min": 70,
		"initial_velocity_max": 140,
		"scale_amount_min": 1.5,
		"scale_amount_max": 3.0,
		"color": Color(1, 0.4, 0.2),  # 橙红色
		"one_shot": true
	},
	ParticleType.PLAYER_DEATH: {
		"amount": 100,
		"lifetime": 1.0,
		"explosiveness": 1.0,
		"direction": Vector2(0, 0),
		"spread": 180,
		"gravity": Vector2(0, 0),
		"initial_velocity_min": 100,
		"initial_velocity_max": 300,
		"scale_amount_min": 3.0,
		"scale_amount_max": 6.0,
		"color": Color(1, 0.7, 0.2),  # 橙黄色
		"one_shot": true
	},
	ParticleType.POWERUP_PICKUP: {
		"amount": 20,
		"lifetime": 0.5,
		"explosiveness": 0.8,
		"direction": Vector2(0, 0),
		"spread": 180,
		"gravity": Vector2(0, 0),
		"initial_velocity_min": 50,
		"initial_velocity_max": 100,
		"scale_amount_min": 1.0,
		"scale_amount_max": 2.0,
		"color": Color(0.3, 1, 0.3),  # 绿色
		"one_shot": true
	},
	ParticleType.BOMB_EXPLOSION: {
		"amount": 80,
		"lifetime": 1.2,
		"explosiveness": 1.0,
		"direction": Vector2(0, 0),
		"spread": 180,
		"gravity": Vector2(0, 0),
		"initial_velocity_min": 150,
		"initial_velocity_max": 350,
		"scale_amount_min": 3.0,
		"scale_amount_max": 7.0,
		"color": Color(1, 0.5, 0.1),  # 橙色
		"one_shot": true
	},
	ParticleType.THRUSTER: {
		"amount": 12,
		"lifetime": 0.4,
		"explosiveness": 0.0,
		"direction": Vector2(0, 1),
		"spread": 10,
		"gravity": Vector2(0, 0),
		"initial_velocity_min": 50,
		"initial_velocity_max": 80,
		"scale_amount_min": 1.0,
		"scale_amount_max": 2.0,
		"color": Color(0.2, 0.7, 1.0),  # 蓝色
		"one_shot": false
	}
}

# 创建粒子效果
func create_particles(type: ParticleType, position: Vector2, custom_params: Dictionary = {}) -> CPUParticles2D:
	# 获取预设参数
	var preset = particle_presets[type].duplicate()
	
	# 应用自定义参数
	for key in custom_params:
		preset[key] = custom_params[key]
	
	# 创建粒子节点
	var particles = CPUParticles2D.new()
	get_tree().current_scene.add_child(particles)
	particles.position = position
	
	# 应用参数
	particles.amount = preset.amount
	particles.lifetime = preset.lifetime
	particles.explosiveness = preset.explosiveness
	particles.direction = preset.direction
	particles.spread = preset.spread
	particles.gravity = preset.gravity
	particles.initial_velocity_min = preset.initial_velocity_min
	particles.initial_velocity_max = preset.initial_velocity_max
	particles.scale_amount_min = preset.scale_amount_min
	particles.scale_amount_max = preset.scale_amount_max
	particles.color = preset.color
	particles.emitting = true
	particles.one_shot = preset.one_shot
	
	# 如果是一次性粒子，设置自动销毁
	if preset.one_shot:
		var timer = Timer.new()
		particles.add_child(timer)
		timer.wait_time = preset.lifetime + 0.5  # 额外时间确保所有粒子都已消失
		timer.one_shot = true
		timer.autostart = true
		timer.timeout.connect(func(): particles.queue_free())
	
	return particles

# 创建护盾受击效果
func create_shield_hit_effect(position: Vector2) -> CPUParticles2D:
	return create_particles(ParticleType.SHIELD_HIT, position)

# 创建玩家受击效果
func create_player_hit_effect(position: Vector2) -> CPUParticles2D:
	return create_particles(ParticleType.PLAYER_HIT, position)

# 创建敌人受击效果
func create_enemy_hit_effect(position: Vector2) -> CPUParticles2D:
	return create_particles(ParticleType.ENEMY_HIT, position)

# 创建敌人死亡效果
func create_enemy_death_effect(position: Vector2) -> CPUParticles2D:
	return create_particles(ParticleType.ENEMY_DEATH, position)

# 创建玩家死亡效果
func create_player_death_effect(position: Vector2) -> CPUParticles2D:
	return create_particles(ParticleType.PLAYER_DEATH, position)

# 创建道具拾取效果
func create_powerup_pickup_effect(position: Vector2, color: Color = Color(0.3, 1, 0.3)) -> CPUParticles2D:
	return create_particles(ParticleType.POWERUP_PICKUP, position, {"color": color})

# 创建炸弹爆炸效果
func create_bomb_explosion_effect(position: Vector2) -> CPUParticles2D:
	return create_particles(ParticleType.BOMB_EXPLOSION, position)

# 创建推进器效果
func create_thruster_effect(position: Vector2) -> CPUParticles2D:
	return create_particles(ParticleType.THRUSTER, position) 