# 自定义改装配件

这个文件夹用于存放自定义的改装配件资源。

## 如何创建自定义改装配件

### 方法1: 使用JSON文件

在 `scenes/modifications/data/` 目录下创建一个JSON文件，例如 `mod_custom.json`：

```json
{
  "id": "mod_custom",
  "name": "自定义配件",
  "description": "这是一个自定义配件",
  "color": {
    "r": 0.8,
    "g": 0.2,
    "b": 0.8,
    "a": 1.0
  },
  "shield_bonus": 1,
  "health_bonus": 0,
  "speed_bonus": 0.0,
  "damage_bonus": 0.0,
  "effect_tags": ["shield_cap_increase", "custom"]
}
```

### 方法2: 使用代码创建

```gdscript
# 创建一个新的护盾增强配件
var mod = ModificationFactory.create_shield_mod(
    "mod_custom",
    "自定义护盾增强",
    "增加1点护盾上限",
    Color(0.5, 0.5, 1.0),
    1
)

# 保存配件
ModificationFactory.save_modification_to_file(mod)
```

## 配件效果

目前支持的配件效果有：

- `shield_bonus`: 护盾上限加成
- `health_bonus`: 生命上限加成
- `speed_bonus`: 速度加成
- `damage_bonus`: 伤害加成

## 自定义效果

可以通过 `effect_tags` 添加自定义效果标签，然后在游戏中检测这些标签。 