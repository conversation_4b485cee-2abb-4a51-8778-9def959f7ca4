extends Node
class_name TooltipManager

# Tooltip Manager
# Responsible for displaying and hiding tooltips

# Singleton instance
static var _instance = null

# Current displayed tooltip
var current_tooltip = null

# Initialize
func _init():
	# Set singleton
	_instance = self

# Get singleton instance
static func get_instance():
	if _instance == null:
		_instance = TooltipManager.new()
	return _instance

# Show tooltip
func show_tooltip(title: String, description: String):
	print("TooltipManager: Showing tooltip - " + title)
	
	# If there's already a tooltip, hide it first
	if current_tooltip != null:
		hide_tooltip()
	
	# Create tooltip node
	current_tooltip = Control.new()
	current_tooltip.name = "Tooltip"
	current_tooltip.z_index = 1000  # Ensure it's on top
	
	# Create panel background
	var panel = Panel.new()
	panel.size_flags_horizontal = Control.SIZE_FILL
	panel.size_flags_vertical = Control.SIZE_FILL
	
	# Create content container
	var container = VBoxContainer.new()
	container.size_flags_horizontal = Control.SIZE_FILL
	container.size_flags_vertical = Control.SIZE_FILL
	container.custom_minimum_size = Vector2(200, 100)
	
	# Create title
	var title_label = Label.new()
	title_label.text = title
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 16)
	
	# Create description
	var desc_label = Label.new()
	desc_label.text = description
	desc_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_LEFT
	desc_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	desc_label.custom_minimum_size = Vector2(180, 0)
	
	# Assemble UI
	container.add_child(title_label)
	container.add_child(desc_label)
	panel.add_child(container)
	current_tooltip.add_child(panel)
	
	# Set size and margins
	container.position = Vector2(10, 10)
	container.size = Vector2(180, 80)
	panel.size = Vector2(200, 100)
	
	# Get mouse position
	var mouse_pos = get_viewport().get_mouse_position()
	
	# Adjust position to ensure it's within screen
	var viewport_size = get_viewport().get_visible_rect().size
	var tooltip_pos = mouse_pos + Vector2(10, 10)  # Offset a bit, not directly under mouse
	
	# Ensure tooltip doesn't exceed right boundary
	if tooltip_pos.x + panel.size.x > viewport_size.x:
		tooltip_pos.x = viewport_size.x - panel.size.x
	
	# Ensure tooltip doesn't exceed bottom boundary
	if tooltip_pos.y + panel.size.y > viewport_size.y:
		tooltip_pos.y = mouse_pos.y - panel.size.y - 10  # Place above mouse
	
	# Set position
	current_tooltip.position = tooltip_pos
	
	# Add to scene tree
	get_tree().get_root().add_child(current_tooltip)
	
	print("TooltipManager: Tooltip displayed successfully")

# Hide tooltip
func hide_tooltip():
	if current_tooltip != null:
		print("TooltipManager: Hiding tooltip")
		current_tooltip.queue_free()
		current_tooltip = null 
