[gd_scene load_steps=3 format=3 uid="uid://b7g2bqcxmgbdl"]

[ext_resource type="Script" path="res://scenes/player/player_bullet.gd" id="1_uh4ys"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_qkx1h"]
size = Vector2(10, 20)

[node name="PlayerBullet" type="Area2D" groups=["player_bullets"]]
collision_layer = 2
collision_mask = 4
script = ExtResource("1_uh4ys")

[node name="Polygon2D" type="Polygon2D" parent="."]
color = Color(0.4, 0.7, 1, 1)
polygon = PackedVector2Array(-2.5, -7, 2.5, -7, 2.5, 7, -2.5, 7)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_qkx1h")

[connection signal="area_entered" from="." to="." method="_on_area_entered"] 