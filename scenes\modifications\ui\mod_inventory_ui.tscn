[gd_scene load_steps=4 format=3 uid="uid://crvj0ggxb78kw"]

[ext_resource type="Script" path="res://scenes/modifications/ui/mod_inventory_ui.gd" id="1_ynojk"]
[ext_resource type="Theme" uid="uid://dd8xdg3twycsx" path="res://themes/mod_ui_theme.tres" id="2_ukw3m"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_1"]

[node name="ModInventoryUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_ynojk")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 10

[node name="ScrollContainer" type="ScrollContainer" parent="VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxEmpty_1")
horizontal_scroll_mode = 0
vertical_scroll_mode = 2

[node name="ItemGrid" type="GridContainer" parent="VBoxContainer/ScrollContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/h_separation = 12
theme_override_constants/v_separation = 12
columns = 10 