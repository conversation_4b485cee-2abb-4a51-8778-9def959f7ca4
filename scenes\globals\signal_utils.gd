extends Node

# 信号工具类
# 提供安全的信号连接和管理方法

# 安全连接信号
# 如果信号已经连接，则不会重复连接
func safe_connect(source: Object, signal_name: String, target: Callable) -> bool:
	if not source.is_connected(signal_name, target):
		var result = source.connect(signal_name, target)
		return result == OK
	return true

# 安全断开信号
# 如果信号已经连接，则断开连接
func safe_disconnect(source: Object, signal_name: String, target: Callable) -> bool:
	if source.is_connected(signal_name, target):
		source.disconnect(signal_name, target)
		return true
	return false

# 连接一次性信号
# 信号触发后会自动断开连接
func connect_one_shot(source: Object, signal_name: String, target: Callable) -> bool:
	# 创建一次性信号处理函数
	var one_shot_handler = func():
		# 首先断开信号连接
		var connections = source.get_signal_connection_list(signal_name)
		for connection in connections:
			if connection["callable"].get_object() == self:
				source.disconnect(signal_name, connection["callable"])
		# 然后调用目标函数
		target.call()
	
	# 连接一次性处理函数
	var result = source.connect(signal_name, one_shot_handler)
	return result == OK

# 延迟连接信号
# 在下一帧连接信号
func deferred_connect(source: Object, signal_name: String, target: Callable) -> bool:
	# 创建参数数组，用于传递给 _do_connect
	var args = [source, signal_name, target]
	
	# 使用 call_deferred 在下一帧连接信号
	call_deferred("_do_connect", args)
	return true

# 实际执行延迟连接的函数
func _do_connect(args: Array) -> void:
	var source = args[0]
	var signal_name = args[1]
	var target = args[2]
	
	if is_instance_valid(source):
		safe_connect(source, signal_name, target)

# 连接到组中的所有节点
func connect_to_group(group_name: String, signal_name: String, target: Callable) -> int:
	var nodes = get_tree().get_nodes_in_group(group_name)
	var count = 0
	
	for node in nodes:
		if node.has_signal(signal_name):
			if safe_connect(node, signal_name, target):
				count += 1
	
	return count

# 查找并连接到特定类型的节点
func find_and_connect(node_path: String, signal_name: String, target: Callable) -> bool:
	var node = get_tree().get_root().get_node_or_null(node_path)
	
	if node and node.has_signal(signal_name):
		return safe_connect(node, signal_name, target)
	
	return false

# 查找并连接到第一个组内节点
func find_first_in_group_and_connect(group_name: String, signal_name: String, target: Callable) -> bool:
	var node = get_tree().get_first_node_in_group(group_name)
	
	if node and node.has_signal(signal_name):
		return safe_connect(node, signal_name, target)
	
	return false 
