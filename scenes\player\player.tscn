[gd_scene load_steps=9 format=3 uid="uid://c1w1n8mfpyasc"]

[ext_resource type="Script" uid="uid://dune4484yfvp4" path="res://scenes/player/player.gd" id="1_gfv15"]
[ext_resource type="PackedScene" uid="uid://b7g2bqcxmgbdl" path="res://scenes/player/player_bullet.tscn" id="2_3yqwq"]

[sub_resource type="Gradient" id="Gradient_0iqnl"]
offsets = PackedFloat32Array(0, 0.3, 0.7, 1)
colors = PackedColorArray(1, 0.9, 0.3, 1, 1, 0.7, 0.2, 1, 0.9, 0.3, 0.1, 0.8, 0.7, 0.1, 0.1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_dxbvj"]
gradient = SubResource("Gradient_0iqnl")

[sub_resource type="Curve" id="Curve_8i2qc"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), -2.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_4iqh2"]
curve = SubResource("Curve_8i2qc")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_vgxh4"]
particle_flag_disable_z = true
emission_shape = 1
emission_sphere_radius = 1.0
direction = Vector3(0, 1, 0)
spread = 30.0
initial_velocity_min = 50.0
initial_velocity_max = 100.0
gravity = Vector3(0, 0, 0)
scale_min = 2.0
scale_max = 3.0
scale_curve = SubResource("CurveTexture_4iqh2")
color_ramp = SubResource("GradientTexture1D_dxbvj")

[sub_resource type="RectangleShape2D" id="RectangleShape2D_jlw1q"]
size = Vector2(22, 22)

[node name="Player" type="Area2D" groups=["player"]]
collision_mask = 28
script = ExtResource("1_gfv15")
bullet_scene = ExtResource("2_3yqwq")

[node name="ShieldOutline" type="Node2D" parent="."]
modulate = Color(0.2, 1, 1, 1)
z_index = 1
scale = Vector2(1.8, 1.8)

[node name="OuterPolygon" type="Polygon2D" parent="ShieldOutline"]
visible = false
color = Color(0.2, 1, 1, 0.7)
polygon = PackedVector2Array(-14, -14, 14, -14, 14, 14, -14, 14, -14, -14)

[node name="ShieldLines" type="Node2D" parent="ShieldOutline"]

[node name="TopLine" type="Line2D" parent="ShieldOutline/ShieldLines"]
points = PackedVector2Array(-16, -16, 16, -16)
width = 2.0
default_color = Color(0.2, 1, 1, 0.6)
begin_cap_mode = 2
end_cap_mode = 2
antialiased = true

[node name="RightLine" type="Line2D" parent="ShieldOutline/ShieldLines"]
points = PackedVector2Array(16, -16, 16, 16)
width = 2.0
default_color = Color(0.2, 1, 1, 0.6)
begin_cap_mode = 2
end_cap_mode = 2
antialiased = true

[node name="BottomLine" type="Line2D" parent="ShieldOutline/ShieldLines"]
points = PackedVector2Array(16, 16, -16, 16)
width = 2.0
default_color = Color(0.2, 1, 1, 0.6)
begin_cap_mode = 2
end_cap_mode = 2
antialiased = true

[node name="LeftLine" type="Line2D" parent="ShieldOutline/ShieldLines"]
points = PackedVector2Array(-16, 16, -16, -16)
width = 2.0
default_color = Color(0.2, 1, 1, 0.6)
begin_cap_mode = 2
end_cap_mode = 2
antialiased = true

[node name="ShieldAnimationTimer" type="Timer" parent="ShieldOutline"]
wait_time = 0.05
autostart = true

[node name="BlueSquare" type="Node2D" parent="."]
scale = Vector2(1.8, 1.8)

[node name="OutlinePolygon" type="Polygon2D" parent="BlueSquare"]
color = Color(0, 0.498039, 1, 1)
polygon = PackedVector2Array(-11.25, -11.25, 11.25, -11.25, 11.25, 11.25, -11.25, 11.25, -11.25, -11.25)

[node name="InnerPolygon" type="Polygon2D" parent="BlueSquare"]
color = Color(0, 0, 0, 1)
polygon = PackedVector2Array(-9.75, -9.75, 9.75, -9.75, 9.75, 9.75, -9.75, 9.75, -9.75, -9.75)

[node name="WhiteCircle" type="Node2D" parent="."]
scale = Vector2(1.8, 1.8)

[node name="CirclePolygon" type="Polygon2D" parent="WhiteCircle"]
polygon = PackedVector2Array(0, -3, 1.05, -2.78, 1.95, -2.25, 2.63, -1.5, 2.93, -0.6, 3, 0, 2.93, 0.6, 2.63, 1.5, 1.95, 2.25, 1.05, 2.78, 0, 3, -1.05, 2.78, -1.95, 2.25, -2.63, 1.5, -2.93, 0.6, -3, 0, -2.93, -0.6, -2.63, -1.5, -1.95, -2.25, -1.05, -2.78)

[node name="ThrusterParticles" type="GPUParticles2D" parent="."]
position = Vector2(0, 22)
scale = Vector2(1.8, 1.8)
emitting = false
amount = 18
lifetime = 0.3
randomness = 0.2
local_coords = true
process_material = SubResource("ParticleProcessMaterial_vgxh4")

[node name="ShootTimer" type="Timer" parent="."]
wait_time = 0.2
autostart = true

[node name="BulletSpawnPoint" type="Marker2D" parent="."]
position = Vector2(0, -18)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
scale = Vector2(1.8, 1.8)
shape = SubResource("RectangleShape2D_jlw1q")

[connection signal="area_entered" from="." to="." method="_on_area_entered"]
[connection signal="timeout" from="ShootTimer" to="." method="_on_shoot_timer_timeout"]
