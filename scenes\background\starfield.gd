extends Node2D

# 星空背景脚本
# 实现动态滚动的星空效果

# 星星属性
@export var star_count: int = 200 # 星星数量
@export var star_colors: Array[Color] = [
	Color(0.9, 0.9, 1.0, 0.7),  # 明亮的白色
	Color(0.8, 0.8, 1.0, 0.6),  # 淡蓝色
	Color(1.0, 0.9, 0.8, 0.5)   # 淡黄色
]
@export var star_sizes: Array[float] = [1.0, 1.5, 2.0, 2.5] # 星星尺寸
@export var scroll_speed: Array[float] = [20.0, 30.0, 40.0] # 不同层星星的滚动速度
@export var background_color: Color = Color(0.02, 0.03, 0.05, 1.0) # 深空蓝黑色背景
@export var twinkle_speed: float = 2.0 # 闪烁速度
@export var twinkle_amount: float = 0.4 # 闪烁幅度 (0-1)

# 设计分辨率
@export var design_width: float = 2560.0
@export var design_height: float = 1440.0

# 星星数据结构
class Star:
	var position: Vector2
	var size: float
	var color: Color
	var base_color: Color  # 原始颜色
	var speed: float
	var layer: int # 星星所在的层
	var twinkle_offset: float # 闪烁偏移，使每颗星星闪烁不同步

# 星星数组
var stars: Array = []
var time: float = 0.0 # 用于闪烁效果的时间累积
var current_screen_size: Vector2 = Vector2.ZERO

func _ready():
	# 获取当前视口大小
	update_screen_size()
	
	# 监听窗口大小变化
	get_viewport().size_changed.connect(update_screen_size)
	
	# 生成初始星星
	generate_stars()

func update_screen_size():
	# 获取当前视口大小
	current_screen_size = get_viewport_rect().size
	
	# 如果星星已经生成，则需要重新生成以适应新的屏幕大小
	if stars.size() > 0:
		generate_stars()

func generate_stars():
	stars.clear()
	
	# 创建三层星星，每层具有不同的速度
	for layer in range(3):
		var layer_star_count = star_count / 3
		if layer == 0:
			layer_star_count += star_count % 3
		
		for i in range(layer_star_count):
			var star = Star.new()
			star.position = Vector2(
				randf() * design_width,
				randf() * design_height
			)
			star.size = star_sizes[randi() % star_sizes.size()]
			star.base_color = star_colors[randi() % star_colors.size()]
			star.color = star.base_color
			star.speed = scroll_speed[layer]
			star.layer = layer
			star.twinkle_offset = randf() * 2.0 * PI # 随机偏移量
			stars.append(star)

func _process(delta):
	# 更新时间
	time += delta
	
	# 更新星星位置和颜色
	for star in stars:
		# 星星向下移动
		star.position.y += star.speed * delta
		
		# 如果星星移出屏幕底部，重新放置到顶部
		if star.position.y > design_height:
			star.position.y = 0
			star.position.x = randf() * design_width
		
		# 更新星星闪烁效果
		var twinkle_factor = sin(time * twinkle_speed + star.twinkle_offset) * 0.5 + 0.5
		twinkle_factor = 1.0 - (twinkle_amount * twinkle_factor)
		star.color = star.base_color
		star.color.a = star.base_color.a * twinkle_factor
	
	# 重绘星空
	queue_redraw()

func _draw():
	# 绘制背景色
	draw_rect(Rect2(0, 0, design_width, design_height), background_color)
	
	# 绘制所有星星
	for star in stars:
		draw_circle(star.position, star.size, star.color) 