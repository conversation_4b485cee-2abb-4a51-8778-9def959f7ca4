[gd_scene load_steps=5 format=3 uid="uid://bfuegta8nt8gi"]

[ext_resource type="Script" path="res://scenes/ui/mod_inventory_panel.gd" id="1_8jb05"]
[ext_resource type="Theme" uid="uid://dd8xdg3twycsx" path="res://themes/mod_ui_theme.tres" id="2_0gkxx"]
[ext_resource type="PackedScene" uid="uid://crvj0ggxb78kw" path="res://scenes/modifications/ui/mod_inventory_ui.tscn" id="3_eqtmr"]
[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.105882, 0.211765, 0.392157, 0.9)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 0.388235, 0.6, 0.8)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="ModInventoryPanel" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_left = 0.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = -350.0
offset_right = -50.0
grow_horizontal = 2
grow_vertical = 0
script = ExtResource("1_8jb05")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_0gkxx")
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 30
theme_override_constants/margin_top = 5
theme_override_constants/margin_right = 30
theme_override_constants/margin_bottom = 5

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 6

[node name="HeaderContainer" type="HBoxContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="Title" type="Label" parent="MarginContainer/VBoxContainer/HeaderContainer"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("2_0gkxx")
theme_override_font_sizes/font_size = 24
text = "改装件仓库"

[node name="HSeparator" type="HSeparator" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="FiltersContainer" type="HBoxContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="SizeFilters" type="HBoxContainer" parent="MarginContainer/VBoxContainer/FiltersContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/FiltersContainer/SizeFilters"]
layout_mode = 2
theme = ExtResource("2_0gkxx")
text = "尺寸:"

[node name="FilterSizeAll" type="Button" parent="MarginContainer/VBoxContainer/FiltersContainer/SizeFilters"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("2_0gkxx")
toggle_mode = true
button_pressed = true
text = "全部"

[node name="FilterSizeSmall" type="Button" parent="MarginContainer/VBoxContainer/FiltersContainer/SizeFilters"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("2_0gkxx")
toggle_mode = true
text = "小型"

[node name="FilterSizeMedium" type="Button" parent="MarginContainer/VBoxContainer/FiltersContainer/SizeFilters"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("2_0gkxx")
toggle_mode = true
text = "中型"

[node name="FilterSizeLarge" type="Button" parent="MarginContainer/VBoxContainer/FiltersContainer/SizeFilters"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("2_0gkxx")
toggle_mode = true
text = "大型"

[node name="VSeparator" type="VSeparator" parent="MarginContainer/VBoxContainer/FiltersContainer"]
layout_mode = 2

[node name="TypeFilters" type="HBoxContainer" parent="MarginContainer/VBoxContainer/FiltersContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/FiltersContainer/TypeFilters"]
layout_mode = 2
theme = ExtResource("2_0gkxx")
text = "类型:"

[node name="FilterTypeAll" type="Button" parent="MarginContainer/VBoxContainer/FiltersContainer/TypeFilters"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("2_0gkxx")
toggle_mode = true
button_pressed = true
text = "全部"

[node name="FilterTypeAttack" type="Button" parent="MarginContainer/VBoxContainer/FiltersContainer/TypeFilters"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("2_0gkxx")
toggle_mode = true
text = "攻击"

[node name="FilterTypeDefense" type="Button" parent="MarginContainer/VBoxContainer/FiltersContainer/TypeFilters"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("2_0gkxx")
toggle_mode = true
text = "防御"

[node name="FilterTypeSpecial" type="Button" parent="MarginContainer/VBoxContainer/FiltersContainer/TypeFilters"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("2_0gkxx")
toggle_mode = true
text = "特殊"

[node name="FilterTypeCore" type="Button" parent="MarginContainer/VBoxContainer/FiltersContainer/TypeFilters"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("2_0gkxx")
toggle_mode = true
text = "核心"

[node name="ModInventoryUI" parent="MarginContainer/VBoxContainer" instance=ExtResource("3_eqtmr")]
layout_mode = 2
size_flags_vertical = 3 