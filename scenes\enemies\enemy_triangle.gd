extends Area2D

# 预加载游戏枚举
const GameEnums = preload("res://scenes/globals/game_enums.gd")

# 敌人属性
@export var hp: int = 3  # 生命值
@export var speed: float = 225.0  # 基础移动速度，从150增加到225
@export var horizontal_drift_speed: float = 45.0  # 水平漂移速度，从30增加到45
@export var score_value: int = 10  # 击败后获得的分数

# 移动模式
enum MovementMode {
	RANDOM_DRIFT,    # 随机漂移（默认）
	STRAIGHT_DOWN,   # 直线向下
	LEFT_DRIFT,      # 向左漂移
	RIGHT_DRIFT,     # 向右漂移
	ZIGZAG,          # 之字形移动
	SINE_WAVE        # 正弦波移动
}

@export var movement_mode: MovementMode = MovementMode.RANDOM_DRIFT
var wave_id: String = ""  # 用于标识敌人所属的波次
var sine_offset: float = 0.0  # 正弦波偏移
var zigzag_direction: float = 1.0  # 之字形移动方向
var zigzag_timer: float = 0.0  # 之字形移动计时器

# 攻击相关
@export var bullet_scene: PackedScene  # 子弹场景引用
@export var shoot_interval: float = 1.5  # 射击间隔

# 屏幕边界
var screen_width: float
var screen_height: float
const SIDEBAR_WIDTH: float = 280.0  # 侧边栏宽度

# 组件引用
@onready var triangle_outline = $TriangleOutline
@onready var shoot_timer = $ShootTimer
@onready var hit_effect_timer = $HitEffectTimer

# 漂移控制
var drift_direction: float = 0.0
var drift_change_timer: float = 0.0
var drift_change_interval: float = 1.0  # 每1秒可能改变一次漂移方向
var drift_check_interval: float = 0.2   # 漂移方向检查间隔，减少计算频率

# 信号
signal enemy_died(score)

# 性能优化 - 预先缓存玩家节点
var player_node = null

# 道具掉落相关
var last_bomb_drop_time: float = 0.0  # 上次炸弹掉落的时间
const BOMB_DROP_COOLDOWN: float = 60.0  # 炸弹掉落冷却时间（秒）
var has_dropped_powerup: bool = false  # 是否已经掉落过道具

func _ready():
	# 获取屏幕尺寸
	var viewport_rect = get_viewport_rect()
	screen_width = viewport_rect.size.x
	screen_height = viewport_rect.size.y
	
	# 初始化漂移方向
	randomize()
	drift_direction = randf_range(-1.0, 1.0)
	sine_offset = randf() * 2.0 * PI  # 随机正弦波初始相位
	
	# 预加载子弹场景（如果未在Inspector中设置）
	if bullet_scene == null:
		bullet_scene = load("res://scenes/enemies/enemy_triangle_bullet.tscn")
	
	# 设置射击计时器
	shoot_timer.wait_time = shoot_interval
	shoot_timer.start()
	
	# 缓存玩家节点引用
	player_node = get_tree().get_first_node_in_group("player")
	
	# 初始化炸弹掉落冷却时间
	last_bomb_drop_time = -BOMB_DROP_COOLDOWN  # 初始设为负值，允许立即掉落
	
	# 通知PowerupManager设置屏幕尺寸
	if PowerupManager:
		PowerupManager.set_screen_size(screen_width, screen_height, SIDEBAR_WIDTH)

func _process(delta):
	# 根据移动模式更新位置
	match movement_mode:
		MovementMode.RANDOM_DRIFT:
			move_random_drift(delta)
		MovementMode.STRAIGHT_DOWN:
			move_straight_down(delta)
		MovementMode.LEFT_DRIFT:
			move_with_constant_drift(delta, -1.0)
		MovementMode.RIGHT_DRIFT:
			move_with_constant_drift(delta, 1.0)
		MovementMode.ZIGZAG:
			move_zigzag(delta)
		MovementMode.SINE_WAVE:
			move_sine_wave(delta)
	
	# 限制在游戏区域的左右边界内（考虑侧边栏）
	position.x = clamp(position.x, SIDEBAR_WIDTH, screen_width - SIDEBAR_WIDTH)
	
	# 如果移出屏幕底部，则销毁自身
	if position.y > screen_height + 20:
		queue_free()

# 随机漂移移动
func move_random_drift(delta):
	# 垂直向下移动
	position.y += speed * delta
	
	# 水平漂移
	position.x += drift_direction * horizontal_drift_speed * delta
	
	# 更新漂移方向
	drift_change_timer += delta
	if drift_change_timer >= drift_change_interval:
		drift_change_timer = 0
		# 有20%的几率改变漂移方向
		if randf() > 0.8:
			drift_direction = randf_range(-1.0, 1.0)

# 直线向下移动
func move_straight_down(delta):
	position.y += speed * delta

# 固定方向漂移
func move_with_constant_drift(delta, drift):
	position.y += speed * delta
	position.x += drift * horizontal_drift_speed * delta

# 之字形移动
func move_zigzag(delta):
	position.y += speed * delta
	position.x += zigzag_direction * horizontal_drift_speed * 1.5 * delta
	
	zigzag_timer += delta
	if zigzag_timer >= 1.0:  # 每1秒改变一次方向
		zigzag_timer = 0
		zigzag_direction *= -1

# 正弦波移动
func move_sine_wave(delta):
	position.y += speed * delta
	
	# 计算正弦波位置
	sine_offset += delta * 2.0  # 控制正弦波频率
	var sine_value = sin(sine_offset)
	position.x += sine_value * horizontal_drift_speed * delta * 2.0

# 设置移动模式
func set_movement_mode(mode):
	movement_mode = mode

# 设置波次ID
func set_wave_id(id):
	wave_id = id

# 射击函数
func _on_shoot_timer_timeout():
	shoot()

func shoot():
	# 使用缓存的玩家引用
	if player_node and is_instance_valid(player_node):
		# 实例化子弹
		var bullet = bullet_scene.instantiate()
		get_tree().current_scene.add_child(bullet)
		
		# 设置子弹位置为敌人位置
		bullet.global_position = global_position
		
		# 计算朝向玩家的方向
		var direction = (player_node.global_position - global_position).normalized()
		bullet.direction = direction

# 受到伤害
func take_damage(damage: int):
	hp -= damage
	
	# 闪烁效果
	triangle_outline.default_color = Color(1, 1, 1)  # 变为白色
	hit_effect_timer.start()
	
	# 检查是否死亡
	if hp <= 0:
		die()

# 恢复原始颜色
func _on_hit_effect_timer_timeout():
	triangle_outline.default_color = Color(1, 0.2, 0.2)  # 恢复红色

# 死亡处理
func die():
	# 发出死亡信号，传递分数
	emit_signal("enemy_died", score_value)
	
	# 创建简化的死亡特效
	create_optimized_death_effect()
	
	# 尝试掉落道具
	try_drop_power_up()
	
	# 销毁自身
	queue_free()

# 创建优化的死亡特效
func create_optimized_death_effect():
	# 创建粒子系统，使用更少的粒子
	var particles = CPUParticles2D.new()
	get_tree().current_scene.add_child(particles)
	particles.position = global_position
	
	# 设置粒子属性 - 减少粒子数量和生命周期
	particles.amount = 8  # 减少粒子数量
	particles.lifetime = 0.4  # 减少生命周期
	particles.explosiveness = 0.9
	particles.direction = Vector2(0, 0)
	particles.spread = 180
	particles.gravity = Vector2(0, 0)
	particles.initial_velocity_min = 50
	particles.initial_velocity_max = 100
	particles.scale_amount_min = 1.0
	particles.scale_amount_max = 2.0
	particles.color = Color(1, 0.2, 0.2)  # 红色
	
	# 设置粒子为一次性
	particles.emitting = true
	particles.one_shot = true
	
	# 使用更短的自动销毁时间
	var timer = Timer.new()
	particles.add_child(timer)
	timer.wait_time = 1.0  # 减少等待时间
	timer.one_shot = true
	timer.autostart = true
	timer.timeout.connect(func(): particles.queue_free())

# 尝试掉落道具
func try_drop_power_up():
	# 检查是否已经掉落过道具
	if has_dropped_powerup:
		print("该敌人已掉落过道具，不再掉落")
		return
		
	# 使用PowerupManager生成道具
	if PowerupManager:
		if PowerupManager.try_spawn_powerup(global_position):
			print("敌人掉落了道具")
			has_dropped_powerup = true  # 标记已掉落道具
	else:
		print("警告: PowerupManager不可用")
		
# 获取伤害值（用于子弹碰撞检测）
func get_damage() -> int:
	return 1  # 敌人碰撞造成1点伤害

# 判断是否为Boss敌人
func is_boss() -> bool:
	return false  # 三角形敌人不是Boss 
 
