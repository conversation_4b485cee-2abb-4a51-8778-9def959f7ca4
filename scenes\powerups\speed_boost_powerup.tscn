[gd_scene load_steps=5 format=3 uid="uid://b8x2g5l2v6vv8"]

[ext_resource type="Script" uid="uid://nxdcr3qs0yge" path="res://scenes/powerups/base_power_up.gd" id="1_yvf0h"]

[sub_resource type="CircleShape2D" id="CircleShape2D_qc4lj"]
radius = 24.0

[sub_resource type="Animation" id="Animation_tnkv6"]
resource_name = "collect"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SpriteContainer:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(0.5, 2),
"update": 0,
"values": [Vector2(1, 1), Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SpriteContainer:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1aq8h"]
_data = {
&"collect": SubResource("Animation_tnkv6")
}

[node name="SpeedBoostPowerUp" type="Area2D" groups=["power_up", "powerups"]]
collision_layer = 16
collision_mask = 1
script = ExtResource("1_yvf0h")
power_up_type = 5
lifetime = 8.0

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_qc4lj")

[node name="SpriteContainer" type="Node2D" parent="."]

[node name="SpeedArrows" type="Node2D" parent="SpriteContainer"]

[node name="Arrow1" type="Polygon2D" parent="SpriteContainer/SpeedArrows"]
position = Vector2(-4, 0)
color = Color(0.2, 0.4, 0.8, 1)
polygon = PackedVector2Array(-12, -10, 0, -10, 0, -16, 12, 0, 0, 16, 0, 10, -12, 10)

[node name="Arrow2" type="Polygon2D" parent="SpriteContainer/SpeedArrows"]
position = Vector2(8, 0)
color = Color(0.2, 0.6, 1, 1)
polygon = PackedVector2Array(-12, -10, 0, -10, 0, -16, 12, 0, 0, 16, 0, 10, -12, 10)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_1aq8h")
}

[node name="LifetimeTimer" type="Timer" parent="."]
wait_time = 10.0
one_shot = true
autostart = true

[node name="BlinkTimer" type="Timer" parent="."]
wait_time = 0.2

[connection signal="area_entered" from="." to="." method="_on_area_entered"]
[connection signal="timeout" from="LifetimeTimer" to="." method="_on_lifetime_timer_timeout"]
[connection signal="timeout" from="BlinkTimer" to="." method="_on_blink_timer_timeout"]
