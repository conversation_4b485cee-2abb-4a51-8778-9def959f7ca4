[gd_scene load_steps=3 format=3 uid="uid://dj6fhuv4fdn2f"]

[ext_resource type="PackedScene" uid="uid://hlwjav75j4k7" path="res://scenes/powerups/base_power_up.tscn" id="1_0ykpg"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1ht0x"]
size = Vector2(48, 48)

[node name="PowerupT" instance=ExtResource("1_0ykpg")]
power_up_type = 0

[node name="CollisionShape2D" parent="." index="0"]
shape = SubResource("RectangleShape2D_1ht0x")

[node name="Background" type="ColorRect" parent="SpriteContainer" index="0"]
offset_left = -24.0
offset_top = -24.0
offset_right = 24.0
offset_bottom = 24.0
color = Color(0, 0.498039, 1, 1)

[node name="Letter" type="Label" parent="SpriteContainer" index="1"]
offset_left = -24.0
offset_top = -24.0
offset_right = 24.0
offset_bottom = 24.0
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 32
text = "T"
horizontal_alignment = 1
vertical_alignment = 1
