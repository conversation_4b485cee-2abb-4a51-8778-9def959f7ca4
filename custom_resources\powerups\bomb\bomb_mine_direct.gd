extends Node

# 这个脚本可以在游戏运行时生成水雷图标

func _ready():
	# 创建一个简单的水雷图标
	var image = Image.create(24, 24, false, Image.FORMAT_RGBA8)
	
	# 设置透明背景
	image.fill(Color(0, 0, 0, 0))
	
	# 绘制水雷主体（灰色圆形）
	draw_circle(image, Vector2(12, 12), 8, Color(0.4, 0.4, 0.4))
	
	# 绘制中心（深灰色圆形）
	draw_circle(image, Vector2(12, 12), 4, Color(0.25, 0.25, 0.25))
	
	# 绘制尖刺
	draw_spikes(image, Vector2(12, 12), 8, 8, 5, Color(0.3, 0.3, 0.3))
	
	# 保存图像到项目目录
	var save_path = "res://custom_resources/powerups/bomb/bomb_mine.png"
	var error = image.save_png(save_path)
	if error == OK:
		print("水雷图标已保存到: " + save_path)
	else:
		print("保存水雷图标失败，错误码: ", error)
		
		# 尝试保存到用户目录
		error = image.save_png("user://bomb_mine.png")
		if error == OK:
			print("水雷图标已保存到用户目录")
		else:
			print("保存到用户目录也失败，错误码: ", error)
	
	# 将图像显示在节点上
	var texture = ImageTexture.create_from_image(image)
	var sprite = Sprite2D.new()
	sprite.texture = texture
	add_child(sprite)

# 绘制圆形
func draw_circle(image: Image, center: Vector2, radius: float, color: Color):
	for x in range(max(0, center.x - radius - 1), min(image.get_width(), center.x + radius + 1)):
		for y in range(max(0, center.y - radius - 1), min(image.get_height(), center.y + radius + 1)):
			var dist = Vector2(x, y).distance_to(center)
			if dist <= radius:
				image.set_pixel(x, y, color)

# 绘制尖刺
func draw_spikes(image: Image, center: Vector2, num_spikes: int, base_radius: float, spike_length: float, color: Color):
	for i in range(num_spikes):
		var angle = i * (2 * PI / num_spikes)
		var dir = Vector2(cos(angle), sin(angle))
		var start_pos = center + dir * base_radius
		var end_pos = center + dir * (base_radius + spike_length)
		draw_line(image, start_pos, end_pos, color, 2)

# 绘制线段
func draw_line(image: Image, from: Vector2, to: Vector2, color: Color, thickness: int = 1):
	var dx = to.x - from.x
	var dy = to.y - from.y
	var steps = max(abs(dx), abs(dy))
	
	if steps == 0:
		return
	
	var x_inc = dx / steps
	var y_inc = dy / steps
	
	var x = from.x
	var y = from.y
	
	for i in range(steps + 1):
		# 绘制粗线
		for tx in range(-thickness/2, thickness/2 + 1):
			for ty in range(-thickness/2, thickness/2 + 1):
				var px = int(x + tx)
				var py = int(y + ty)
				if px >= 0 and px < image.get_width() and py >= 0 and py < image.get_height():
					image.set_pixel(px, py, color)
		
		x += x_inc
		y += y_inc 