extends Resource
class_name ModificationEffect

# 效果类型枚举
enum EffectType {
	HEALTH_BONUS,          # 生命值加成
	SHIELD_BONUS,          # 护盾值加成
	SPEED_BONUS,           # 速度加成
	DAMAGE_BONUS,          # 伤害加成
	FIRE_RATE_BONUS,       # 射速加成
	ACCURACY_BONUS,        # 精准度加成
	RELOAD_SPEED_BONUS,    # 装弹速度加成
	SPECIAL_ABILITY        # 特殊能力
}

# 改装件尺寸枚举
enum ModSize {
	SMALL,                 # 小型改装件
	MEDIUM,                # 中型改装件
	LARGE                  # 大型改装件
}

# 改装件类型枚举
enum ModType {
	ATTACK,                # 攻击型改装件
	DEFENSE,               # 防御型改装件
	SPECIAL,               # 特殊型改装件
	CORE                   # 核心型改装件
}

# 效果类型
var effect_type: int = EffectType.HEALTH_BONUS

# 效果数值
var value: float = 0.0

# 效果描述
var description: String = ""

# 初始化
func _init(p_type: int = EffectType.HEALTH_BONUS, p_value: float = 0.0, p_description: String = ""):
	effect_type = p_type
	value = p_value
	description = p_description
	
	# 如果没有自定义描述，生成默认描述
	if description.is_empty():
		description = generate_default_description()

# 生成默认描述
func generate_default_description() -> String:
	match effect_type:
		EffectType.HEALTH_BONUS:
			return "生命值 +%d" % value
		EffectType.SHIELD_BONUS:
			return "护盾值 +%d" % value
		EffectType.SPEED_BONUS:
			return "速度 +%.0f%%" % (value * 100)
		EffectType.DAMAGE_BONUS:
			return "伤害 +%.0f%%" % (value * 100)
		EffectType.FIRE_RATE_BONUS:
			return "射速 +%.0f%%" % (value * 100)
		EffectType.ACCURACY_BONUS:
			return "精准度 +%.0f%%" % (value * 100)
		EffectType.RELOAD_SPEED_BONUS:
			return "装弹速度 +%.0f%%" % (value * 100)
		EffectType.SPECIAL_ABILITY:
			return "特殊能力: %s" % str(value)
		_:
			return "未知效果"

# 获取效果描述
func get_description() -> String:
	return description

# 根据效果类型获取对应的标签
func get_effect_tag() -> String:
	match effect_type:
		EffectType.HEALTH_BONUS:
			return "effect_health"
		EffectType.SHIELD_BONUS:
			return "effect_shield"
		EffectType.SPEED_BONUS:
			return "effect_speed"
		EffectType.DAMAGE_BONUS:
			return "effect_damage"
		EffectType.FIRE_RATE_BONUS:
			return "effect_fire_rate"
		EffectType.ACCURACY_BONUS:
			return "effect_accuracy"
		EffectType.RELOAD_SPEED_BONUS:
			return "effect_reload"
		EffectType.SPECIAL_ABILITY:
			return "effect_special"
		_:
			return "effect_unknown"

# 根据尺寸获取对应的标签
static func get_size_tag(size: int) -> String:
	match size:
		ModSize.SMALL:
			return "size_small"
		ModSize.MEDIUM:
			return "size_medium"
		ModSize.LARGE:
			return "size_large"
		_:
			return "size_unknown"

# 根据类型获取对应的标签
static func get_type_tag(type: int) -> String:
	match type:
		ModType.ATTACK:
			return "type_attack"
		ModType.DEFENSE:
			return "type_defense"
		ModType.SPECIAL:
			return "type_special"
		ModType.CORE:
			return "type_core"
		_:
			return "type_unknown"

# 工厂方法，创建新的效果实例
static func create(effect_type: int, value: float, description: String = "") -> ModificationEffect:
	return ModificationEffect.new(effect_type, value, description) 