[gd_scene load_steps=2 format=3 uid="uid://c7qx8q5gu7lfr"]

[ext_resource type="Script" uid="uid://dgwgk5hraoeeu" path="res://scenes/powerups/bomb_powerup_preview.gd" id="1_t6nnw"]

[node name="BombPowerupPreview" type="Node2D"]
script = ExtResource("1_t6nnw")

[node name="SubViewport" type="SubViewport" parent="."]
transparent_bg = true
size = Vector2i(32, 32)

[node name="SeaMine" type="Node2D" parent="SubViewport"]
position = Vector2(16, 16)

[node name="MainBody" type="Polygon2D" parent="SubViewport/SeaMine"]
color = Color(0.4, 0.4, 0.4, 1)
polygon = PackedVector2Array(0, -10, 7, -7, 10, 0, 7, 7, 0, 10, -7, 7, -10, 0, -7, -7)

[node name="Center" type="Polygon2D" parent="SubViewport/SeaMine"]
color = Color(0.2, 0.2, 0.2, 1)
polygon = PackedVector2Array(0, -5, 3.5, -3.5, 5, 0, 3.5, 3.5, 0, 5, -3.5, 3.5, -5, 0, -3.5, -3.5)

[node name="Spike1" type="Polygon2D" parent="SubViewport/SeaMine"]
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -10, 2, -13, 0, -16, -2, -13)

[node name="Spike2" type="Polygon2D" parent="SubViewport/SeaMine"]
rotation = 0.785398
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -10, 2, -13, 0, -16, -2, -13)

[node name="Spike3" type="Polygon2D" parent="SubViewport/SeaMine"]
rotation = 1.5708
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -10, 2, -13, 0, -16, -2, -13)

[node name="Spike4" type="Polygon2D" parent="SubViewport/SeaMine"]
rotation = 2.35619
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -10, 2, -13, 0, -16, -2, -13)

[node name="Spike5" type="Polygon2D" parent="SubViewport/SeaMine"]
rotation = 3.14159
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -10, 2, -13, 0, -16, -2, -13)

[node name="Spike6" type="Polygon2D" parent="SubViewport/SeaMine"]
rotation = 3.92699
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -10, 2, -13, 0, -16, -2, -13)

[node name="Spike7" type="Polygon2D" parent="SubViewport/SeaMine"]
rotation = 4.71239
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -10, 2, -13, 0, -16, -2, -13)

[node name="Spike8" type="Polygon2D" parent="SubViewport/SeaMine"]
rotation = 5.49779
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -10, 2, -13, 0, -16, -2, -13)

[node name="Camera2D" type="Camera2D" parent="SubViewport"]
position = Vector2(16, 16)
