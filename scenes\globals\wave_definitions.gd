extends RefCounted

# 波次定义
# 包含游戏中所有敌人波次的定义数据

# 波次类型枚举
enum WaveType {
	CONTINUOUS_SPAWN,  # 持续生成敌人
	SCRIPTED_WAVE      # 脚本化波次（按预定义的时间点生成敌人）
}

# 触发类型枚举
enum TriggerType {
	TIME,           # 基于游戏时间触发
	DISTANCE,       # 基于行进距离触发
	SPECIAL_RANDOM  # 特殊随机触发（用于随机波次系统）
}

# 敌人类型枚举
enum EnemyType {
	BASIC_TRIANGLE  # 基础三角形敌人
}

# 敌人移动模式枚举 - 与敌人脚本中的枚举保持一致
enum EnemyMovementMode {
	RANDOM_DRIFT,    # 随机漂移（默认）
	STRAIGHT_DOWN,   # 直线向下
	LEFT_DRIFT,      # 向左漂移
	RIGHT_DRIFT,     # 向右漂移
	ZIGZAG,          # 之字形移动
	SINE_WAVE        # 正弦波移动
}

# 敌人生成位置逻辑枚举
enum SpawnPositionLogic {
	RANDOM_TOP,  # 顶部随机位置
	CENTER_TOP,  # 顶部中央
	LEFT_TOP,    # 顶部左侧
	RIGHT_TOP,   # 顶部右侧
	
	# 特殊位置
	LEFT_CORNER,  # 左上角
	RIGHT_CORNER, # 右上角
	
	# 倒三角形阵列位置
	INV_TOP,      # 倒三角顶部
	INV_LEFT_1,   # 倒三角左侧第一个
	INV_RIGHT_1,  # 倒三角右侧第一个
	INV_LEFT_2,   # 倒三角左侧第二个
	INV_MID_2,    # 倒三角中间第二个
	INV_RIGHT_2,  # 倒三角右侧第二个
	INV_LEFT_3,   # 倒三角左侧第三个
	INV_MID_LEFT_3, # 倒三角中间偏左第三个
	INV_MID_RIGHT_3, # 倒三角中间偏右第三个
	INV_RIGHT_3,  # 倒三角右侧第三个
	
	# 直线阵列位置
	LINE_1,       # 直线第1个
	LINE_2,       # 直线第2个
	LINE_3,       # 直线第3个
	LINE_4,       # 直线第4个
	LINE_5,       # 直线第5个
	LINE_6,       # 直线第6个
	LINE_7,       # 直线第7个
	LINE_8,       # 直线第8个
}

# 获取基于时间或距离触发的波次
func get_waves_by_trigger(trigger_type, trigger_value):
	var matching_waves = []
	var all_waves = get_all_waves()
	
	for wave in all_waves:
		# 跳过不匹配触发类型的波次
		if wave["trigger_type"] != trigger_type:
			continue
		
		# 检查触发值是否匹配
		if abs(wave["trigger_value"] - trigger_value) <= 0.5:  # 允许0.5的误差
			matching_waves.append(wave)
	
	return matching_waves

# 获取所有特殊随机波次
func get_special_random_waves():
	var special_waves = []
	var all_waves = get_all_waves()
	
	for wave in all_waves:
		if wave["trigger_type"] == TriggerType.SPECIAL_RANDOM:
			special_waves.append(wave)
	
	return special_waves

# 获取所有波次定义
func get_all_waves():
	var waves = []
	
	# 添加基于时间的波次
	waves.append_array(get_time_based_waves())
	
	# 添加特殊随机波次
	waves.append_array(get_special_random_waves_definitions())
	
	return waves

# 基于时间的波次定义
func get_time_based_waves():
	var waves = []
	
	# 游戏开始时的基础波次 - 单个敌人，间隔2秒，永久持续
	waves.append({
		"name": "basic_continuous_wave",  # 添加名称以便识别
		"wave_type": WaveType.CONTINUOUS_SPAWN,
		"trigger_type": TriggerType.TIME,
		"trigger_value": 0,
		"duration": 999999.0,  # 实质上是永久持续
		"base_interval": 2.0,  # 每2秒生成一个敌人
		"enemy_type": EnemyType.BASIC_TRIANGLE,
		"spawn_position_logic": SpawnPositionLogic.RANDOM_TOP,
		"movement_mode": EnemyMovementMode.RANDOM_DRIFT  # 默认移动模式
	})
	
	# 40秒后的第一次特殊波次 - 倒三角形阵列
	waves.append({
		"name": "first_special_inverted_triangle",  # 添加名称以便识别
		"wave_type": WaveType.SCRIPTED_WAVE,
		"trigger_type": TriggerType.TIME,
		"trigger_value": 40,
		"movement_mode": EnemyMovementMode.STRAIGHT_DOWN,  # 整个波次使用同一移动模式
		"events": [
			# 倒三角形顶部
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_TOP
			},
			# 倒三角形第二行
			{
				"delay": 0.0,  # 同时出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_LEFT_1
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_RIGHT_1
			},
			# 倒三角形第三行
			{
				"delay": 0.0,  # 同时出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_LEFT_2
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_MID_2
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_RIGHT_2
			},
			# 倒三角形第四行
			{
				"delay": 0.0,  # 同时出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_LEFT_3
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_MID_LEFT_3
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_MID_RIGHT_3
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_RIGHT_3
			},
			# 左上角和右上角第1组（同时出现）
			{
				"delay": 0.5,  # 稍后出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LEFT_CORNER,
				"movement_mode": EnemyMovementMode.RIGHT_DRIFT  # 单独设置移动模式
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.RIGHT_CORNER,
				"movement_mode": EnemyMovementMode.LEFT_DRIFT
			},
			# 左上角和右上角第2组（同时出现）
			{
				"delay": 0.5,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LEFT_CORNER,
				"movement_mode": EnemyMovementMode.RIGHT_DRIFT
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.RIGHT_CORNER,
				"movement_mode": EnemyMovementMode.LEFT_DRIFT
			}
		]
	})
	
	return waves

# 特殊随机波次定义
func get_special_random_waves_definitions():
	var waves = []
	
	# 特殊波次1 - 倒三角形阵列（12只）
	waves.append({
		"name": "special_inverted_triangle",  # 添加名称以便识别
		"wave_type": WaveType.SCRIPTED_WAVE,
		"trigger_type": TriggerType.SPECIAL_RANDOM,
		"trigger_value": 0,  # 对于特殊随机波次，触发值不重要
		"movement_mode": EnemyMovementMode.STRAIGHT_DOWN,  # 整个波次使用同一移动模式
		"events": [
			# 倒三角形顶部
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_TOP
			},
			# 倒三角形第二行
			{
				"delay": 0.0,  # 同时出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_LEFT_1
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_RIGHT_1
			},
			# 倒三角形第三行
			{
				"delay": 0.0,  # 同时出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_LEFT_2
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_MID_2
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_RIGHT_2
			},
			# 倒三角形第四行
			{
				"delay": 0.0,  # 同时出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_LEFT_3
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_MID_LEFT_3
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_MID_RIGHT_3
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.INV_RIGHT_3
			},
			# 左上角和右上角各1只 - 第1组
			{
				"delay": 0.5,  # 稍后出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LEFT_CORNER,
				"movement_mode": EnemyMovementMode.RIGHT_DRIFT  # 单独设置移动模式
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.RIGHT_CORNER,
				"movement_mode": EnemyMovementMode.LEFT_DRIFT
			}
		]
	})
	
	# 特殊波次2 - 直线阵列（8只）
	waves.append({
		"name": "special_line_formation",  # 添加名称以便识别
		"wave_type": WaveType.SCRIPTED_WAVE,
		"trigger_type": TriggerType.SPECIAL_RANDOM,
		"trigger_value": 0,  # 对于特殊随机波次，触发值不重要
		"movement_mode": EnemyMovementMode.SINE_WAVE,  # 整个波次使用同一移动模式
		"events": [
			# 直线阵列（8只敌人，同时出现）
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LINE_1
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LINE_2
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LINE_3
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LINE_4
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LINE_5
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LINE_6
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LINE_7
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LINE_8
			},
			# 左上角和右上角第1组（同时出现）
			{
				"delay": 0.5,  # 稍后出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LEFT_CORNER,
				"movement_mode": EnemyMovementMode.RIGHT_DRIFT
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.RIGHT_CORNER,
				"movement_mode": EnemyMovementMode.LEFT_DRIFT
			},
			# 左上角和右上角第2组（同时出现）
			{
				"delay": 0.5,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LEFT_CORNER,
				"movement_mode": EnemyMovementMode.RIGHT_DRIFT
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.RIGHT_CORNER,
				"movement_mode": EnemyMovementMode.LEFT_DRIFT
			}
		]
	})
	
	# 特殊波次3 - 左上角和右上角各4只敌人
	waves.append({
		"name": "special_corner_attack",  # 添加名称以便识别
		"wave_type": WaveType.SCRIPTED_WAVE,
		"trigger_type": TriggerType.SPECIAL_RANDOM,
		"trigger_value": 0,  # 对于特殊随机波次，触发值不重要
		"events": [
			# 左上角和右上角第1只（同时出现）
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LEFT_CORNER,
				"movement_mode": EnemyMovementMode.ZIGZAG
			},
			{
				"delay": 0.0,
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.RIGHT_CORNER,
				"movement_mode": EnemyMovementMode.ZIGZAG
			},
			# 左上角和右上角第2只（同时出现）
			{
				"delay": 0.5,  # 0.5秒后出现第2只
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LEFT_CORNER,
				"movement_mode": EnemyMovementMode.ZIGZAG
			},
			{
				"delay": 0.0,  # 同时出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.RIGHT_CORNER,
				"movement_mode": EnemyMovementMode.ZIGZAG
			},
			# 左上角和右上角第3只（同时出现）
			{
				"delay": 0.5,  # 0.5秒后出现第3只
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LEFT_CORNER,
				"movement_mode": EnemyMovementMode.ZIGZAG
			},
			{
				"delay": 0.0,  # 同时出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.RIGHT_CORNER,
				"movement_mode": EnemyMovementMode.ZIGZAG
			},
			# 左上角和右上角第4只（同时出现）
			{
				"delay": 0.5,  # 0.5秒后出现第4只
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.LEFT_CORNER,
				"movement_mode": EnemyMovementMode.ZIGZAG
			},
			{
				"delay": 0.0,  # 同时出现
				"enemy_type": EnemyType.BASIC_TRIANGLE,
				"spawn_position_logic": SpawnPositionLogic.RIGHT_CORNER,
				"movement_mode": EnemyMovementMode.ZIGZAG
			}
		]
	})
	
	return waves 