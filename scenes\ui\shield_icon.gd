@tool
extends TextureRect

# 护盾图标绘制脚本 - 扁平圆润风格盾设计

# 闪烁效果变量
var is_flashing = false
var flash_time = 0.0
var flash_duration = 0.2

# 基础颜色
var base_color = Color(0.2, 0.5, 0.9, 1.0)
var outline_color = Color(0.4, 0.7, 1.0, 1.0)
var flash_color = Color(1.0, 1.0, 1.0, 1.0)
var background_color = Color(0.1, 0.12, 0.18, 1.0)

# 绘制参数
var scale_factor = 0.45  # 图标缩放因子

func _ready():
	# 确保工具模式下不连接信号
	if not Engine.is_editor_hint():
		# 尝试查找玩家节点并连接信号
		call_deferred("connect_to_player")

func connect_to_player():
	# 尝试查找玩家节点
	var player = get_tree().get_first_node_in_group("player")
	if player and player.has_signal("shield_changed"):
		# 使用SignalUtils安全连接护盾变化信号
		if Engine.get_singleton("SignalUtils"):
			var signal_utils = Engine.get_singleton("SignalUtils")
			if signal_utils and signal_utils.has_method("safe_connect"):
				signal_utils.safe_connect(player, "shield_changed", _on_player_shield_changed)
			else:
				# 如果无法访问SignalUtils单例，使用常规连接方法
				if not player.is_connected("shield_changed", _on_player_shield_changed):
					player.connect("shield_changed", _on_player_shield_changed)
		else:
			# 备用方法
			if not player.is_connected("shield_changed", _on_player_shield_changed):
				player.connect("shield_changed", _on_player_shield_changed)

# 当玩家护盾变化时
func _on_player_shield_changed(new_shield, max_shield):
	# 如果护盾减少，播放闪烁效果
	if new_shield < max_shield:
		play_flash()

# 播放闪烁效果
func play_flash():
	is_flashing = true
	flash_time = 0.0
	queue_redraw()

func _process(delta):
	# 处理闪烁效果
	if is_flashing:
		flash_time += delta
		if flash_time >= flash_duration:
			is_flashing = false
		queue_redraw()

# 获取绘制区域中心点和缩放比例
func get_draw_params():
	var rect = get_rect()
	var center_x = rect.size.x / 2
	var center_y = rect.size.y / 2
	var scale = min(rect.size.x, rect.size.y) * scale_factor
	
	return {
		"rect": rect,
		"center_x": center_x,
		"center_y": center_y,
		"scale": scale
	}

# 绘制背景
func draw_background():
	draw_rect(get_rect(), background_color, true)

func _draw():
	# 绘制背景
	draw_background()
	
	# 获取绘制参数
	var params = get_draw_params()
	var center_x = params.center_x
	var center_y = params.center_y
	var scale = params.scale
	
	# 盾牌参数 - 更扁平的形状
	var shield_width = scale * 1.8  # 增加宽度
	var shield_height = scale * 1.6  # 减少高度
	var top_radius = shield_width / 2
	var bottom_radius = shield_width / 4  # 底部也做成圆形
	
	# 创建盾牌形状的多边形点
	var shield_points = PackedVector2Array()
	
	# 顶部半圆弧的点
	var arc_steps = 16  # 增加点数使曲线更平滑
	for i in range(arc_steps + 1):
		var angle = PI + (i / float(arc_steps)) * PI
		var x = center_x + cos(angle) * top_radius
		var y = center_y - shield_height * 0.25 + sin(angle) * (top_radius * 0.8)  # 压扁顶部圆弧
		shield_points.append(Vector2(x, y))
	
	# 添加底部圆弧而不是尖端
	for i in range(arc_steps + 1):
		var angle = 0 + (i / float(arc_steps)) * PI
		var x = center_x + cos(angle) * bottom_radius
		var y = center_y + shield_height * 0.4 + sin(angle) * (bottom_radius * 0.8)  # 底部圆弧
		shield_points.append(Vector2(x, y))
	
	# 确定当前颜色（闪烁或正常）
	var shield_color = flash_color if is_flashing else base_color
	var current_outline_color = flash_color if is_flashing else outline_color
	
	# 填充盾牌主体
	draw_colored_polygon(shield_points, shield_color)
	
	# 绘制盾牌边缘
	for i in range(shield_points.size() - 1):
		draw_line(shield_points[i], shield_points[i+1], current_outline_color, 1.5)
	# 连接最后一点和第一点
	draw_line(shield_points[shield_points.size() - 1], shield_points[0], current_outline_color, 1.5)
	
	# 如果不在闪烁状态，添加高光效果
	if not is_flashing:
		# 添加高光效果
		var highlight_points = PackedVector2Array([
			Vector2(center_x - shield_width * 0.3, center_y - shield_height * 0.15),
			Vector2(center_x, center_y - shield_height * 0.2),
			Vector2(center_x + shield_width * 0.3, center_y - shield_height * 0.15)
		])
		
		draw_colored_polygon(highlight_points, Color(0.6, 0.8, 1.0, 0.2))

func _notification(what):
	if what == NOTIFICATION_RESIZED:
		queue_redraw() 
