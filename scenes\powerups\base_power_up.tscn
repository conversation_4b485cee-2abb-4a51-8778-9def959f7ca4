[gd_scene load_steps=5 format=3 uid="uid://hlwjav75j4k7"]

[ext_resource type="Script" uid="uid://nxdcr3qs0yge" path="res://scenes/powerups/base_power_up.gd" id="1_1a5uq"]

[sub_resource type="Animation" id="Animation_8yrjt"]
resource_name = "appear"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SpriteContainer:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(0.5, 1),
"update": 0,
"values": [Vector2(0, 0), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SpriteContainer:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_xm6xt"]
resource_name = "collect"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SpriteContainer:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 0.5),
"transitions": PackedFloat32Array(0.5, 2, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.5, 1.5), Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SpriteContainer:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2, 0.5),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_aqkv7"]
_data = {
&"appear": SubResource("Animation_8yrjt"),
&"collect": SubResource("Animation_xm6xt")
}

[node name="BasePowerUp" type="Area2D" groups=["powerups"]]
collision_layer = 16
collision_mask = 1
script = ExtResource("1_1a5uq")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
scale = Vector2(0.7, 0.7)

[node name="SpriteContainer" type="Node2D" parent="."]
scale = Vector2(0.7, 0.7)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_aqkv7")
}

[node name="LifetimeTimer" type="Timer" parent="."]
one_shot = true

[node name="BlinkTimer" type="Timer" parent="."]
wait_time = 0.2
one_shot = true

[connection signal="area_entered" from="." to="." method="_on_area_entered"]
[connection signal="timeout" from="BlinkTimer" to="." method="_on_blink_timer_timeout"]
[connection signal="timeout" from="LifetimeTimer" to="." method="_on_lifetime_timer_timeout"]
