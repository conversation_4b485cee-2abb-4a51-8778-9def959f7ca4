[gd_scene load_steps=5 format=3 uid="uid://b52g0s04nh5vo"]

[ext_resource type="Script" path="res://scenes/modifications/ui/mod_inventory_item_entry.gd" id="1_s4jmw"]
[ext_resource type="Theme" uid="uid://dd8xdg3twycsx" path="res://themes/mod_ui_theme.tres" id="2_mjinf"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_wv0hu"]
bg_color = Color(0.15, 0.15, 0.15, 0.8)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xprx8"]
bg_color = Color(0.25, 0.25, 0.25, 0.9)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="ModInventoryItemEntry" type="Panel"]
custom_minimum_size = Vector2(120, 140)
theme_override_styles/panel = SubResource("StyleBoxFlat_wv0hu")
script = ExtResource("1_s4jmw")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="IconContainer" type="MarginContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
mouse_filter = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 5

[node name="IconRect" type="ColorRect" parent="VBoxContainer/IconContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
mouse_filter = 2
color = Color(0.4, 0.8, 1, 1)

[node name="SizeLabel" type="Label" parent="VBoxContainer/IconContainer"]
layout_mode = 2
size_flags_horizontal = 8
size_flags_vertical = 0
theme = ExtResource("2_mjinf")
theme_override_colors/font_color = Color(1, 1, 1, 0.8)
theme_override_font_sizes/font_size = 16
text = "S"

[node name="NameLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme = ExtResource("2_mjinf")
theme_override_font_sizes/font_size = 14
text = "改装件名称"
horizontal_alignment = 1
text_overrun_behavior = 3

[node name="StatsContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
mouse_filter = 2

[node name="LevelLabel" type="Label" parent="VBoxContainer/StatsContainer"]
layout_mode = 2
theme = ExtResource("2_mjinf")
theme_override_font_sizes/font_size = 12
text = "Lv.1"

[node name="Spacer" type="Control" parent="VBoxContainer/StatsContainer"]
layout_mode = 2
size_flags_horizontal = 3
mouse_filter = 2

[node name="QuantityLabel" type="Label" parent="VBoxContainer/StatsContainer"]
layout_mode = 2
theme = ExtResource("2_mjinf")
theme_override_font_sizes/font_size = 12
text = "x1"
horizontal_alignment = 2

[node name="HoverStyle" type="StyleBoxFlat" parent="."]
resource_local_to_scene = true
bg_color = Color(0.25, 0.25, 0.25, 0.9)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="SelectedStyle" type="StyleBoxFlat" parent="."]
resource_local_to_scene = true
bg_color = Color(0.3, 0.5, 0.7, 0.9)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5 