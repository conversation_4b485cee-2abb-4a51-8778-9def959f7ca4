[gd_scene load_steps=4 format=3 uid="uid://4nhh0hmyuoey"]

[ext_resource type="Script" uid="uid://4ebbs85bhn28" path="res://scenes/ui/tooltip.gd" id="1_yk5ew"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_u8rkx"]
bg_color = Color(0.12, 0.12, 0.12, 0.9)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.8, 0.8, 0.8, 0.3)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
shadow_color = Color(0, 0, 0, 0.3)
shadow_size = 4

[sub_resource type="LabelSettings" id="LabelSettings_3n5xw"]
font_size = 14
font_color = Color(1, 0.9, 0.7, 1)
outline_size = 1
outline_color = Color(0, 0, 0, 0.2)
shadow_color = Color(0, 0, 0, 0.2)

[node name="Tooltip" type="Control"]
z_index = 1000
layout_mode = 3
anchors_preset = 0
script = ExtResource("1_yk5ew")

[node name="CanvasLayer" type="CanvasLayer" parent="."]
layer = 100

[node name="Panel" type="Panel" parent="CanvasLayer"]
offset_right = 200.0
offset_bottom = 80.0
theme_override_styles/panel = SubResource("StyleBoxFlat_u8rkx")

[node name="MarginContainer" type="MarginContainer" parent="CanvasLayer/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 8
theme_override_constants/margin_top = 8
theme_override_constants/margin_right = 8
theme_override_constants/margin_bottom = 8

[node name="VBoxContainer" type="VBoxContainer" parent="CanvasLayer/Panel/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 6

[node name="TitleLabel" type="Label" parent="CanvasLayer/Panel/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "装备名称"
label_settings = SubResource("LabelSettings_3n5xw")

[node name="HSeparator" type="HSeparator" parent="CanvasLayer/Panel/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="DescriptionLabel" type="Label" parent="CanvasLayer/Panel/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "装备描述文本"
autowrap_mode = 3
