# 几何射击 - 改装系统

这个文件夹包含了游戏的改装系统，用于管理玩家可以装备的各种改装配件。

## 文件夹结构

- `data/`: 包含配件的JSON数据文件
- `resources/`: 包含配件的资源文件（.tres）
- `scripts/`: 包含改装系统的脚本文件

## 主要组件

1. **ModificationBase**: 改装配件基类，定义了所有配件的基本属性和方法
2. **ModificationManager**: 管理改装配件的应用和移除
3. **ModificationFactory**: 用于创建和管理改装配件资源
4. **ModificationLoader**: 从文件系统加载配件数据
5. **ModificationVisual**: 改装配件的可视化组件
6. **ModificationVisualsManager**: 管理玩家身上的配件可视化
7. **ModificationSystem**: 改装系统的总入口，将新系统与现有系统集成

## 如何使用

### 1. 添加到玩家

将 `ModificationSystem` 添加到玩家节点：

```gdscript
# 在玩家脚本中
var mod_system = preload("res://scenes/modifications/scripts/modification_system.gd").new()
mod_system.name = "ModificationSystem"
add_child(mod_system)
```

### 2. 创建新的改装配件

有两种方式创建新的改装配件：

#### 方式1: 使用JSON数据文件

在 `data/` 目录下创建一个JSON文件，例如 `mod_example.json`：

```json
{
  "id": "mod_example",
  "name": "示例配件",
  "description": "这是一个示例配件，用于演示配件系统",
  "color": {
    "r": 0.8,
    "g": 0.2,
    "b": 0.8,
    "a": 1.0
  },
  "shield_bonus": 1,
  "health_bonus": 0,
  "speed_bonus": 0.0,
  "damage_bonus": 0.0,
  "effect_tags": ["shield_cap_increase", "example"]
}
```

#### 方式2: 使用代码创建

```gdscript
# 创建一个新的护盾增强配件
var mod = ModificationFactory.create_shield_mod(
    "mod_custom",
    "自定义护盾增强",
    "增加1点护盾上限",
    Color(0.5, 0.5, 1.0),
    1
)

# 保存配件
ModificationFactory.save_modification_to_file(mod)
```

### 3. 配件效果

目前支持的配件效果有：

- `shield_bonus`: 护盾上限加成
- `health_bonus`: 生命上限加成
- `speed_bonus`: 速度加成
- `damage_bonus`: 伤害加成

### 4. 自定义效果

可以通过 `effect_tags` 添加自定义效果标签，然后在游戏中检测这些标签：

```gdscript
# 检查配件是否有特定效果
func has_effect(mod_id, effect_tag):
    var mod = modification_manager.get_modification(mod_id)
    if mod and mod.effect_tags.has(effect_tag):
        return true
    return false
```

## 与旧系统的兼容性

新系统与旧系统保持兼容，可以无缝替换。`ModificationSystem` 会自动处理格式转换。 