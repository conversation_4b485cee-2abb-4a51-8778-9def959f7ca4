extends Node

# 输入映射设置脚本
# 用于定义和设置游戏输入映射

func _ready():
	# 确保输入映射已设置
	setup_input_mappings()

# 设置输入映射
func setup_input_mappings():
	# 如果已有映射，则不重复添加
	if InputMap.has_action("ui_b"):
		return
	
	# 添加"B"键输入动作，用于打开/关闭改装界面
	InputMap.add_action("ui_b")
	
	# 创建B键事件
	var b_event = InputEventKey.new()
	b_event.keycode = KEY_B
	
	# 将B键添加到ui_b动作
	InputMap.action_add_event("ui_b", b_event)
	
	print("输入映射已设置完成") 