@tool
extends TextureRect

# H字图标绘制脚本

func _draw():
	# 绘制白色背景
	var rect = Rect2(0, 0, size.x, size.y)
	draw_rect(rect, Color(1, 1, 1), true)
	
	# 绘制红色H字
	var margin = size.x * 0.15
	var stroke_width = size.x * 0.2
	
	# 绘制H字的左竖线
	var left_line = Rect2(margin, margin, stroke_width, size.y - 2 * margin)
	draw_rect(left_line, Color(1, 0, 0), true)
	
	# 绘制H字的右竖线
	var right_line = Rect2(size.x - margin - stroke_width, margin, stroke_width, size.y - 2 * margin)
	draw_rect(right_line, Color(1, 0, 0), true)
	
	# 绘制H字的横线
	var middle_line = Rect2(margin, (size.y - stroke_width) / 2, size.x - 2 * margin, stroke_width)
	draw_rect(middle_line, Color(1, 0, 0), true)

func _notification(what):
	if what == NOTIFICATION_RESIZED:
		queue_redraw() 