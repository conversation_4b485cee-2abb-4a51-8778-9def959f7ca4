extends Node2D
class_name ModVisualManager

# 改装件视觉管理器
# 负责管理玩家飞船上改装件的视觉表现

# 信号
signal mod_attached(mod_id, position)  # 改装件被装备
signal mod_detached(mod_id, position)  # 改装件被卸下

# 常量
const ATTACH_ANIM_DURATION = 0.2  # 装备动画持续时间
const DETACH_ANIM_DURATION = 0.15  # 卸下动画持续时间

# 预加载资源
var mod_visual_scene = preload("res://scenes/modifications/visuals/mod_visual.tscn")

# 父节点（玩家飞船）
var parent_ship = null

# 当前可见的改装件视觉节点 {mod_id+position: node}
var visible_mods = {}

# 音效资源 - 使用内置音效或添加延迟加载
var attach_sound = null
var detach_sound = null

# 初始化
func _ready():
	parent_ship = get_parent()
	
	# 验证父节点是否为飞船
	if not parent_ship.is_in_group("player"):
		push_warning("ModVisualManager: 父节点不是玩家飞船，视觉效果可能无法正确显示")
	
	# 尝试加载音效
	_load_sound_resources()

# 加载音效资源
func _load_sound_resources():
	# 彻底禁用音频加载，避免产生错误
	print("ModVisualManager: 音频加载已禁用")
	
	# 将音频变量设为null
	attach_sound = null
	detach_sound = null

# 装备改装件（添加视觉效果）
func attach_mod(mod_resource: ModificationResource, grid_position: Vector2) -> void:
	# 创建唯一ID，用于跟踪该位置的改装件
	var unique_id = _create_unique_id(mod_resource.id, grid_position)
	
	# 如果该位置已有改装件，先移除
	if visible_mods.has(unique_id):
		detach_mod(mod_resource.id, grid_position)
	
	# 打印详细调试信息
	print("正在装备改装件: ", mod_resource.name)
	print("- ID: ", mod_resource.id)
	print("- 颜色: ", mod_resource.color)
	print("- 网格位置: ", grid_position)
	
	# 创建视觉节点
	var visual_node = _create_visual_node(mod_resource, grid_position)
	
	# 添加到场景树
	parent_ship.add_child(visual_node)
	
	# 记录到可见改装件字典
	visible_mods[unique_id] = visual_node
	
	# 播放装备动画
	_play_attach_animation(visual_node)
	
	# 播放音效
	_play_sound(attach_sound)
	
	# 发送信号
	emit_signal("mod_attached", mod_resource.id, grid_position)
	
	print("ModVisualManager: 已装备改装件 " + mod_resource.name + " 到位置 " + str(grid_position))
	print("- 节点全局位置: " + str(visual_node.global_position))
	print("- 节点局部位置: " + str(visual_node.position))

# 卸下改装件（移除视觉效果）
func detach_mod(mod_id: String, grid_position: Vector2) -> void:
	# 创建唯一ID
	var unique_id = _create_unique_id(mod_id, grid_position)
	
	# 检查是否存在该改装件
	if not visible_mods.has(unique_id):
		push_warning("ModVisualManager: 尝试卸下不存在的改装件: " + mod_id + " 位置: " + str(grid_position))
		return
	
	# 获取视觉节点
	var visual_node = visible_mods[unique_id]
	
	# 播放卸下动画
	_play_detach_animation(visual_node)
	
	# 从字典中移除
	visible_mods.erase(unique_id)
	
	# 播放音效
	_play_sound(detach_sound)
	
	# 发送信号
	emit_signal("mod_detached", mod_id, grid_position)
	
	print("ModVisualManager: 已卸下改装件，位置: " + str(grid_position))

# 创建视觉节点
func _create_visual_node(mod_resource: ModificationResource, grid_position: Vector2) -> Node2D:
	# 实例化视觉节点场景
	var node = mod_visual_scene.instantiate()
	
	# 设置节点属性
	node.mod_resource = mod_resource
	node.grid_position = grid_position
	
	# 计算实际位置
	# 使用更小的单元格大小，更适合玩家实际大小
	var cell_size = Vector2(32, 32)  # 调小单元格大小
	
	# 玩家中心位置是(0,0)，配件应该相对于玩家中心定位
	# 定义网格中心点(玩家位置)
	var center_grid = Vector2(5, 4)  # 网格中心点
	
	# 计算相对于中心的偏移
	var relative_grid_pos = grid_position - center_grid
	
	# 计算实际位置，直接相对于父节点(玩家)的中心点定位
	node.position = relative_grid_pos * cell_size
	
	# 调整Z索引确保视觉顺序正确
	node.z_index = 5  # 确保在玩家上方显示
	
	# 打印调试信息
	print("创建改装件视觉节点 - 网格位置:", grid_position, 
		"相对位置:", relative_grid_pos, 
		"实际位置:", node.position)
	
	return node

# 播放装备动画
func _play_attach_animation(visual_node: Node2D) -> void:
	# 设置初始状态
	visual_node.scale = Vector2(0.1, 0.1)  # 从小尺寸开始
	visual_node.modulate.a = 0.0  # 从完全透明开始
	
	# 创建补间动画
	var tween = create_tween()
	tween.set_parallel(true)  # 并行执行所有属性动画
	
	# 尺寸动画
	tween.tween_property(visual_node, "scale", Vector2(1.0, 1.0), ATTACH_ANIM_DURATION) \
		.set_ease(Tween.EASE_OUT) \
		.set_trans(Tween.TRANS_BACK)  # 弹性效果
	
	# 透明度动画
	tween.tween_property(visual_node, "modulate:a", 1.0, ATTACH_ANIM_DURATION) \
		.set_ease(Tween.EASE_OUT) \
		.set_trans(Tween.TRANS_SINE)

# 播放卸下动画
func _play_detach_animation(visual_node: Node2D) -> void:
	# 创建补间动画
	var tween = create_tween()
	tween.set_parallel(true)  # 并行执行所有属性动画
	
	# 尺寸动画
	tween.tween_property(visual_node, "scale", Vector2(0.1, 0.1), DETACH_ANIM_DURATION) \
		.set_ease(Tween.EASE_IN) \
		.set_trans(Tween.TRANS_BACK)  # 弹性效果
	
	# 透明度动画
	tween.tween_property(visual_node, "modulate:a", 0.0, DETACH_ANIM_DURATION) \
		.set_ease(Tween.EASE_IN) \
		.set_trans(Tween.TRANS_SINE)
	
	# 动画完成后销毁节点
	tween.tween_callback(visual_node.queue_free)

# 播放音效
func _play_sound(sound_resource: Resource) -> void:
	# 已禁用音频功能，不执行任何操作
	return

# 创建唯一ID
func _create_unique_id(mod_id: String, position: Vector2) -> String:
	return mod_id + "_" + str(position.x) + "_" + str(position.y)

# 清除所有视觉效果
func clear_all_visuals() -> void:
	for unique_id in visible_mods.keys():
		var visual_node = visible_mods[unique_id]
		visual_node.queue_free()
	
	visible_mods.clear()
	print("ModVisualManager: 已清除所有视觉效果") 
