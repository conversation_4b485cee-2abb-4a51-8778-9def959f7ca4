extends Resource
class_name ModificationResource

# 改装配件资源
# 存储改装配件的基本数据和效果

# 基本属性
var id: String = ""
var name: String = ""
var description: String = ""
var color: Color = Color(0.5, 0.5, 0.5)  # 默认灰色
var icon_path: String = ""

# 尺寸和形状属性
enum SizeCategory { SMALL, MEDIUM, LARGE }
var size_category: int = SizeCategory.SMALL  # 默认为小型
var shape_data: Array = []  # 存储形状数据，格式: [(0,0), (1,0), ...] 相对于主锚点的偏移

# 效果属性（向后兼容）
var shield_bonus: int = 0
var health_bonus: int = 0
var speed_bonus: float = 0.0
var damage_bonus: float = 0.0

# 效果标签
var effect_tags: Array = []

# 新系统 - 效果列表
var effects: Array = []

# 初始化
func _init(p_id: String = "", p_name: String = "", p_description: String = "", p_color: Color = Color(0.5, 0.5, 0.5)):
	id = p_id
	name = p_name
	description = p_description
	color = p_color

# 添加效果
func add_effect(effect) -> void:
	effects.append(effect)

# 获取所有效果
func get_effects() -> Array:
	return effects

# 获取效果描述文本
func get_effects_description() -> String:
	var desc = ""
	
	if shield_bonus > 0:
		desc += "护盾 +%d\n" % shield_bonus
		
	if health_bonus > 0:
		desc += "生命 +%d\n" % health_bonus
		
	if speed_bonus > 0:
		desc += "速度 +%.0f%%\n" % (speed_bonus * 100)
		
	if damage_bonus > 0:
		desc += "伤害 +%.0f%%\n" % (damage_bonus * 100)
	
	# 添加自定义效果描述
	for effect in effects:
		if effect.has_method("get_description"):
			desc += effect.get_description() + "\n"
	
	return desc.strip_edges()

# 添加效果标签
func add_effect_tag(tag: String) -> void:
	if not effect_tags.has(tag):
		effect_tags.append(tag)

# 检查是否有指定标签
func has_effect_tag(tag: String) -> bool:
	return effect_tags.has(tag)

# 获取配件摘要
func get_summary() -> String:
	return "%s: %s" % [name, description]

# 设置形状数据并自动计算尺寸分类
func set_shape_data(shape: Array) -> void:
	shape_data = shape
	
	# 根据格子数量自动计算尺寸分类
	var grid_count = shape.size()
	
	if grid_count == 1:
		size_category = SizeCategory.SMALL
	elif grid_count >= 2 and grid_count <= 5:
		size_category = SizeCategory.MEDIUM
	else:  # 6个及以上
		size_category = SizeCategory.LARGE
	
	# 更新尺寸标签
	_update_size_tags()

# 获取尺寸类别的字符串表示
func get_size_category_string() -> String:
	match size_category:
		SizeCategory.SMALL:
			return "small"
		SizeCategory.MEDIUM:
			return "medium"
		SizeCategory.LARGE:
			return "large"
		_:
			return "unknown"

# 更新尺寸相关的标签
func _update_size_tags() -> void:
	# 先移除所有尺寸标签
	var size_tags = ["size_small", "size_medium", "size_large"]
	for tag in size_tags:
		if effect_tags.has(tag):
			effect_tags.erase(tag)
	
	# 添加当前尺寸的标签
	match size_category:
		SizeCategory.SMALL:
			add_effect_tag("size_small")
		SizeCategory.MEDIUM:
			add_effect_tag("size_medium")
		SizeCategory.LARGE:
			add_effect_tag("size_large")

# 获取格子数量
func get_grid_count() -> int:
	return shape_data.size()

# 设置尺寸类别（如果需要手动设置）
func set_size_category(category: int) -> void:
	size_category = category
	_update_size_tags() 