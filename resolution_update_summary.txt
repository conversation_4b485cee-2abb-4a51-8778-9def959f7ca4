高分辨率渲染设置已完成，主要修改如下：
1. 将渲染分辨率从1280x720提高到2560x1440，但保持窗口显示尺寸为1280x720
2. 更新了主游戏场景和全局常量中的设计分辨率
3. 调整了侧边栏宽度从140增加到280，与新分辨率保持比例
4. 更新了相机位置到新分辨率的中心点(1280, 720)
5. 调整了玩家位置到(1280, 1200)，保持在屏幕下方合适位置
6. 更新了星空背景，增加星星数量从100到200，使用实际屏幕尺寸而非设计分辨率
7. 调整了改装网格单元格尺寸从50x50增加到100x100，适应高分辨率
8. 增大了拖拽预览容器尺寸从70x70到140x140，图标尺寸从40x40到80x80
9. 设置窗口拉伸模式为viewport，保持高度比例(keep_height)
10. 增大了玩家尺寸1.8倍，包括蓝色方块、白色圆点和护盾轮廓
11. 增大了子弹尺寸，从6x12增加到10x20
12. 增大了敌人尺寸1.8倍，包括三角形和子弹
13. 增大了UI元素，包括血量图标(40x40→60x60)、护盾图标(40x40→60x60)和道具图标(32x32→48x48)
14. 增大了左侧状态栏的字体大小，从18增加到28
15. 增大了道具图标尺寸，从120x40增加到220x60，图标区域从30x30增加到50x50
16. 增大了进度条尺寸，从80x20增加到150x30
17. 增加了玩家移动速度，从250增加到375，提升50%
18. 增加了玩家子弹速度，从800增加到1200，提升50%
19. 增加了敌人移动速度，从150增加到225，提升50%
20. 增加了敌人子弹速度，从250增加到450，提升80%
21. 增加了道具下落速度，从30增加到60，提升100%
