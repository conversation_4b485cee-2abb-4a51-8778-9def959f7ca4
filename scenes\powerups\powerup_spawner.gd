extends Node

# 道具生成器脚本
# 负责在游戏中随机生成各种道具

# 道具类型及其对应的场景路径
var powerup_scenes = {
	"triple_shot": preload("res://scenes/powerups/powerup_T.tscn"),
	"auto_aim": preload("res://scenes/powerups/powerup_A.tscn"),
	"spread_shot": preload("res://scenes/powerups/powerup_S.tscn"),
	"health_up": preload("res://scenes/powerups/powerup_health.tscn"),
	"bomb": preload("res://scenes/powerups/bomb_powerup.tscn"),
	"speed_boost": preload("res://scenes/powerups/speed_boost_powerup.tscn")
}

# 道具生成概率配置
@export var drop_chance: float = 0.15  # 15%的几率掉落道具
@export var health_drop_weight: float = 0.25  # 回血道具的权重
@export var triple_shot_weight: float = 0.20  # 三连射道具的权重
@export var auto_aim_weight: float = 0.15  # 自动追踪道具的权重
@export var spread_shot_weight: float = 0.15  # 散射道具的权重
@export var bomb_weight: float = 0.05  # 炸弹道具的权重
@export var speed_boost_weight: float = 0.20  # 速度提升道具的权重

# 炸弹道具冷却时间
var bomb_cooldown: float = 60.0  # 60秒冷却时间
var bomb_timer: float = 0.0

# 屏幕尺寸
var screen_width: float = 1280.0
var screen_height: float = 720.0
var sidebar_width: float = 140.0

func _ready():
	# 初始化随机数生成器
	randomize()

func _process(delta):
	# 更新炸弹冷却计时器
	if bomb_timer > 0:
		bomb_timer -= delta

# 尝试生成道具
func try_spawn_powerup(position: Vector2) -> bool:
	# 随机决定是否生成道具
	if randf() > drop_chance:
		return false
	
	# 选择要生成的道具类型
	var powerup_type = select_powerup_type()
	
	# 生成选定的道具
	spawn_powerup(powerup_type, position)
	return true

# 选择要生成的道具类型
func select_powerup_type() -> String:
	# 计算总权重
	var total_weight = health_drop_weight + triple_shot_weight + auto_aim_weight + spread_shot_weight + bomb_weight + speed_boost_weight
	
	# 生成随机值
	var random_value = randf() * total_weight
	var cumulative_weight = 0.0
	
	# 根据权重选择道具类型
	cumulative_weight += health_drop_weight
	if random_value <= cumulative_weight:
		return "health_up"
	
	cumulative_weight += triple_shot_weight
	if random_value <= cumulative_weight:
		return "triple_shot"
	
	cumulative_weight += auto_aim_weight
	if random_value <= cumulative_weight:
		# 检查炸弹冷却时间
		if bomb_timer <= 0 and randf() < 0.33:  # 1/3的几率在自动追踪的位置生成炸弹
			bomb_timer = bomb_cooldown  # 重置炸弹冷却计时器
			return "bomb"
		return "auto_aim"
	
	cumulative_weight += spread_shot_weight
	if random_value <= cumulative_weight:
		return "spread_shot"
	
	cumulative_weight += bomb_weight
	if random_value <= cumulative_weight:
		# 检查炸弹冷却时间
		if bomb_timer <= 0:
			bomb_timer = bomb_cooldown  # 重置炸弹冷却计时器
			return "bomb"
		# 如果炸弹在冷却中，改为生成速度提升
		return "speed_boost"
	
	# 默认返回速度提升
	return "speed_boost"

# 生成指定类型的道具
func spawn_powerup(powerup_type: String, position: Vector2):
	# 确保道具类型有效
	if not powerup_scenes.has(powerup_type):
		print("无效的道具类型: " + powerup_type)
		return
	
	# 实例化道具
	var powerup = powerup_scenes[powerup_type].instantiate()
	
	# 将道具添加到场景
	get_tree().current_scene.add_child(powerup)
	
	# 设置道具位置
	powerup.global_position = position
	
	# 确保道具在屏幕范围内
	var powerup_size = 24  # 假设道具大小为24像素
	powerup.global_position.x = clamp(powerup.global_position.x, sidebar_width + powerup_size, screen_width - sidebar_width - powerup_size)
	powerup.global_position.y = clamp(powerup.global_position.y, powerup_size, screen_height - powerup_size)
	
	print("生成道具: " + powerup_type + " 在位置 " + str(powerup.global_position))