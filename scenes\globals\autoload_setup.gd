extends Node

# 自动加载设置脚本
# 用于在游戏启动时自动加载所有必要的管理器

func _ready():
	print("AutoloadSetup: 开始初始化...")
	
	# 检查是否已经有GameManager实例
	var game_manager = get_tree().get_first_node_in_group("game_manager")
	if not game_manager:
		print("AutoloadSetup: 创建GameManager...")
		_create_game_manager()
	
	print("AutoloadSetup: 初始化完成")

# 创建GameManager实例
func _create_game_manager():
	# 加载GameManager场景
	var game_manager_scene = load("res://scenes/globals/game_manager.tscn")
	if game_manager_scene:
		# 实例化GameManager
		var game_manager = game_manager_scene.instantiate()
		game_manager.add_to_group("game_manager")
		
		# 添加到场景树
		get_tree().root.call_deferred("add_child", game_manager)
		print("AutoloadSetup: GameManager已创建并添加到场景树")
	else:
		push_error("AutoloadSetup: 无法加载GameManager场景!") 