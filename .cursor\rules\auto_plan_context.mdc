---
description: 
globs: 
alwaysApply: false
---
---
description: 自动将 PROJECT_PLAN.md 加入 Godot 项目文件编辑上下文
globs:
  - "**/*.{gd,tscn,tres,import,shader,json,md}" # Godot 文件 + 计划文件
alwaysApply: true
---
Godot Engine 4.4.1
请严格按照 PROJECT_PLAN.md 中定义的模块顺序（从当前焦点模块开始）进行开发。

一次只专注于实现 当前焦点任务 中指定的模块和一个具体子任务。

开始新模块前，我会确认上个模块或重要阶段完成并更新此文件（修改模块完成状态 [ ] 为 [x]，并更新 当前焦点任务）。

当一个模块包含多个子任务时，请逐个实现。完成一个子任务后请告知我，我会更新子任务状态（如果我在此文件中也列出子任务状态）。

实现复杂模块或子任务时，如果需要，请先提出更细致的步骤分解计划供我确认。

完成一个子任务或在开发过程中遇到任何问题、不明确之处或需要决策的地方，请立即告知我。

请始终参考整个 @PROJECT_PLAN.md 文件以及最新的 @几何射击 (Geometric Shooter) - 游戏需求详细说明 .txt 文件来理解项目的整体背景、目标、当前进展和具体需求。

代码风格请遵循 GDScript 最佳实践（例如，清晰的变量/函数命名，适当的类型提示，模块化代码），并添加必要的注释来解释关键逻辑。

视觉元素（颜色、尺寸、形状等）请尽量精确遵循GDD中的描述。如果GDD没有明确具体数值但有风格描述，请使用符合该风格的合理默认值，或向我询问。对于GDD中标记为“易于实现”的效果，请优先考虑性能和开发效率。