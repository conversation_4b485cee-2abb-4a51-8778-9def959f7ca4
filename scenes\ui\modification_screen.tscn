[gd_scene load_steps=2 format=3 uid="uid://c51a5g7vkx3np"]

[ext_resource type="Script" path="res://scenes/ui/modification_screen.gd" id="1_684hg"]

[node name="ModificationScreen" type="Control" groups=["modification_screen"]]
process_mode = 3
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_684hg")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.501961)

[node name="AnimationContainer" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="GridPanel" type="Panel" parent="AnimationContainer"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -550.0
offset_top = -450.0
offset_right = 550.0
offset_bottom = 400.0
grow_horizontal = 2
grow_vertical = 2

[node name="GridContainer" type="GridContainer" parent="AnimationContainer/GridPanel"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_top = 15.0
offset_right = -15.0
offset_bottom = -15.0
grow_horizontal = 2
grow_vertical = 2
columns = 11

[node name="PlayerPlaceholder" type="ColorRect" parent="AnimationContainer/GridPanel"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -35.0
offset_top = -35.0
offset_right = 35.0
offset_bottom = 35.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.117647, 0.564706, 1, 1) 