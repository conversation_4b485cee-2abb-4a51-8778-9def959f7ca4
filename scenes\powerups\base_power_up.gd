extends Area2D

# 基础道具类
# 包含所有道具共有的行为

# 预加载游戏枚举
const GameEnums = preload("res://scenes/globals/game_enums.gd")

# 道具属性
@export var power_up_type: GameEnums.PowerUpType  # 道具类型
@export var lifetime: float = 10.0  # 道具存在时间
@export var warning_time: float = 3.0  # 提前警告时间（秒）
@export var float_amplitude: float = 5.0  # 浮动动画幅度
@export var float_speed: float = 2.0  # 浮动动画速度
@export var rotation_speed: float = 1.0  # 旋转速度
@export var fall_speed: float = 60.0  # 下落速度，从30增加到60

# 组件引用
@onready var lifetime_timer = $LifetimeTimer
@onready var warning_timer = $WarningTimer
@onready var animation_player = $AnimationPlayer
@onready var collision_shape = $CollisionShape2D
@onready var sprite_container = $SpriteContainer
@onready var blink_timer = $BlinkTimer

# 内部变量
var initial_position: Vector2
var elapsed_time: float = 0.0
var player_node = null
var screen_height: float
var is_blinking: bool = false
var blink_count: int = 0
var max_blinks: int = 3
var is_collected: bool = false  # 跟踪道具是否已被收集

# 信号
signal power_up_collected(type)

func _ready():
	# 设置初始位置
	initial_position = position
	
	# 获取屏幕高度（用于边界检测）
	screen_height = get_viewport_rect().size.y
	
	# 设置生命周期计时器
	lifetime_timer.wait_time = lifetime
	lifetime_timer.start()
	
	# 设置警告计时器（提前警告时间）
	warning_timer = Timer.new()
	warning_timer.one_shot = true
	warning_timer.wait_time = lifetime - warning_time  # 提前3秒警告
	warning_timer.timeout.connect(_on_warning_timer_timeout)
	add_child(warning_timer)
	warning_timer.start()
	
	# 播放出现动画
	if animation_player.has_animation("appear"):
		animation_player.play("appear")
	
	# 查找玩家节点（用于拾取效果）
	player_node = get_tree().get_first_node_in_group("player")
	
	# 尝试加载自定义图片
	load_custom_texture()

# 加载自定义图片
func load_custom_texture():
	# 获取道具类型资源名称
	var powerup_type_name = GameEnums.get_powerup_resource_name(power_up_type)
	
	# 直接使用ResourceManager自动加载单例
	var custom_texture = ResourceManager.get_powerup_texture(powerup_type_name)
	if custom_texture:
		# 找到自定义图片，创建精灵节点
		var sprite = Sprite2D.new()
		sprite.texture = custom_texture
		
		# 清除默认的视觉节点
		for child in sprite_container.get_children():
			child.queue_free()
		
		# 添加自定义精灵
		sprite_container.add_child(sprite)
		print("已加载自定义道具图片: " + powerup_type_name)

func _process(delta):
	# 向下移动
	position.y += fall_speed * delta
	
	# 浮动动画
	elapsed_time += delta
	position.y += sin(elapsed_time * float_speed) * float_amplitude * delta
	
	# 轻微旋转
	sprite_container.rotation += delta * rotation_speed
	
	# 检查是否超出屏幕底部
	if position.y > screen_height + 30:
		queue_free()

# 当生命周期结束时
func _on_lifetime_timer_timeout():
	# 如果还没有开始闪烁，才开始闪烁效果
	if not is_blinking:
		start_blinking()
	else:
		# 如果已经在闪烁，直接销毁
		queue_free()

# 开始闪烁效果
func start_blinking():
	is_blinking = true
	blink_count = 0
	blink_timer.wait_time = 0.4  # 闪烁间隔，从0.2秒增加到0.4秒，减慢闪烁速度
	blink_timer.start()
	
	# 第一次闪烁
	_on_blink_timer_timeout()

# 闪烁计时器超时
func _on_blink_timer_timeout():
	if blink_count >= max_blinks * 2:
		# 完成闪烁后销毁
		if animation_player.has_animation("disappear"):
			animation_player.play("disappear")
			# 动画结束后销毁
			await animation_player.animation_finished
			queue_free()
		else:
			# 如果没有动画，直接销毁
			queue_free()
		return
	
	# 切换可见性
	if blink_count % 2 == 0:
		sprite_container.modulate.a = 0.2  # 几乎透明
	else:
		sprite_container.modulate.a = 1.0  # 完全不透明
	
	blink_count += 1
	blink_timer.start()

# 当玩家进入区域时
func _on_area_entered(area):
	print("道具检测到碰撞: ", area.name, " 组: ", area.get_groups())
	
	# 更全面的检测方法
	var is_player = false
	
	# 检查是否在player组中
	if area.is_in_group("player"):
		is_player = true
	
	# 检查名称是否包含"Player"
	if "Player" in area.name:
		is_player = true
	
	# 检查是否有玩家特有的方法或属性
	if area.has_method("apply_power_up") or area.has_method("take_damage"):
		is_player = true
	
	if is_player:
		print("玩家碰到道具，触发收集")
		# 尝试调用玩家的apply_power_up方法
		if area.has_method("apply_power_up"):
			area.apply_power_up(power_up_type)
			print("直接调用玩家的apply_power_up方法")
		
		# 无论如何都调用自己的collect方法
		collect()
	else:
		print("非玩家对象碰到道具，忽略")

# 收集道具
func collect():
	# 检查是否已被收集
	if is_collected:
		print("道具 " + get_power_up_type_name() + " 已被收集，忽略重复收集")
		return
		
	# 标记为已收集
	is_collected = true
	
	# 禁用碰撞，防止多次触发
	if collision_shape:
		collision_shape.set_deferred("disabled", true)
	
	print("道具被收集：" + get_power_up_type_name())
	
	# 停止闪烁计时器
	if blink_timer and blink_timer.time_left > 0:
		blink_timer.stop()
	
	# 发出被收集信号
	emit_signal("power_up_collected", power_up_type)
	
	# 播放收集动画和音效
	if animation_player and animation_player.has_animation("collect"):
		animation_player.play("collect")
		# 动画结束后销毁
		await animation_player.animation_finished
		queue_free()
	else:
		# 如果没有动画，直接销毁
		queue_free()

# 获取道具类型名称（用于调试）
func get_power_up_type_name() -> String:
	return GameEnums.get_powerup_type_name(power_up_type)

# 警告计时器超时，开始闪烁
func _on_warning_timer_timeout():
	print("道具即将消失，开始闪烁警告")
	start_blinking() 
