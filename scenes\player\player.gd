extends Area2D

# 玩家基础脚本
# 根据项目计划，这个脚本将实现玩家的移动和自动射击功能

# 预加载游戏枚举
const GameEnums = preload("res://scenes/globals/game_enums.gd")

# 信号
signal health_changed(new_health)
signal player_died
signal power_up_status_changed(type, active, time_left)  # 道具状态变化信号
signal shield_changed(new_shield, max_shield)  # 护盾状态变化信号

# 玩家属性 - 使用GameConstants
@export var move_speed: float = GameConstants.PLAYER_DEFAULT_SPEED * 1.5 # 玩家移动速度，增加50%
@export var hp: int = GameConstants.PLAYER_DEFAULT_HP # 玩家生命值
@export var max_hp: int = GameConstants.PLAYER_DEFAULT_HP # 最大生命值
@export var shield_points: int = 0 # 当前护盾点数
@export var max_shield_points: int = GameConstants.PLAYER_MAX_SHIELD # 最大护盾点数
@export var thruster_rotation_speed: float = 10.0 # 推进器旋转平滑速度
@export var base_move_speed: float = GameConstants.PLAYER_DEFAULT_SPEED * 1.5 # 基础移动速度，增加50%

# 防止回血道具重复收集
var last_health_pickup_time: float = 0.0
var health_pickup_cooldown: float = 0.5 # 0.5秒冷却时间

# 射击相关 - 使用GameConstants
@export var fire_rate: float = GameConstants.PLAYER_FIRE_RATE # 射击间隔
@export var bullet_scene: PackedScene # 子弹场景引用
var base_fire_rate: float = GameConstants.PLAYER_FIRE_RATE  # 保存基础射击速率
# 新增射击控制变量
var is_auto_shooting: bool = false  # 是否处于自动射击状态
var shoot_mode: int = 0  # 0=点击切换, 1=按住射击
var bullet_damage_multiplier: float = 1.0  # 子弹伤害倍率
var cockpit_position: Vector2 = Vector2.ZERO  # 驾驶舱位置（白色小圆点中心）

# 屏幕边界 - 使用GameConstants
const SCREEN_WIDTH: float = GameConstants.DEFAULT_SCREEN_WIDTH
const SCREEN_HEIGHT: float = GameConstants.DEFAULT_SCREEN_HEIGHT
const SIDEBAR_WIDTH: float = GameConstants.SIDEBAR_WIDTH
const PLAYER_SIZE: float = 40.0  # 玩家大小，从22x22增加到40x40
const SIDEBAR_COLLISION_WIDTH: float = GameConstants.SIDEBAR_WIDTH * 1.33 + PLAYER_SIZE * (2.0/3.0)  # 增加约1/3的边栏宽度，再加2/3个玩家宽度

# 组件引用
@onready var thruster_particles = $ThrusterParticles
@onready var shoot_timer = $ShootTimer
@onready var bullet_spawn_point = $BulletSpawnPoint
@onready var blue_square = $BlueSquare

# 推进器状态
var target_thruster_rotation: float = 0.0
var target_particles_amount: int = 12 # 默认粒子数量
var original_particles_amount: int = 12 # 存储原始粒子数量

# 玩家状态
var is_invincible: bool = false
var invincibility_timer: float = 0.0
var invincibility_duration: float = 2.0
var is_dead: bool = false

# 无敌模式状态
var god_mode: bool = false

# 道具状态
var power_up_duration: float = 20.0  # 基础持续时间为20秒（从15秒改为20秒）
var auto_aim_duration: float = 10.0  # 自动瞄准持续时间为10秒
var power_ups = {
	GameEnums.PowerUpType.TRIPLE_SHOT: 0.0,  # 剩余时间
	GameEnums.PowerUpType.AUTO_AIM: 0.0,     # 剩余时间
	GameEnums.PowerUpType.SPREAD_SHOT: 0.0,  # 剩余时间
	GameEnums.PowerUpType.SPEED_BOOST: 0.0   # 剩余时间
}
var power_ups_active = {
	GameEnums.PowerUpType.TRIPLE_SHOT: false,
	GameEnums.PowerUpType.AUTO_AIM: false,
	GameEnums.PowerUpType.SPREAD_SHOT: false,
	GameEnums.PowerUpType.SPEED_BOOST: false
}

# 残影特效相关
var afterimage_timer: float = 0.0
var afterimage_interval: float = 0.2  # 每0.2秒创建一个残影（从0.15秒增加）
var afterimage_duration: float = 0.7  # 残影持续0.7秒（从0.5秒增加）
var afterimage_nodes = []  # 存储所有残影节点

# 护盾动画相关
var shield_animation_time: float = 0.0
var shield_pulse_speed: float = 5.0  # 脉冲速度
var shield_width_min: float = 1.5    # 最小线宽
var shield_width_max: float = 2.5    # 最大线宽
var shield_color_shift: float = 0.0  # 颜色变化值

# 玩家当前速度向量（用于惯性补偿）
var velocity: Vector2 = Vector2.ZERO
var prev_position: Vector2 = Vector2.ZERO

# 鼠标移动矢量补偿系统
var mouse_positions_history = [] # 存储鼠标历史位置
var mouse_history_length = 5  # 历史记录长度
var mouse_compensation_factor = 0.06  # 鼠标补偿系数
var inertia_factor = 0.3  # 惯性补偿系数

func _ready():
	# 玩家初始化
	original_particles_amount = thruster_particles.amount
	
	# 将玩家添加到"player"组，便于调试管理器查找
	add_to_group("player")
	
	# 移除可能存在的旧版视觉管理器
	var old_visual_manager = get_node_or_null("ModVisualManager")
	if old_visual_manager:
		print("移除旧版视觉管理器")
		old_visual_manager.queue_free()
	
	# 设置基础移动速度
	base_move_speed = move_speed
	
	# 设置射击计时器
	base_fire_rate = fire_rate  # 保存基础射击速率
	shoot_timer.wait_time = fire_rate
	# 不再自动开始射击，需要玩家点击鼠标
	
	# 初始化驾驶舱位置（假设是玩家中心）
	cockpit_position = Vector2.ZERO
	
	# 预加载子弹场景（如果未在Inspector中设置）
	if bullet_scene == null:
		bullet_scene = load("res://scenes/player/player_bullet.tscn")
	
	# 打印道具持续时间设置
	print("道具持续时间设置:")
	print("普通道具持续时间: " + str(power_up_duration) + "秒")
	print("自动瞄准持续时间: " + str(auto_aim_duration) + "秒")
	
	# 初始化护盾视觉效果
	update_shield_visuals()
	
	# 连接护盾动画计时器
	var shield_timer = $ShieldOutline/ShieldAnimationTimer
	if shield_timer:
		shield_timer.timeout.connect(_on_shield_animation_timer_timeout)
	
	# 延迟发送初始血量信号，确保UI已准备好
	call_deferred("emit_health_changed_signal")
	
	# 延迟发送初始护盾信号
	call_deferred("emit_shield_changed_signal")
	
	# 连接作弊管理器的无敌模式信号
	if CheatManager != null:
		CheatManager.god_mode_activated.connect(_on_god_mode_activated)
	
	# 记录初始位置用于速度计算
	prev_position = position

	# 初始化改装系统
	initialize_modification_system()

# 初始化改装系统
func initialize_modification_system():
	# 检查是否已有改装系统
	if has_node("ModificationSystem"):
		print("改装系统已存在，跳过初始化")
		return
	
	# 创建改装系统
	# 使用load方式引用脚本，避免直接类名引用
	var mod_system_script = load("res://scenes/modifications/scripts/modification_system_new.gd")
	var mod_system = mod_system_script.new()
	mod_system.name = "ModificationSystem"
	add_child(mod_system)
	
	# 连接改装系统信号
	mod_system.modifications_updated.connect(_on_modifications_updated)
	
	print("改装系统初始化完成")

# 当改装系统更新时
func _on_modifications_updated():
	print("改装系统已更新，玩家属性已刷新")
	
	# 获取改装系统
	var mod_system = get_node_or_null("ModificationSystem")
	if mod_system:
		# 更新子弹伤害倍率
		bullet_damage_multiplier = mod_system.get_damage_multiplier()
		print("子弹伤害倍率更新为: ", bullet_damage_multiplier)
	
	# 这里可以添加额外的处理逻辑，如更新UI等

# 延迟发送血量变化信号
func emit_health_changed_signal():
	emit_signal("health_changed", hp)

# 延迟发送护盾变化信号
func emit_shield_changed_signal():
	emit_signal("shield_changed", shield_points, max_shield_points)

# 设置无敌模式状态
func set_god_mode(enabled: bool):
	god_mode = enabled
	if god_mode:
		is_invincible = true
		hp = max_hp  # 恢复满血
		emit_signal("health_changed", hp)
		print("玩家无敌模式已开启")
	else:
		is_invincible = false
		print("玩家无敌模式已关闭")

func _process(delta):
	# 如果玩家已死亡，不处理输入
	if is_dead:
		return
		
	# 处理玩家输入和移动
	var direction = Vector2.ZERO
	
	# 检测WASD和方向键输入
	if Input.is_action_pressed("ui_right") or Input.is_action_pressed("ui_d"):
		direction.x += 1
	if Input.is_action_pressed("ui_left") or Input.is_action_pressed("ui_a"):
		direction.x -= 1
	if Input.is_action_pressed("ui_down") or Input.is_action_pressed("ui_s"):
		direction.y += 1
	if Input.is_action_pressed("ui_up") or Input.is_action_pressed("ui_w"):
		direction.y -= 1
	
	# 标准化方向向量并应用移动
	velocity = Vector2.ZERO
	if direction != Vector2.ZERO:
		direction = direction.normalized()
		
		# 应用速度提升效果
		var current_speed = base_move_speed
		if power_ups_active[GameEnums.PowerUpType.SPEED_BOOST]:
			current_speed *= 1.5  # 速度提升为原来的1.5倍（从1.3提高）
			
			# 处理残影效果
			afterimage_timer += delta
			if afterimage_timer >= afterimage_interval:
				create_afterimage()
				afterimage_timer = 0.0
		
		# 计算期望的移动量
		velocity = direction * current_speed
		var movement = velocity * delta
		
		# 使用更安全的方法处理移动，防止穿过边栏
		# 因为Area2D不支持test_move，我们需要使用替代方法
		var new_position = position + movement
		
		# 检查新位置是否在左右边栏内（使用扩大后的碰撞区域）
		var in_left_sidebar = new_position.x < SIDEBAR_COLLISION_WIDTH
		var in_right_sidebar = new_position.x > SCREEN_WIDTH - SIDEBAR_COLLISION_WIDTH
		
		# 如果会进入边栏，调整位置
		if in_left_sidebar:
			new_position.x = SIDEBAR_COLLISION_WIDTH
		elif in_right_sidebar:
			new_position.x = SCREEN_WIDTH - SIDEBAR_COLLISION_WIDTH
		
		# 应用有效的移动
		position = new_position
	else:
		# 没有输入时，计算当前速度为0
		velocity = Vector2.ZERO
	
	# 限制玩家在屏幕可视区域内（使用BoundaryManager）
	var player_radius = 11.25  # 根据OutlinePolygon的大小
	position = BoundaryManager.clamp_position(position, player_radius)
	
	# 更新鼠标历史位置（用于矢量补偿）
	update_mouse_history()
	
	# 处理射击输入
	handle_shooting_input()
	
	# 更新子弹发射点朝向鼠标位置
	update_bullet_spawn_direction()
	
	# 控制推进器粒子效果
	update_thruster(velocity, delta)
	
	# 处理无敌状态
	if is_invincible and not god_mode:
		invincibility_timer += delta
		# 闪烁效果
		blue_square.modulate.a = 0.5 + 0.5 * sin(invincibility_timer * 10)
		
		if invincibility_timer >= invincibility_duration:
			is_invincible = false
			blue_square.modulate.a = 1.0
	
	# 无敌模式特效
	if god_mode:
		# 彩虹闪烁效果
		var hue = fmod(Time.get_ticks_msec() / 1000.0, 1.0)
		blue_square.modulate = Color.from_hsv(hue, 0.8, 1.0, 1.0)
	
	# 更新道具状态
	update_power_ups(delta)
	
	# 更新护盾动画
	update_shield_animation(delta)

# 更新鼠标历史位置
func update_mouse_history():
	var current_mouse_pos = get_viewport().get_mouse_position()
	
	# 添加当前鼠标位置到历史记录
	mouse_positions_history.push_front(current_mouse_pos)
	
	# 限制历史记录长度
	if mouse_positions_history.size() > mouse_history_length:
		mouse_positions_history.pop_back()

# 计算鼠标速度向量
func get_mouse_velocity_vector() -> Vector2:
	# 如果历史记录不足，返回零向量
	if mouse_positions_history.size() < 2:
		return Vector2.ZERO
	
	# 使用最近两个位置计算速度
	var prev_pos = mouse_positions_history[1]
	var curr_pos = mouse_positions_history[0]
	
	# 避免鼠标未移动导致的零向量
	if curr_pos.distance_squared_to(prev_pos) < 1.0:
		if mouse_positions_history.size() > 2:
			prev_pos = mouse_positions_history[2]  # 尝试使用更早的点
		else:
			return Vector2.ZERO
	
	# 计算鼠标移动向量
	return curr_pos - prev_pos

# 处理射击输入
func handle_shooting_input():
	# 模式0: 点击切换自动射击
	if shoot_mode == 0:
		if Input.is_action_just_pressed("ui_left_mouse"):
			is_auto_shooting = !is_auto_shooting
			if is_auto_shooting:
				shoot_timer.start()
			else:
				shoot_timer.stop()
	# 模式1: 按住鼠标射击
	else:
		if Input.is_action_just_pressed("ui_left_mouse"):
			is_auto_shooting = true
			shoot_timer.start()
		elif Input.is_action_just_released("ui_left_mouse"):
			is_auto_shooting = false
			shoot_timer.stop()

# 更新子弹发射点朝向鼠标位置
func update_bullet_spawn_direction():
	# 获取鼠标位置
	var mouse_pos = get_viewport().get_mouse_position()
	
	# 计算从驾驶舱到鼠标的方向
	var global_cockpit_pos = global_position + cockpit_position
	var direction = (mouse_pos - global_cockpit_pos).normalized()
	
	# 更新子弹发射点的位置和旋转
	bullet_spawn_point.position = cockpit_position
	bullet_spawn_point.rotation = direction.angle() + PI/2  # 旋转使其朝向鼠标
	
	# 旋转玩家使其朝向鼠标位置
	# 计算角度并应用到玩家的旋转
	var target_angle = direction.angle() + PI/2  # 加上90度偏移使正面朝向鼠标
	rotation = target_angle

# 射击函数，由射击计时器触发
func _on_shoot_timer_timeout():
	# 如果玩家已死亡或不在射击状态，不射击
	if is_dead or !is_auto_shooting:
		return
	shoot()

# 创建并发射子弹
func shoot():
	# 添加调试输出
	print("射击状态 - 三连射: " + str(power_ups_active[GameEnums.PowerUpType.TRIPLE_SHOT]) + 
		", 散射: " + str(power_ups_active[GameEnums.PowerUpType.SPREAD_SHOT]))
	
	# 根据道具状态确定射击模式
	if power_ups_active[GameEnums.PowerUpType.TRIPLE_SHOT] and power_ups_active[GameEnums.PowerUpType.SPREAD_SHOT]:
		# 三连射+散射组合 - 发射5发子弹
		print("使用三连射+散射组合模式")
		shoot_triple_spread()
	elif power_ups_active[GameEnums.PowerUpType.SPREAD_SHOT]:
		# 散射模式 - 发射3发子弹
		print("使用散射模式")
		shoot_spread()
	elif power_ups_active[GameEnums.PowerUpType.TRIPLE_SHOT]:
		# 三连射模式 - 发射3发平行子弹
		print("使用三连射模式")
		shoot_triple()
	else:
		# 普通射击 - 发射单发子弹
		print("使用普通射击模式")
		shoot_single()

# 发射单发子弹
func shoot_single():
	var bullet = create_bullet(bullet_spawn_point.global_position)
	
	# 设置子弹方向为朝向鼠标
	var mouse_pos = get_viewport().get_mouse_position()
	var base_direction = (mouse_pos - bullet_spawn_point.global_position).normalized()
	
	# 计算鼠标速度补偿
	var mouse_velocity = get_mouse_velocity_vector()
	var mouse_comp = mouse_velocity.normalized() * mouse_compensation_factor * min(mouse_velocity.length() / 20.0, 1.0)
	
	# 计算惯性补偿
	var inertia_comp = velocity * inertia_factor / GameConstants.PLAYER_BULLET_SPEED
	
	# 合并所有向量，形成最终方向
	var final_direction = (base_direction + mouse_comp + inertia_comp).normalized()
	bullet.direction = final_direction
	
	# 应用伤害倍率
	apply_damage_multiplier(bullet)
	
	# 如果启用了自动追踪
	if power_ups_active[GameEnums.PowerUpType.AUTO_AIM]:
		bullet.enable_homing()

# 三连射 - 发射3发平行子弹
func shoot_triple():
	# 获取鼠标位置和方向
	var mouse_pos = get_viewport().get_mouse_position()
	var base_direction = (mouse_pos - bullet_spawn_point.global_position).normalized()
	
	# 计算鼠标速度补偿
	var mouse_velocity = get_mouse_velocity_vector()
	var mouse_comp = mouse_velocity.normalized() * mouse_compensation_factor * min(mouse_velocity.length() / 20.0, 1.0)
	
	# 计算惯性补偿
	var inertia_comp = velocity * inertia_factor / GameConstants.PLAYER_BULLET_SPEED
	
	# 合并所有向量，形成最终方向
	var final_direction = (base_direction + mouse_comp + inertia_comp).normalized()
	var perpendicular = Vector2(-final_direction.y, final_direction.x)  # 垂直于主方向的向量
	
	# 中间子弹
	var bullet_center = create_bullet(bullet_spawn_point.global_position)
	bullet_center.direction = final_direction
	
	# 左侧子弹 - 沿垂直方向偏移
	var left_pos = bullet_spawn_point.global_position - perpendicular * 10
	var bullet_left = create_bullet(left_pos)
	bullet_left.direction = final_direction
	
	# 右侧子弹 - 沿垂直方向偏移
	var right_pos = bullet_spawn_point.global_position + perpendicular * 10
	var bullet_right = create_bullet(right_pos)
	bullet_right.direction = final_direction
	
	# 应用伤害倍率
	apply_damage_multiplier(bullet_center)
	apply_damage_multiplier(bullet_left)
	apply_damage_multiplier(bullet_right)
	
	# 如果启用了自动追踪
	if power_ups_active[GameEnums.PowerUpType.AUTO_AIM]:
		bullet_center.enable_homing()
		bullet_left.enable_homing()
		bullet_right.enable_homing()

# 散射 - 发射3发子弹
func shoot_spread():
	# 扇形角度范围（60度）
	var spread_angle = deg_to_rad(60.0)
	var bullet_count = 3  # 增加到3发子弹
	
	# 获取基础方向（朝向鼠标）
	var mouse_pos = get_viewport().get_mouse_position()
	var base_direction = (mouse_pos - bullet_spawn_point.global_position).normalized()
	
	# 计算鼠标速度补偿
	var mouse_velocity = get_mouse_velocity_vector()
	var mouse_comp = mouse_velocity.normalized() * mouse_compensation_factor * min(mouse_velocity.length() / 20.0, 1.0)
	
	# 计算惯性补偿
	var inertia_comp = velocity * inertia_factor / GameConstants.PLAYER_BULLET_SPEED
	
	# 合并所有向量，形成最终基础方向
	var final_base_direction = (base_direction + mouse_comp + inertia_comp).normalized()
	var main_angle = final_base_direction.angle()
	
	# 计算每个子弹的角度
	for i in range(bullet_count):
		var angle_offset = -spread_angle/2 + spread_angle * i / (bullet_count - 1)
		var bullet_angle = main_angle + angle_offset
		var direction = Vector2(cos(bullet_angle), sin(bullet_angle))
		
		# 创建子弹
		var bullet = create_bullet(bullet_spawn_point.global_position)
		bullet.direction = direction
		
		# 应用伤害倍率
		apply_damage_multiplier(bullet)
		
		# 如果启用了自动追踪
		if power_ups_active[GameEnums.PowerUpType.AUTO_AIM]:
			bullet.enable_homing()

# 三连射+散射组合 - 发射5发子弹（中间三连+两侧散射）
func shoot_triple_spread():
	# 获取基础方向（朝向鼠标）
	var mouse_pos = get_viewport().get_mouse_position()
	var base_direction = (mouse_pos - bullet_spawn_point.global_position).normalized()
	
	# 计算鼠标速度补偿
	var mouse_velocity = get_mouse_velocity_vector()
	var mouse_comp = mouse_velocity.normalized() * mouse_compensation_factor * min(mouse_velocity.length() / 20.0, 1.0)
	
	# 计算惯性补偿
	var inertia_comp = velocity * inertia_factor / GameConstants.PLAYER_BULLET_SPEED
	
	# 合并所有向量，形成最终基础方向
	var final_direction = (base_direction + mouse_comp + inertia_comp).normalized()
	var perpendicular = Vector2(-final_direction.y, final_direction.x)  # 垂直于主方向的向量
	var main_angle = final_direction.angle()
	
	# 中间三发平行子弹
	var bullet_center = create_bullet(bullet_spawn_point.global_position)
	bullet_center.direction = final_direction
	
	# 左侧子弹 - 沿垂直方向偏移
	var left_pos = bullet_spawn_point.global_position - perpendicular * 10
	var bullet_left = create_bullet(left_pos)
	bullet_left.direction = final_direction
	
	# 右侧子弹 - 沿垂直方向偏移
	var right_pos = bullet_spawn_point.global_position + perpendicular * 10
	var bullet_right = create_bullet(right_pos)
	bullet_right.direction = final_direction
	
	# 两侧散射子弹
	var spread_angle = deg_to_rad(30.0)  # 较窄的角度
	
	# 左侧散射
	var left_bullet = create_bullet(bullet_spawn_point.global_position)
	left_bullet.direction = Vector2(cos(main_angle - spread_angle), sin(main_angle - spread_angle))
	
	# 右侧散射
	var right_bullet = create_bullet(bullet_spawn_point.global_position)
	right_bullet.direction = Vector2(cos(main_angle + spread_angle), sin(main_angle + spread_angle))
	
	# 应用伤害倍率
	apply_damage_multiplier(bullet_center)
	apply_damage_multiplier(bullet_left)
	apply_damage_multiplier(bullet_right)
	apply_damage_multiplier(left_bullet)
	apply_damage_multiplier(right_bullet)
	
	# 如果启用了自动追踪
	if power_ups_active[GameEnums.PowerUpType.AUTO_AIM]:
		bullet_center.enable_homing()
		bullet_left.enable_homing()
		bullet_right.enable_homing()
		left_bullet.enable_homing()
		right_bullet.enable_homing()

# 创建子弹的通用函数
func create_bullet(spawn_position):
	# 实例化子弹
	var bullet = bullet_scene.instantiate()
	
	# 将子弹添加到场景中
	get_tree().root.add_child(bullet)
	
	# 设置子弹位置
	bullet.global_position = spawn_position
	
	return bullet

# 应用伤害倍率到子弹
func apply_damage_multiplier(bullet):
	if bullet.has_method("set_damage_multiplier"):
		bullet.set_damage_multiplier(bullet_damage_multiplier)

# 受到伤害
func take_damage(damage: int):
	# 如果处于无敌状态、无敌模式或已死亡，忽略伤害
	if is_invincible or god_mode or is_dead:
		return
	
	# 优先使用护盾抵挡伤害
	if shield_points > 0:
		shield_points -= 1
		print("Shield absorbed damage! Remaining shields: ", shield_points)
		
		# 发送护盾变化信号
		emit_signal("shield_changed", shield_points, max_shield_points)
		
		# 更新护盾视觉效果
		update_shield_visuals()
		
		# 播放护盾受击效果
		play_shield_hit_effect()
		
		# 设置短暂无敌状态，避免同一帧内受到多次伤害
		is_invincible = true
		invincibility_timer = 0.0
		
		return
	
	# 如果没有护盾，减少生命值
	hp -= damage
	print("Player hit! HP: ", hp)
	
	# 播放受伤音效（暂时留空）
	# if has_node("HitSoundPlayer"):
	#     $HitSoundPlayer.play()
	
	# 发送血量变化信号
	emit_signal("health_changed", hp)
	
	# 设置无敌状态
	is_invincible = true
	invincibility_timer = 0.0
	
	# 检查是否死亡
	if hp <= 0:
		die()

# 玩家死亡
func die():
	# 如果处于无敌模式，不会死亡
	if god_mode:
		hp = 3  # 恢复生命值
		emit_signal("health_changed", hp)
		return
		
	# 设置死亡状态
	is_dead = true
	
	# 停止射击
	is_auto_shooting = false
	shoot_timer.stop()
	
	# 创建死亡特效
	create_death_effect()
	
	# 播放死亡音效（暂时留空）
	# if has_node("DeathSoundPlayer"):
	#     $DeathSoundPlayer.play()
	
	# 发送玩家死亡信号
	emit_signal("player_died")
	
	# 隐藏玩家
	visible = false
	
	# 禁用碰撞
	$CollisionShape2D.set_deferred("disabled", true)

# 创建死亡特效
func create_death_effect():
	# 存储原始驾驶舱位置
	var original_position = global_position
	
	# 复制驾驶舱节点，用于逃生动画
	var escape_pod = Node2D.new()
	escape_pod.name = "EscapePod"
	get_tree().current_scene.add_child(escape_pod)
	escape_pod.global_position = original_position
	
	# 添加驾驶舱视觉效果
	var circle = Polygon2D.new()
	if has_node("WhiteCircle/CirclePolygon"):
		# 复制驾驶舱圆形多边形
		circle.polygon = $WhiteCircle/CirclePolygon.polygon.duplicate()
		circle.color = Color(1, 1, 1, 1)  # 纯白色
		circle.scale = Vector2(1.5, 1.5)  # 稍微放大一点
	else:
		# 如果没有找到驾驶舱节点，创建一个简单的圆形
		var points = []
		for i in range(16):
			var angle = i * 2 * PI / 16
			points.append(Vector2(cos(angle) * 4, sin(angle) * 4))
		circle.polygon = points
		circle.color = Color(1, 1, 1, 1)
	
	escape_pod.add_child(circle)
	
	# 添加一个小小的推进器效果 - 使用简化的CPUParticles2D配置
	var thruster = CPUParticles2D.new()
	thruster.position = Vector2(0, 2) # 位于圆形下方
	thruster.amount = 16
	thruster.lifetime = 0.3
	thruster.explosiveness = 0.0
	thruster.randomness = 0.2
	
	# 正确设置CPUParticles2D的emission_shape
	thruster.emission_shape = CPUParticles2D.EMISSION_SHAPE_SPHERE
	thruster.emission_sphere_radius = 1.0
	
	thruster.direction = Vector2(0, 1)
	thruster.spread = 30.0
	thruster.gravity = Vector2(0, 0)
	thruster.initial_velocity_min = 20.0
	thruster.initial_velocity_max = 40.0
	thruster.scale_amount_min = 1.0
	thruster.scale_amount_max = 2.0
	
	# 设置颜色而不使用color_ramp
	thruster.color = Color(1, 0.7, 0.2, 0.8)
	
	# 不再使用复杂的color_ramp设置，简化为直接的颜色设置
	# 如果需要颜色渐变，可以使用tween在动画过程中改变颜色
	
	thruster.emitting = true
	escape_pod.add_child(thruster)
	
	# 创建逃生轨迹
	var tween = get_tree().create_tween()
	
	# 先创建一个小的随机偏移（驾驶舱弹出效果）
	var random_dir = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized()
	var initial_offset = random_dir * 20  # 初始随机弹出偏移
	
	# 然后创建一个向屏幕下方的逃生路径
	var screen_height = get_viewport_rect().size.y
	var escape_distance = screen_height + 50  # 确保足够的距离逃离屏幕
	
	# 创建向下逃逸的动画序列
	tween.tween_property(escape_pod, "position", escape_pod.position + initial_offset, 0.3)  # 初始弹出
	tween.tween_property(escape_pod, "position", escape_pod.position + initial_offset + Vector2(0, escape_distance), 2.0)  # 向下逃离
	
	# 添加旋转
	tween.parallel().tween_property(escape_pod, "rotation", randf_range(-PI, PI), 2.3)
	
	# 逐渐淡出并删除逃生舱
	tween.parallel().tween_property(circle, "color:a", 0.0, 1.0).set_delay(1.3)
	tween.parallel().tween_property(thruster, "modulate:a", 0.0, 0.7).set_delay(1.6)
	
	# 动画结束后删除节点
	tween.tween_callback(escape_pod.queue_free)
	
	# 使用粒子管理器创建爆炸特效
	ParticleManager.create_shield_hit_effect(original_position)
	
	# 延迟一点再创建主要死亡效果
	await get_tree().create_timer(0.1).timeout
	ParticleManager.create_player_death_effect(original_position)

# 当玩家与敌人子弹或敌人碰撞
func _on_area_entered(area):
	print("玩家检测到碰撞: ", area.name, " 组: ", area.get_groups())
	
	# 处理道具碰撞
	if area.is_in_group("powerups") or area.is_in_group("power_up"):
		print("玩家碰到道具，尝试应用道具效果")
		
		# 确保道具有效且可收集
		if not is_instance_valid(area) or area.is_queued_for_deletion():
			print("道具无效或已被销毁，忽略")
			return
			
		# 检查道具是否有碰撞形状
		var collision = area.get_node_or_null("CollisionShape2D")
		if collision and collision.disabled:
			print("道具碰撞已禁用，忽略重复收集")
			return
			
		# 立即禁用碰撞，防止多次触发
		if collision:
			collision.set_deferred("disabled", true)
			
		# 收集道具
		if area.has_method("collect"):
			# 添加一个延迟，确保不会在同一帧内多次处理
			await get_tree().create_timer(0.01).timeout
			if is_instance_valid(area) and not area.is_queued_for_deletion():
				area.collect()
	
	# 处理敌人和敌人子弹碰撞
	elif area.is_in_group("enemy_bullets") or area.is_in_group("enemies"):
		# 如果是敌人子弹或敌人本身，玩家受到伤害
		if area.has_method("get_damage"):
			take_damage(area.get_damage())
		else:
			take_damage(1)  # 默认伤害值为1
	
	# 转发碰撞信号给改装配件
	var mod_visuals = get_node_or_null("ModificationVisuals")
	if mod_visuals:
		print("找到ModificationVisuals节点，尝试转发碰撞信号")
		var mod_count = 0
		for mod in mod_visuals.get_children():
			if mod is Area2D:
				mod_count += 1
				print("转发碰撞信号到配件: ", mod.name)
				# 模拟改装配件也接收到同样的碰撞
				if mod.has_method("_on_area_entered"):
					print("使用_on_area_entered方法转发")
					mod.call("_on_area_entered", area)
				# 或者直接转发信号
				elif mod.has_signal("area_entered"):
					print("使用信号转发")
					mod.emit_signal("area_entered", area)
		print("总共转发给", mod_count, "个配件")
	else:
		print("未找到ModificationVisuals节点，无法转发碰撞信号")

# 激活无敌模式
func _on_god_mode_activated():
	god_mode = true
	is_invincible = true  # 设置为无敌
	print("Player: God Mode Activated!")
	
	# 恢复生命值
	hp = 3
	emit_signal("health_changed", hp)

# 获取无敌模式状态
func is_in_god_mode():
	return god_mode 

# 激活炸弹效果
func activate_bomb():
	# 创建炸弹爆炸特效
	create_bomb_explosion_effect()
	
	# 获取所有敌人
	var enemies = get_tree().get_nodes_in_group("enemies")
	
	# 摧毁所有非Boss敌人
	for enemy in enemies:
		if enemy.has_method("is_boss") and enemy.is_boss():
			# 如果是Boss，只造成伤害
			if enemy.has_method("take_damage"):
				enemy.take_damage(3)  # 对Boss造成3点伤害
		else:
			# 普通敌人直接摧毁
			if enemy.has_method("die"):
				enemy.die()  # 使用敌人的die方法以确保正确处理分数和特效
			else:
				enemy.queue_free()  # 直接销毁

# 创建炸弹爆炸特效
func create_bomb_explosion_effect():
	# 创建全屏闪光效果
	var flash = ColorRect.new()
	get_tree().current_scene.add_child(flash)
	
	# 设置闪光效果属性
	flash.color = Color(1, 1, 1, 0.7)  # 白色半透明
	flash.size = Vector2(SCREEN_WIDTH, SCREEN_HEIGHT)
	flash.position = Vector2.ZERO
	
	# 创建动画效果
	var tween = get_tree().create_tween()
	tween.tween_property(flash, "color:a", 0.0, 0.5)  # 0.5秒内淡出
	tween.tween_callback(flash.queue_free)  # 完成后删除
	
	# 使用粒子管理器创建爆炸效果
	ParticleManager.create_bomb_explosion_effect(Vector2(SCREEN_WIDTH/2, SCREEN_HEIGHT/2))

# 创建残影效果
func create_afterimage():
	# 创建一个新的Node2D节点作为残影容器
	var afterimage = Node2D.new()
	get_tree().current_scene.add_child(afterimage)
	afterimage.global_position = global_position
	afterimage.rotation = rotation
	
	# 复制玩家的外观 - 使用Polygon2D
	var blue_outline = Polygon2D.new()
	if $BlueSquare and $BlueSquare.has_node("OutlinePolygon"):
		blue_outline.polygon = $BlueSquare/OutlinePolygon.polygon.duplicate()
		blue_outline.color = Color(0.7, 0.9, 1.0, 0.3)  # 浅蓝色半透明
		afterimage.add_child(blue_outline)
	
	# 添加到残影列表
	afterimage_nodes.append(afterimage)
	
	# 创建淡出和自动销毁效果
	var tween = get_tree().create_tween()
	tween.tween_property(blue_outline, "color:a", 0.0, afterimage_duration)
	tween.tween_callback(func(): 
		if is_instance_valid(afterimage) and afterimage_nodes.has(afterimage):
			afterimage_nodes.erase(afterimage)
			afterimage.queue_free()
	)

# 清除所有残影
func clear_afterimages():
	for afterimage in afterimage_nodes:
		if is_instance_valid(afterimage):
			afterimage.queue_free()
	afterimage_nodes.clear() 

# 增加护盾点数
func add_shield_point():
	if shield_points < max_shield_points:
		shield_points += 1
		print("Shield point added! Current shields: ", shield_points)
		
		# 发送护盾变化信号
		emit_signal("shield_changed", shield_points, max_shield_points)
		
		# 更新护盾视觉效果
		update_shield_visuals()
		
		return true
	else:
		print("Shield already at maximum capacity!")
		return false

# 更新护盾视觉效果
func update_shield_visuals():
	# 根据护盾点数比例设置颜色
	var shield_ratio = float(shield_points) / float(max_shield_points) if max_shield_points > 0 else 0.0
	
	# 获取ShieldOutline节点
	var shield_outline = get_node_or_null("ShieldOutline")
	if shield_outline:
		# 如果没有护盾，隐藏描边
		if shield_points <= 0:
			shield_outline.visible = false
			return
		
		# 确保描边可见
		shield_outline.visible = true
		
		# 根据护盾比例设置颜色
		var shield_color = Color.WHITE  # 默认颜色
		
		if shield_ratio <= 0.33:
			# 状态1: 低护盾 - 橙红色
			shield_color = Color(1.0, 0.4, 0.2, 0.8)
		elif shield_ratio <= 0.66:
			# 状态2: 中等护盾 - 黄色
			shield_color = Color(1.0, 0.9, 0.2, 0.8)
		else:
			# 状态3: 高护盾 - 亮青色
			shield_color = Color(0.2, 1.0, 1.0, 0.8)
		
		# 应用颜色到护盾线条
		var shield_lines = shield_outline.get_node_or_null("ShieldLines")
		if shield_lines:
			for line in shield_lines.get_children():
				if line is Line2D:
					line.default_color = shield_color
			
		# 设置整体节点的可见性
		shield_outline.modulate = Color(1, 1, 1, 1)  # 重置modulate，确保可见

# 播放护盾受击效果
func play_shield_hit_effect():
	# 创建护盾受击粒子效果
	ParticleManager.create_shield_hit_effect(global_position)
	
	# 获取ShieldOutline节点
	var shield_outline = get_node_or_null("ShieldOutline")
	if shield_outline and shield_points > 0:
		# 获取护盾线条
		var shield_lines = shield_outline.get_node_or_null("ShieldLines")
		if shield_lines:
			# 保存原始颜色
			var original_colors = []
			for line in shield_lines.get_children():
				if line is Line2D:
					original_colors.append(line.default_color)
					line.default_color = Color(1, 1, 1, 1)  # 短暂变为纯白色
			
			# 使用计时器恢复原始颜色
			var timer = get_tree().create_timer(0.1)
			await timer.timeout
			
			# 如果节点仍然存在，恢复颜色
			if is_instance_valid(shield_lines):
				var i = 0
				for line in shield_lines.get_children():
					if line is Line2D and i < original_colors.size():
						line.default_color = original_colors[i]
						i += 1

# 更新护盾动画
func update_shield_animation(delta):
	shield_animation_time += delta
	shield_color_shift += delta * 0.3
	
	# 获取ShieldOutline节点
	var shield_outline = get_node_or_null("ShieldOutline")
	if shield_outline and shield_points > 0:
		var shield_lines = shield_outline.get_node_or_null("ShieldLines")
		if shield_lines:
			# 计算波动效果的线宽
			var pulse_factor = (sin(shield_animation_time * shield_pulse_speed) + 1.0) * 0.5
			var current_width = lerp(shield_width_min, shield_width_max, pulse_factor)
			
			# 计算颜色微妙变化
			var hue_shift = sin(shield_color_shift) * 0.05  # 微小的色调变化
			
			for line in shield_lines.get_children():
				if line is Line2D:
					line.width = current_width
					
					# 获取基础颜色
					var base_color = line.default_color
					
					# 应用微妙的颜色变化
					var new_color = base_color
					new_color.h = fmod(base_color.h + hue_shift, 1.0)
					new_color.a = 0.6 + pulse_factor * 0.2  # 透明度也轻微波动
					
					# 临时应用颜色变化，但不改变默认颜色
					line.modulate = Color(1.0, 1.0, 1.0, new_color.a / 0.6)

# 护盾动画计时器回调
func _on_shield_animation_timer_timeout():
	# 这个函数每0.05秒调用一次，用于更新护盾效果
	pass  # 主要动画在update_shield_animation中处理

# 更新道具状态
func update_power_ups(delta):
	# 遍历所有道具类型
	for power_up_type in power_ups.keys():
		# 如果道具处于激活状态
		if power_ups[power_up_type] > 0:
			# 减少剩余时间
			power_ups[power_up_type] -= delta
			
			# 如果道具时间用完，禁用效果
			if power_ups[power_up_type] <= 0:
				power_ups[power_up_type] = 0
				power_ups_active[power_up_type] = false
				
				# 发送道具状态变化信号
				emit_signal("power_up_status_changed", power_up_type, false, 0)
				
				# 如果是三连射，恢复原始射速
				if power_up_type == GameEnums.PowerUpType.TRIPLE_SHOT:
					update_fire_rate()
					
				# 如果是速度提升，清除所有残影
				if power_up_type == GameEnums.PowerUpType.SPEED_BOOST:
					clear_afterimages()
			else:
				# 发送道具状态更新信号（剩余时间）
				emit_signal("power_up_status_changed", power_up_type, true, power_ups[power_up_type])

# 应用道具效果
func apply_power_up(power_up_type):
	print("应用道具效果，类型: ", power_up_type)
	match power_up_type:
		GameEnums.PowerUpType.TRIPLE_SHOT:
			# 三连射 - 固定20秒，不叠加
			print("三连射 - 应用前持续时间: " + str(power_ups[GameEnums.PowerUpType.TRIPLE_SHOT]))
			# 直接设置为20秒，不考虑之前的时间
			power_ups[GameEnums.PowerUpType.TRIPLE_SHOT] = power_up_duration  # 固定20秒
			power_ups_active[GameEnums.PowerUpType.TRIPLE_SHOT] = true
			update_fire_rate()  # 更新射速
			
			# 发送道具状态变化信号
			emit_signal("power_up_status_changed", GameEnums.PowerUpType.TRIPLE_SHOT, true, power_ups[GameEnums.PowerUpType.TRIPLE_SHOT])
			print("激活三连射！持续时间: " + str(power_ups[GameEnums.PowerUpType.TRIPLE_SHOT]) + "秒")
		
		GameEnums.PowerUpType.AUTO_AIM:
			# 追踪子弹 - 固定10秒
			print("自动瞄准 - 应用前持续时间: " + str(power_ups[GameEnums.PowerUpType.AUTO_AIM]))
			power_ups[GameEnums.PowerUpType.AUTO_AIM] = auto_aim_duration  # 固定10秒
			power_ups_active[GameEnums.PowerUpType.AUTO_AIM] = true
			
			# 发送道具状态变化信号
			emit_signal("power_up_status_changed", GameEnums.PowerUpType.AUTO_AIM, true, power_ups[GameEnums.PowerUpType.AUTO_AIM])
			print("激活追踪子弹！持续时间: " + str(power_ups[GameEnums.PowerUpType.AUTO_AIM]) + "秒")
		
		GameEnums.PowerUpType.SPREAD_SHOT:
			# 散射 - 固定20秒，不叠加
			print("散射 - 应用前持续时间: " + str(power_ups[GameEnums.PowerUpType.SPREAD_SHOT]))
			# 直接设置为20秒，不考虑之前的时间
			power_ups[GameEnums.PowerUpType.SPREAD_SHOT] = power_up_duration  # 固定20秒
			power_ups_active[GameEnums.PowerUpType.SPREAD_SHOT] = true
			
			# 发送道具状态变化信号
			emit_signal("power_up_status_changed", GameEnums.PowerUpType.SPREAD_SHOT, true, power_ups[GameEnums.PowerUpType.SPREAD_SHOT])
			print("激活散射！持续时间: " + str(power_ups[GameEnums.PowerUpType.SPREAD_SHOT]) + "秒")
		
		GameEnums.PowerUpType.HEALTH_UP:
			# 检查是否在冷却期内
			var current_time = Time.get_ticks_msec() / 1000.0
			if current_time - last_health_pickup_time < health_pickup_cooldown:
				print("回血道具冷却中，忽略此次收集 (间隔: " + str(current_time - last_health_pickup_time) + "秒)")
				return
				
			# 更新最后收集时间
			last_health_pickup_time = current_time
			
			# 回血 - 立即恢复1点生命值，如果生命值已满则增加护盾
			print("====== 应用回血道具 ======")
			print("应用前状态 - 生命值: " + str(hp) + "/" + str(max_hp) + ", 护盾: " + str(shield_points) + "/" + str(max_shield_points))
			
			# 只执行一种效果：要么回血，要么加护盾
			if hp < max_hp:
				# 只恢复1点生命值
				hp += 1
				emit_signal("health_changed", hp)
				print("效果：恢复1点生命值！当前生命: " + str(hp))
			# 只有当生命值已满时才尝试增加护盾
			elif shield_points < max_shield_points:
				add_shield_point()
				print("效果：生命值已满，增加1点护盾！当前护盾: " + str(shield_points))
			else:
				print("效果：生命值已满，护盾也已满！无效果")
			
			print("应用后状态 - 生命值: " + str(hp) + "/" + str(max_hp) + ", 护盾: " + str(shield_points) + "/" + str(max_shield_points))
			print("====== 回血道具应用完成 ======")
		
		GameEnums.PowerUpType.BOMB:
			# 炸弹 - 清除所有普通敌人
			activate_bomb()
			print("激活炸弹！摧毁所有普通敌人！")
			
		GameEnums.PowerUpType.SPEED_BOOST:
			# 速度提升 - 固定20秒，不叠加
			print("速度提升 - 应用前持续时间: " + str(power_ups[GameEnums.PowerUpType.SPEED_BOOST]))
			# 直接设置为20秒，不考虑之前的时间
			power_ups[GameEnums.PowerUpType.SPEED_BOOST] = power_up_duration  # 固定20秒
			power_ups_active[GameEnums.PowerUpType.SPEED_BOOST] = true
			
			# 发送道具状态变化信号
			emit_signal("power_up_status_changed", GameEnums.PowerUpType.SPEED_BOOST, true, power_ups[GameEnums.PowerUpType.SPEED_BOOST])
			print("激活速度提升！持续时间: " + str(power_ups[GameEnums.PowerUpType.SPEED_BOOST]) + "秒")
		_:
			print("未知道具类型: ", power_up_type)

# 更新射击速率
func update_fire_rate():
	# 三连射不再提高射速，使用基础射速
	fire_rate = base_fire_rate
	
	# 更新射击计时器
	shoot_timer.wait_time = fire_rate

func update_thruster(velocity: Vector2, delta: float):
	# 默认情况：向下喷射
	target_thruster_rotation = 0.0
	var should_emit = true # 默认都会发射，只是数量不同
	
	if velocity.y > 0:
		# 向下移动时，减少粒子数量但不完全停止
		target_particles_amount = max(3, original_particles_amount / 4) # 减少到原来的1/4，但至少有3个
	else:
		# 处理水平移动和向上移动的情况
		if velocity.x < 0:
			# 向左移动，火焰向右后方喷射（-45度）
			target_thruster_rotation = deg_to_rad(-45.0)
			target_particles_amount = original_particles_amount
		elif velocity.x > 0:
			# 向右移动，火焰向左后方喷射（45度）
			target_thruster_rotation = deg_to_rad(45.0)
			target_particles_amount = original_particles_amount
		elif velocity.y < 0 or (velocity.x == 0 and velocity.y == 0):
			# 向上移动或静止时，火焰向下喷射
			target_thruster_rotation = 0.0
			target_particles_amount = original_particles_amount
	
	# 平滑过渡到目标旋转角度
	thruster_particles.rotation = lerp(thruster_particles.rotation, target_thruster_rotation, delta * thruster_rotation_speed)
	
	# 设置粒子数量
	thruster_particles.amount = target_particles_amount
	
	# 设置粒子发射状态
	thruster_particles.emitting = should_emit
