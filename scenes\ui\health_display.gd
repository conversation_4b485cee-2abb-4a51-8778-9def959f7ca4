extends Control

# 玩家最大生命值
var max_health: int = 3

# 组件引用
@onready var heart_icon = $HBoxContainer/HeartIcon
@onready var health_label = $HBoxContainer/HealthLabel

func _ready():
	# 初始化心形图标
	heart_icon.modulate.a = 1.0

# 加载自定义健康图标
func load_custom_health_icon():
	var health_texture = ResourceManager.get_resource(ResourceManager.ResourceType.HEALTH)
	if health_texture:
		heart_icon.texture = health_texture

# 更新显示的生命值
func update_health(current_health: int):
	# 确保生命值在有效范围内
	current_health = clamp(current_health, 0, max_health)
	
	# 更新生命值文本显示
	health_label.text = str(current_health) + "/" + str(max_health)
	
	# 如果生命值为0，使心形图标变暗
	if current_health <= 0:
		heart_icon.modulate.a = 0.3
	else:
		heart_icon.modulate.a = 1.0

# 设置最大生命值（可供将来扩展使用）
func set_max_health(new_max_health: int):
	max_health = max(1, new_max_health)  # 确保最大生命值至少为1 
 
