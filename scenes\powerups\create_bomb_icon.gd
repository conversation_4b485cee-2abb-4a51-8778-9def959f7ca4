@tool
extends EditorScript

# 在编辑器中运行此脚本以生成炸弹图标并保存为PNG

func _run():
	# 创建图像
	var img = Image.create(24, 24, false, Image.FORMAT_RGBA8)
	img.fill(Color(0, 0, 0, 0))  # 透明背景
	
	# 绘制水雷
	draw_sea_mine(img)
	
	# 保存图像
	var save_path = "res://custom_resources/powerups/bomb/bomb_mine.png"
	img.save_png(save_path)
	print("炸弹图标已保存到: " + save_path)

func draw_sea_mine(img: Image):
	# 主体 (灰色八边形)
	var center = Vector2(12, 12)
	var radius = 8
	var color = Color(0.4, 0.4, 0.4)
	var dark_color = Color(0.2, 0.2, 0.2)
	
	# 绘制主体
	for x in range(24):
		for y in range(24):
			var pos = Vector2(x, y)
			var dist = pos.distance_to(center)
			
			# 主体 (圆形)
			if dist < radius:
				img.set_pixel(x, y, color)
			
			# 中心 (深色圆形)
			if dist < radius / 2:
				img.set_pixel(x, y, dark_color)
	
	# 绘制尖刺
	var spike_color = Color(0.3, 0.3, 0.3)
	var spike_length = 5
	var num_spikes = 8
	
	for i in range(num_spikes):
		var angle = i * (2 * PI / num_spikes)
		var dir = Vector2(cos(angle), sin(angle))
		var start = center + dir * radius
		var end = center + dir * (radius + spike_length)
		
		# 绘制一个简单的线段作为尖刺
		draw_line(img, start, end, spike_color, 2)

# 简单的线段绘制函数
func draw_line(img: Image, from: Vector2, to: Vector2, color: Color, width: int = 1):
	var dx = to.x - from.x
	var dy = to.y - from.y
	var steps = max(abs(dx), abs(dy))
	
	if steps == 0:
		return
	
	var x_inc = dx / steps
	var y_inc = dy / steps
	
	var x = from.x
	var y = from.y
	
	for i in range(steps + 1):
		# 绘制宽度为width的点
		for w_x in range(-width/2, width/2 + 1):
			for w_y in range(-width/2, width/2 + 1):
				var px = int(x + w_x)
				var py = int(y + w_y)
				if px >= 0 and px < img.get_width() and py >= 0 and py < img.get_height():
					img.set_pixel(px, py, color)
		
		x += x_inc
		y += y_inc 