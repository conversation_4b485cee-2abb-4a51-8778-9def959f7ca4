extends Node2D
class_name ModificationVisualScript

# 改装配件视觉效果类 - 简化版
# 负责显示改装配件的视觉效果

# 常量定义
const PART_SCALE_FACTOR = 0.7

# 改装配件的基本属性
var mod_id: String = ""
var mod_color: Color = Color(0.3, 0.7, 1.0)  # 默认蓝色
var mod_size: Vector2 = Vector2(15, 15)  # 默认大小
var mod_position: Vector2 = Vector2.ZERO  # 相对于玩家的位置

# 视觉组件
var outline: Polygon2D
var inner: Polygon2D

# 初始化
func _init(p_id: String, p_color: Color, p_position: Vector2, p_size: Vector2 = Vector2(15, 15)):
	mod_id = p_id
	mod_color = p_color
	mod_position = p_position
	mod_size = p_size * PART_SCALE_FACTOR  # 应用缩放因子
	
	# 创建视觉组件
	call_deferred("create_visual_components")

# 准备
func _ready():
	# 设置位置
	position = mod_position
	
	# 简单的出现动画
	scale = Vector2(0.5, 0.5)
	modulate.a = 0.5
	
	# 使用简单的Tween
	var tween = create_tween()
	tween.tween_property(self, "scale", Vector2.ONE, 0.3)
	tween.parallel().tween_property(self, "modulate:a", 1.0, 0.3)

# 创建视觉组件
func create_visual_components():
	# 创建外框
	outline = Polygon2D.new()
	outline.color = mod_color
	outline.polygon = create_polygon(mod_size)
	add_child(outline)
	
	# 创建内框（略小一些，黑色填充）
	inner = Polygon2D.new()
	inner.color = Color(0, 0, 0, 1)  # 黑色
	var inner_size = mod_size * 0.85  # 内框稍小一些
	inner.polygon = create_polygon(inner_size)
	add_child(inner)

# 创建多边形点阵列
func create_polygon(size: Vector2) -> PackedVector2Array:
	var half_width = size.x / 2.0
	var half_height = size.y / 2.0
	
	return PackedVector2Array([
		Vector2(-half_width, -half_height),
		Vector2(half_width, -half_height),
		Vector2(half_width, half_height),
		Vector2(-half_width, half_height),
		Vector2(-half_width, -half_height)
	])

# 播放移除动画并销毁
func play_remove_animation():
	# 创建简单的消失动画
	var tween = create_tween()
	tween.tween_property(self, "scale", Vector2.ZERO, 0.3)
	tween.parallel().tween_property(self, "modulate:a", 0.0, 0.3)
	
	# 动画结束后删除节点
	tween.tween_callback(queue_free)

# 更新颜色
func update_color(new_color: Color):
	mod_color = new_color
	if outline:
		outline.color = new_color

# 显示闪烁效果
func flash(duration: float = 0.5, frequency: float = 10.0):
	var tween = create_tween()
	tween.set_loops(int(frequency * duration)) 
	tween.tween_property(outline, "modulate:a", 0.5, duration/(frequency*2.0))
	tween.tween_property(outline, "modulate:a", 1.0, duration/(frequency*2.0))
	
	return tween 