[gd_scene load_steps=5 format=3 uid="uid://bj4jcf0f3ywu0"]

[ext_resource type="Script" path="res://scenes/enemies/enemy_triangle.gd" id="1_qlcjm"]

[sub_resource type="Gradient" id="Gradient_ioqn4"]
offsets = PackedFloat32Array(0, 1)
colors = PackedColorArray(1, 0.2, 0.2, 1, 1, 0.2, 0.2, 0.2)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_yvkb0"]
gradient = SubResource("Gradient_ioqn4")
width = 64
height = 64
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(1, 0.5)

[sub_resource type="CircleShape2D" id="CircleShape2D_3jbdx"]
radius = 22.0

[node name="EnemyTriangle" type="Area2D" groups=["enemies"]]
scale = Vector2(1.8, 1.8)
collision_layer = 4
collision_mask = 3
script = ExtResource("1_qlcjm")

[node name="TrianglePolygon" type="Polygon2D" parent="."]
color = Color(0, 0, 0, 1)
polygon = PackedVector2Array(0, 12, -12, -12, 12, -12)

[node name="TriangleOutline" type="Line2D" parent="."]
points = PackedVector2Array(0, 12, -12, -12, 12, -12, 0, 12)
width = 2.5
default_color = Color(1, 0.2, 0.2, 1)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_3jbdx")

[node name="PointLight2D" type="PointLight2D" parent="."]
color = Color(1, 0.2, 0.2, 1)
energy = 0.3
texture = SubResource("GradientTexture2D_yvkb0")
texture_scale = 0.5

[node name="ShootTimer" type="Timer" parent="."]
wait_time = 1.5
autostart = true

[node name="HitEffectTimer" type="Timer" parent="."]
wait_time = 0.1
one_shot = true

[connection signal="area_entered" from="." to="." method="_on_area_entered"]
[connection signal="timeout" from="ShootTimer" to="." method="_on_shoot_timer_timeout"]
[connection signal="timeout" from="HitEffectTimer" to="." method="_on_hit_effect_timer_timeout"] 