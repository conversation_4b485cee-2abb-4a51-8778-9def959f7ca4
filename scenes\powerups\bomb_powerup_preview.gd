extends Node2D

# 生成炸弹道具预览图像的脚本

@onready var viewport = $SubViewport
@onready var sprite = $SubViewport/SeaMine

func _ready():
	# 等待一帧以确保视口已经准备好
	await get_tree().process_frame
	
	# 生成图像
	var img = viewport.get_texture().get_image()
	
	# 保存图像
	var path = "res://custom_resources/powerups/bomb/bomb_preview.png"
	img.save_png(path)
	print("已保存炸弹道具预览图像到: " + path)
	
	# 任务完成
	print("预览图像已生成，可以退出此场景")

func _process(_delta):
	# 轻微旋转以模拟游戏中的效果
	sprite.rotation += 0.01 