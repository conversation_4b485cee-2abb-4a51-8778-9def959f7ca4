extends Node2D
class_name ModVisualManager

# 改装件视觉管理器 - 空实现
# 该脚本已被简化，不再显示玩家周边的改装配件视觉效果

# 信号
signal mod_attached(mod_id, position)  # 改装件被装备
signal mod_detached(mod_id, position)  # 改装件被卸下

# 父节点（玩家飞船）
var parent_ship = null

# 当前可见的改装件视觉节点 {mod_id+position: node}
var visible_mods = {}

# 初始化
func _ready():
	parent_ship = get_parent()
	print("ModVisualManager: 空实现已初始化")

# 装备改装件（添加视觉效果）- 空实现
func attach_mod(mod_resource, grid_position: Vector2) -> void:
	# 创建唯一ID，用于跟踪该位置的改装件
	var unique_id = _create_unique_id(mod_resource.id, grid_position)
	
	# 记录到可见改装件字典
	visible_mods[unique_id] = null
	
	# 发送信号
	emit_signal("mod_attached", mod_resource.id, grid_position)
	
	print("ModVisualManager: 改装件已记录 " + mod_resource.name + " 在位置 " + str(grid_position))

# 卸下改装件（移除视觉效果）- 空实现
func detach_mod(mod_id: String, grid_position: Vector2) -> void:
	# 创建唯一ID
	var unique_id = _create_unique_id(mod_id, grid_position)
	
	# 从字典中移除
	if visible_mods.has(unique_id):
		visible_mods.erase(unique_id)
		# 发送信号
		emit_signal("mod_detached", mod_id, grid_position)
		print("ModVisualManager: 改装件已移除，位置: " + str(grid_position))

# 创建唯一ID
func _create_unique_id(mod_id: String, position: Vector2) -> String:
	return mod_id + "_" + str(position.x) + "_" + str(position.y)

# 清除所有视觉效果
func clear_all_visuals() -> void:
	visible_mods.clear()
	print("ModVisualManager: 已清除所有记录") 
	
