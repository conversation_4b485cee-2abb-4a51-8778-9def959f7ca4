extends Panel

# 改装件物品的拖放脚本
# 用于物品栏中可拖拽的改装件项

# 改装件属性
var mod_name: String = "默认改装件"
var mod_color: Color = Color.RED

# 用于标识此道具在物品栏中的位置
var inventory_index: int = -1

# 提示框管理器
var tooltip_manager = null

# 配件数据管理器
var mod_data = null

# 初始化
func _ready():
	# 设置鼠标样式
	mouse_default_cursor_shape = Control.CURSOR_POINTING_HAND
	
	# 确保所有子节点不拦截鼠标事件
	for child in get_children():
		make_child_pass_events(child)
	
	# 连接鼠标事件
	mouse_entered.connect(_on_mouse_entered)
	mouse_exited.connect(_on_mouse_exited)
	
	# 延迟加载管理器，确保游戏已完全初始化
	call_deferred("_load_managers")

# 加载管理器
func _load_managers():
	# 加载提示框管理器
	tooltip_manager = load("res://scenes/ui/tooltip_manager.gd").get_instance()
	
	# 加载配件数据管理器
	mod_data = load("res://scenes/modifications/scripts/modification_data_manager.gd").get_instance()

# 鼠标进入事件
func _on_mouse_entered():
	if tooltip_manager and mod_data:
		# 获取配件数据
		var data = mod_data.get_modification_data(mod_name)
		
		# 显示提示框
		tooltip_manager.show_tooltip(data["name"], data["description"])
		
		print("显示物品栏配件提示框: ", mod_name)

# 鼠标离开事件
func _on_mouse_exited():
	if tooltip_manager:
		# 隐藏提示框
		tooltip_manager.hide_tooltip()
		
		print("隐藏物品栏配件提示框")

# 递归设置所有子节点不拦截鼠标事件
func make_child_pass_events(node):
	if node is Control:
		# 设置鼠标过滤为传递，让事件穿透给父节点
		node.mouse_filter = Control.MOUSE_FILTER_PASS
		
	# 递归处理所有子节点
	for child in node.get_children():
		make_child_pass_events(child)

# 获取拖拽数据
func _get_drag_data(position):
	# 创建拖拽数据字典
	var drag_data = {
		"type": "modification_part_placeholder",
		"name": mod_name,
		"color": mod_color,
		"source_type": "inventory", # 标记来源为物品栏
		"source_item": self # 引用自身，方便后续删除
	}
	
	# 创建拖拽预览
	var preview = create_drag_preview()
	set_drag_preview(preview)
	
	print("开始从物品栏拖拽: " + mod_name)
	return drag_data

# 从物品栏中移除此道具
func remove_from_inventory():
	# 从父容器中移除自己
	if get_parent():
		get_parent().remove_child(self)
		queue_free()
		print("已从物品栏移除道具: " + mod_name)

# 创建拖拽预览
func create_drag_preview():
	# 创建一个控制节点作为预览容器
	var preview_container = Panel.new()
	preview_container.custom_minimum_size = Vector2(70, 70)
	
	# 复制当前面板样式
	if get("theme_override_styles/panel") != null:
		preview_container.set("theme_override_styles/panel", get("theme_override_styles/panel"))
	
	# 设置半透明效果
	preview_container.modulate = Color(1, 1, 1, 0.7)
	
	# 创建图标
	var icon = ColorRect.new()
	icon.color = mod_color
	icon.custom_minimum_size = Vector2(40, 40)
	icon.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	icon.size_flags_vertical = Control.SIZE_SHRINK_CENTER
	
	# 创建标签
	var label = Label.new()
	label.text = mod_name
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.vertical_alignment = VERTICAL_ALIGNMENT_BOTTOM
	
	# 创建容器布局
	var vbox = VBoxContainer.new()
	vbox.add_child(icon)
	vbox.add_child(label)
	vbox.alignment = BoxContainer.ALIGNMENT_CENTER
	
	# 添加到预览容器
	preview_container.add_child(vbox)
	
	vbox.anchors_preset = Control.PRESET_FULL_RECT
	vbox.offset_right = 0
	vbox.offset_bottom = 0
	
	return preview_container
