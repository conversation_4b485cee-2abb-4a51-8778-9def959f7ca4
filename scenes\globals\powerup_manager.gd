extends Node

# 道具管理器
# 统一管理所有道具相关的功能

# 预加载游戏枚举
const GameEnums = preload("res://scenes/globals/game_enums.gd")

# 道具场景路径
var powerup_scenes = {
	GameEnums.PowerUpType.TRIPLE_SHOT: preload("res://scenes/powerups/powerup_T.tscn"),
	GameEnums.PowerUpType.AUTO_AIM: preload("res://scenes/powerups/powerup_A.tscn"),
	GameEnums.PowerUpType.SPREAD_SHOT: preload("res://scenes/powerups/powerup_S.tscn"),
	GameEnums.PowerUpType.HEALTH_UP: preload("res://scenes/powerups/powerup_health.tscn"),
	GameEnums.PowerUpType.BOMB: preload("res://scenes/powerups/bomb_powerup.tscn"),
	GameEnums.PowerUpType.SPEED_BOOST: preload("res://scenes/powerups/speed_boost_powerup.tscn")
}

# 道具生成概率配置
@export var drop_chance: float = 0.25  # 提高到25%的几率掉落道具
@export var health_drop_weight: float = 0.25  # 回血道具的权重
@export var triple_shot_weight: float = 0.20  # 三连射道具的权重
@export var auto_aim_weight: float = 0.15  # 自动追踪道具的权重
@export var spread_shot_weight: float = 0.15  # 散射道具的权重
@export var bomb_weight: float = 0.05  # 炸弹道具的权重
@export var speed_boost_weight: float = 0.20  # 速度提升道具的权重

# 道具权重列表
var power_up_weights = []
var total_weight: float = 0.0

# 炸弹道具冷却时间
var bomb_cooldown: float = 60.0  # 60秒冷却时间
var bomb_timer: float = 0.0

# 屏幕尺寸
var screen_width: float = 1280.0
var screen_height: float = 720.0
var sidebar_width: float = 140.0

# 信号
signal powerup_spawned(powerup)

func _ready():
	# 初始化随机数生成器
	randomize()
	
	# 初始化权重列表
	power_up_weights = [
		triple_shot_weight,    # TRIPLE_SHOT
		auto_aim_weight,       # AUTO_AIM
		spread_shot_weight,    # SPREAD_SHOT
		health_drop_weight,    # HEALTH_UP
		bomb_weight,           # BOMB
		speed_boost_weight     # SPEED_BOOST
	]
	
	# 计算总权重
	total_weight = 0.0
	for weight in power_up_weights:
		total_weight += weight
	
	print("PowerupManager: 初始化完成，总权重: ", total_weight)

func _process(delta):
	# 更新炸弹冷却计时器
	if bomb_timer > 0:
		bomb_timer -= delta

# 尝试生成道具
func try_spawn_powerup(position: Vector2) -> bool:
	# 随机决定是否生成道具
	if randf() > drop_chance:
		return false
	
	# 选择要生成的道具类型
	var powerup_type = select_powerup_type()
	
	# 生成选定的道具
	spawn_powerup(powerup_type, position)
	return true

# 选择要生成的道具类型
func select_powerup_type() -> int:
	# 生成随机值
	var random_value = randf() * total_weight
	var cumulative_weight = 0.0
	
	# 根据权重随机选择
	for i in range(power_up_weights.size()):
		cumulative_weight += power_up_weights[i]
		if random_value <= cumulative_weight:
			# 炸弹特殊处理 - 检查冷却时间
			if i == GameEnums.PowerUpType.BOMB and bomb_timer > 0:
				# 如果炸弹在冷却中，改为生成速度提升
				print("炸弹在冷却中，改为生成速度提升")
				return GameEnums.PowerUpType.SPEED_BOOST
			elif i == GameEnums.PowerUpType.BOMB:
				# 重置炸弹冷却计时器
				bomb_timer = bomb_cooldown
				print("炸弹道具已选择，下次掉落冷却时间: " + str(bomb_cooldown) + "秒")
			return i
	
	# 默认返回速度提升
	return GameEnums.PowerUpType.SPEED_BOOST

# 生成指定类型的道具
func spawn_powerup(powerup_type: int, position: Vector2) -> Node:
	# 确保道具类型有效
	if not powerup_scenes.has(powerup_type):
		print("无效的道具类型: " + str(powerup_type))
		return null
	
	# 实例化道具
	var powerup = powerup_scenes[powerup_type].instantiate()
	
	# 设置道具位置
	powerup.global_position = position
	
	# 确保道具在屏幕范围内
	var powerup_size = 18  # 默认道具大小为18像素
	
	# 炸弹和速度提升道具使用更大的尺寸
	if powerup_type == GameEnums.PowerUpType.BOMB or powerup_type == GameEnums.PowerUpType.SPEED_BOOST:
		powerup_size = 43  # 放大后的尺寸约为43像素 (24 * 1.8)
	
	powerup.global_position.x = clamp(powerup.global_position.x, sidebar_width + powerup_size, screen_width - sidebar_width - powerup_size)
	powerup.global_position.y = clamp(powerup.global_position.y, powerup_size, screen_height - powerup_size)
	
	# 发出信号
	emit_signal("powerup_spawned", powerup)
	
	print("生成道具: " + GameEnums.get_powerup_type_name(powerup_type) + " 在位置 " + str(powerup.global_position))
	
	return powerup

# 获取道具场景
func get_powerup_scene(powerup_type: int) -> PackedScene:
	if powerup_scenes.has(powerup_type):
		return powerup_scenes[powerup_type]
	return null

# 设置屏幕尺寸
func set_screen_size(width: float, height: float, sidebar: float):
	screen_width = width
	screen_height = height
	sidebar_width = sidebar 
