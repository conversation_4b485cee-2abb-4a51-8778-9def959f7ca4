@tool
extends "res://scenes/ui/base_icon.gd"

# 心形图标绘制脚本

func _ready():
	# 设置颜色
	base_color = Color(1, 0, 0.2, 1.0)
	background_color = Color(1, 1, 1, 1.0)

func _draw():
	# 调用基类绘制背景
	draw_background()
	
	# 获取绘制参数
	var params = get_draw_params()
	var center_x = params.center_x
	var center_y = params.center_y
	var scale = params.scale
	
	# 心形上半部分（两个圆）
	var radius = scale * 0.5
	var offset = scale * 0.4
	
	# 确定当前颜色（闪烁或正常）
	var heart_color = flash_color if is_flashing else base_color
	
	# 左侧圆
	draw_circle(Vector2(center_x - offset, center_y - offset), radius, heart_color)
	
	# 右侧圆
	draw_circle(Vector2(center_x + offset, center_y - offset), radius, heart_color)
	
	# 心形下半部分（三角形）
	var points = PackedVector2Array([
		Vector2(center_x - scale, center_y - offset * 0.5),
		Vector2(center_x + scale, center_y - offset * 0.5),
		Vector2(center_x, center_y + scale)
	])
	draw_colored_polygon(points, heart_color)

func _notification(what):
	if what == NOTIFICATION_RESIZED:
		queue_redraw() 