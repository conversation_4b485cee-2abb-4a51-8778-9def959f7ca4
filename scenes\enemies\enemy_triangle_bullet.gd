extends Area2D

# 子弹属性
@export var speed: float = 450.0  # 子弹速度，从250增加到450
@export var damage: int = 1  # 子弹伤害

# 移动方向
var direction: Vector2 = Vector2.DOWN  # 默认向下，会被敌人设置为朝向玩家的方向

# 屏幕边界
var viewport_rect: Rect2

func _ready():
	# 获取视口大小用于边界检测
	viewport_rect = get_viewport_rect()
	
	# 根据方向旋转子弹
	rotation = direction.angle() + PI/2  # 因为三角形默认朝下，所以加上90度

func _process(delta):
	# 按方向移动
	position += direction * speed * delta
	
	# 检查是否离开屏幕
	if !viewport_rect.has_point(position):
		queue_free()  # 销毁子弹

# 返回子弹伤害值
func get_damage() -> int:
	return damage

# 当子弹与玩家碰撞时调用
func _on_area_entered(area):
	if area.is_in_group("player"):
		# 对玩家造成伤害
		if area.has_method("take_damage"):
			area.take_damage(damage)
		queue_free()  # 销毁子弹

# 当子弹与玩家物理碰撞体碰撞时调用
func _on_body_entered(body):
	if body.is_in_group("player"):
		# 对玩家造成伤害
		if body.has_method("take_damage"):
			body.take_damage(damage)
		queue_free()  # 销毁子弹 