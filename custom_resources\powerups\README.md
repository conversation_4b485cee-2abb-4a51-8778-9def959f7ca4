# 几何射击 - 技能添加指南

本指南将帮助你向游戏中添加新的技能（道具）。按照以下步骤，你可以轻松地扩展游戏的道具系统。

## 步骤1：更新游戏枚举

首先，在 `scenes/globals/game_enums.gd` 文件中添加新的道具类型：

```gdscript
# 道具类型枚举
enum PowerUpType {
    TRIPLE_SHOT = 0,  # T - 连续射击
    AUTO_AIM = 1,     # A - 追踪子弹
    SPREAD_SHOT = 2,  # S - 散射
    HEALTH_UP = 3,    # + - 回血
    BOMB = 4,         # B - 炸弹
    SPEED_BOOST = 5,  # > - 速度提升
    YOUR_NEW_POWERUP = 6  # 添加你的新道具
}
```

同时，更新获取道具类型名称和资源路径名称的函数：

```gdscript
# 获取道具类型名称
static func get_powerup_type_name(type: int) -> String:
    match type:
        # ... 现有的道具类型 ...
        PowerUpType.YOUR_NEW_POWERUP:
            return "Your New Powerup"
        _:
            return "Unknown"

# 获取道具类型资源路径名称
static func get_powerup_resource_name(type: int) -> String:
    match type:
        # ... 现有的道具类型 ...
        PowerUpType.YOUR_NEW_POWERUP:
            return "your_new_powerup"
        _:
            return "unknown"
```

## 步骤2：创建道具资源目录

在 `custom_resources/powerups/` 目录下创建新的道具资源目录：

```
custom_resources/powerups/your_new_powerup/
```

## 步骤3：创建道具图标

在新创建的目录中添加道具图标。你可以使用SVG或PNG格式：

```
custom_resources/powerups/your_new_powerup/your_new_powerup.svg
```

确保将图标导入到Godot中，并设置适当的导入选项。

## 步骤4：创建道具场景

创建一个新的场景文件，继承自基础道具场景：

1. 在Godot编辑器中，选择 `scenes/powerups/base_power_up.tscn`
2. 点击 "实例化为继承场景"
3. 保存为 `scenes/powerups/your_new_powerup.tscn`
4. 在场景中设置道具类型为你新添加的枚举值

```gdscript
# 在Inspector面板中设置
power_up_type = 6  # 对应YOUR_NEW_POWERUP枚举值
```

## 步骤5：更新PowerupManager

在 `scenes/globals/powerup_manager.gd` 中添加新道具的场景引用：

```gdscript
# 道具场景路径
var powerup_scenes = {
    # ... 现有的道具场景 ...
    GameEnums.PowerUpType.YOUR_NEW_POWERUP: preload("res://scenes/powerups/your_new_powerup.tscn")
}
```

同时，添加新道具的权重配置：

```gdscript
# 道具生成概率配置
@export var your_new_powerup_weight: float = 0.15  # 新道具的权重

# 在_ready函数中更新权重列表
func _ready():
    # 初始化权重列表
    power_up_weights = [
        triple_shot_weight,    # TRIPLE_SHOT
        auto_aim_weight,       # AUTO_AIM
        spread_shot_weight,    # SPREAD_SHOT
        health_drop_weight,    # HEALTH_UP
        bomb_weight,           # BOMB
        speed_boost_weight,    # SPEED_BOOST
        your_new_powerup_weight # YOUR_NEW_POWERUP
    ]
    
    # ... 其余代码 ...
```

## 步骤6：实现道具效果

在 `scenes/player/player.gd` 中添加新道具的状态变量：

```gdscript
# 道具状态
var power_ups = {
    # ... 现有的道具状态 ...
    GameEnums.PowerUpType.YOUR_NEW_POWERUP: 0.0  # 剩余时间
}
var power_ups_active = {
    # ... 现有的道具激活状态 ...
    GameEnums.PowerUpType.YOUR_NEW_POWERUP: false
}
```

然后，在 `apply_power_up` 函数中添加新道具的处理逻辑：

```gdscript
# 应用道具效果
func apply_power_up(power_up_type):
    match power_up_type:
        # ... 现有的道具处理 ...
        GameEnums.PowerUpType.YOUR_NEW_POWERUP:
            # 实现你的道具效果
            power_ups[GameEnums.PowerUpType.YOUR_NEW_POWERUP] = min(power_ups[GameEnums.PowerUpType.YOUR_NEW_POWERUP] + power_up_duration, power_up_max_duration)
            power_ups_active[GameEnums.PowerUpType.YOUR_NEW_POWERUP] = true
            
            # 发送道具状态变化信号
            emit_signal("power_up_status_changed", GameEnums.PowerUpType.YOUR_NEW_POWERUP, true, power_ups[GameEnums.PowerUpType.YOUR_NEW_POWERUP])
            print("激活新道具！持续时间: " + str(power_ups[GameEnums.PowerUpType.YOUR_NEW_POWERUP]))
            
            # 实现特殊效果
            apply_your_new_powerup_effect()
```

最后，添加新道具的特殊效果实现函数：

```gdscript
# 应用新道具效果
func apply_your_new_powerup_effect():
    # 实现你的特殊效果
    pass
```

在 `update_power_ups` 函数中，确保处理新道具的失效逻辑：

```gdscript
# 如果是你的新道具，清除特殊效果
if power_up_type == GameEnums.PowerUpType.YOUR_NEW_POWERUP:
    clear_your_new_powerup_effect()
```

## 步骤7：添加UI支持（可选）

如果你的道具需要在UI中显示状态，可以在 `scenes/ui/power_up_display.gd` 中添加相应的支持。

## 步骤8：测试

最后，启动游戏并测试你的新道具是否正常工作。

## 示例：火力全开道具

以下是添加一个"火力全开"道具的示例，该道具可以临时提高射速：

1. 在 `game_enums.gd` 中添加：
   ```gdscript
   RAPID_FIRE = 6  # F - 火力全开
   ```

2. 创建资源目录和图标：
   ```
   custom_resources/powerups/rapid_fire/rapid_fire.svg
   ```

3. 创建场景：
   ```
   scenes/powerups/rapid_fire_powerup.tscn
   ```

4. 在 `powerup_manager.gd` 中添加：
   ```gdscript
   GameEnums.PowerUpType.RAPID_FIRE: preload("res://scenes/powerups/rapid_fire_powerup.tscn")
   ```

5. 在 `player.gd` 中实现效果：
   ```gdscript
   # 应用火力全开效果
   func apply_rapid_fire_effect():
       # 临时将射速提高到原来的3倍
       fire_rate = base_fire_rate / 3.0
       shoot_timer.wait_time = fire_rate
   
   # 清除火力全开效果
   func clear_rapid_fire_effect():
       # 恢复原始射速
       fire_rate = base_fire_rate
       shoot_timer.wait_time = fire_rate
   ```

祝你开发顺利！ 