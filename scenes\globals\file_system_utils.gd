extends Node

# 文件系统工具类
# 提供通用的文件操作方法

# 支持的图片格式
const SUPPORTED_IMAGE_FORMATS = ["png", "jpg", "jpeg", "webp", "svg"]

# 基础路径
const CUSTOM_RESOURCES_PATH = "res://custom_resources/"
const DEFAULT_RESOURCES_PATH = "res://scenes/"

# 检查文件是否存在
func file_exists(path: String) -> bool:
	return FileAccess.file_exists(path)

# 检查目录是否存在
func dir_exists(path: String) -> bool:
	return DirAccess.dir_exists_absolute(path)

# 创建目录
func create_directory(path: String) -> bool:
	var dir = DirAccess.open("res://")
	if dir:
		var error = dir.make_dir_recursive(path)
		return error == OK
	return false

# 列出目录中的所有文件
func list_files_in_directory(path: String) -> Array:
	var files = []
	var dir = DirAccess.open(path)
	
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		
		while file_name != "":
			if not dir.current_is_dir():
				files.append(file_name)
			file_name = dir.get_next()
		
		dir.list_dir_end()
	
	return files

# 列出目录中的所有子目录
func list_subdirectories(path: String) -> Array:
	var subdirs = []
	var dir = DirAccess.open(path)
	
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		
		while file_name != "":
			if dir.current_is_dir() and file_name != "." and file_name != "..":
				subdirs.append(file_name)
			file_name = dir.get_next()
		
		dir.list_dir_end()
	
	return subdirs

# 查找目录中的第一个图片文件
func find_first_image_in_directory(path: String) -> String:
	var dir = DirAccess.open(path)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		
		while file_name != "":
			if not dir.current_is_dir():
				var extension = file_name.get_extension().to_lower()
				if SUPPORTED_IMAGE_FORMATS.has(extension):
					dir.list_dir_end()
					return path + "/" + file_name
			file_name = dir.get_next()
		
		dir.list_dir_end()
	
	return ""

# 检查文件是否为图片
func is_image_file(file_path: String) -> bool:
	var extension = file_path.get_extension().to_lower()
	return SUPPORTED_IMAGE_FORMATS.has(extension)

# 加载图片为纹理
func load_image_as_texture(path: String) -> Texture2D:
	if not file_exists(path):
		print("FileSystemUtils: 文件不存在 ", path)
		return null
	
	# 如果是资源路径，直接加载
	if path.begins_with("res://"):
		return load(path)
	
	# 否则，加载为图像
	var image = Image.new()
	var error = image.load(path)
	
	if error != OK:
		print("FileSystemUtils: 加载图像错误 ", path, " 错误代码: ", error)
		return null
	
	# 创建纹理
	var texture = ImageTexture.create_from_image(image)
	return texture

# 检查目录中是否有图片
func has_images_in_directory(path: String) -> bool:
	var dir = DirAccess.open(path)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		
		while file_name != "":
			if not dir.current_is_dir():
				var extension = file_name.get_extension().to_lower()
				if SUPPORTED_IMAGE_FORMATS.has(extension):
					dir.list_dir_end()
					return true
			file_name = dir.get_next()
		
		dir.list_dir_end()
	
	return false

# 获取自定义资源路径
func get_custom_resource_path(resource_type: String) -> String:
	return CUSTOM_RESOURCES_PATH + resource_type + "/"

# 获取默认资源路径
func get_default_resource_path(resource_path: String) -> String:
	return DEFAULT_RESOURCES_PATH + resource_path 