@tool
extends TextureRect

# 基础图标类
# 为所有程序化绘制的图标提供通用功能

# 闪烁效果变量
var is_flashing = false
var flash_time = 0.0
var flash_duration = 0.2

# 基础颜色 - 子类可以覆盖
var base_color = Color(1, 1, 1, 1.0)
var outline_color = Color(1, 1, 1, 0.8)
var flash_color = Color(1.0, 1.0, 1.0, 1.0)
var background_color = Color(0.1, 0.1, 0.1, 1.0)

# 绘制参数
var scale_factor = 0.45  # 图标缩放因子

func _ready():
	# 子类可以在这里添加特定的初始化逻辑
	pass

func _process(delta):
	# 处理闪烁效果
	if is_flashing:
		flash_time += delta
		if flash_time >= flash_duration:
			is_flashing = false
		queue_redraw()

# 播放闪烁效果
func play_flash():
	is_flashing = true
	flash_time = 0.0
	queue_redraw()

# 获取绘制区域中心点和缩放比例
func get_draw_params():
	var rect = Rect2(0, 0, size.x, size.y)
	var center_x = size.x / 2
	var center_y = size.y / 2
	var scale = min(size.x, size.y) * scale_factor
	
	return {
		"rect": rect,
		"center_x": center_x,
		"center_y": center_y,
		"scale": scale
	}

# 绘制背景
func draw_background():
	draw_rect(Rect2(0, 0, size.x, size.y), background_color, true)

# 主绘制函数 - 子类应该覆盖此方法
func _draw():
	draw_background()
	# 子类应该在这里实现具体的绘制逻辑

# 当窗口大小改变时重绘
func _notification(what):
	if what == NOTIFICATION_RESIZED:
		queue_redraw() 