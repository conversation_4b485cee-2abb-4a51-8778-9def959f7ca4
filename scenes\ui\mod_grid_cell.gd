extends Panel

# 改装网格单元格脚本
# 处理拖放操作和改装件的显示

# 常量定义
const PART_SCALE_FACTOR = 0.7

# 网格位置
var grid_position: Vector2 = Vector2.ZERO

# 当前放置的改装件
var current_mod = null

# 是否正在悬停
var is_hovering = false

# 对修改界面的引用
var modification_screen = null

# 提示框管理器
var tooltip_manager = null

# 配件数据管理器
var mod_data = null

# 颜色定义
const COLOR_NORMAL = Color(1, 1, 1)
const COLOR_HOVER = Color(1.1, 1.1, 1.1)
const COLOR_DROP_VALID = Color(1.2, 1.2, 0.8)
const COLOR_DROP_INVALID = Color(1.0, 0.6, 0.6)
const COLOR_PLACEMENT_VALID = Color(0.8, 1.0, 0.8, 0.3)

# 初始化
func _ready():
	# 确保可以接收拖放
	mouse_filter = Control.MOUSE_FILTER_STOP
	
	# 连接鼠标进入/离开信号
	mouse_entered.connect(_on_mouse_entered)
	mouse_exited.connect(_on_mouse_exited)
	
	# 延迟加载管理器，确保游戏已完全初始化
	call_deferred("_load_managers")

# 加载管理器
func _load_managers():
	print("网格单元格加载管理器...")
	
	# 等待一帧，确保GameManager已经初始化
	await get_tree().process_frame
	
	# 加载提示框管理器
	tooltip_manager = TooltipManager.get_instance()
	
	# 加载配件数据管理器
	var mod_data_script = load("res://scenes/modifications/data/modification_data_manager.gd")
	mod_data = ModDataManagerLegacy.get_instance()
	
	if tooltip_manager:
		print("网格单元格成功加载提示框管理器")
	else:
		push_error("网格单元格无法加载提示框管理器")
		
	if mod_data:
		print("网格单元格成功加载配件数据管理器")
	else:
		push_error("网格单元格无法加载配件数据管理器")

# 鼠标进入处理
func _on_mouse_entered():
	is_hovering = true
	
	# 如果有改装件，显示提示框
	if current_mod != null:
		show_tooltip()
	# 如果没有改装件，可以显示轻微高亮
	else:
		# 检查是否是有效放置位置
		if modification_screen and modification_screen.can_place_at_position(grid_position):
			modulate = COLOR_HOVER
		else:
			modulate = COLOR_NORMAL

# 鼠标离开处理
func _on_mouse_exited():
	is_hovering = false
	
	# 隐藏提示框
	if tooltip_manager:
		tooltip_manager.hide_tooltip()
	
	# 恢复正常显示
	modulate = COLOR_NORMAL
	
	# 如果是有效放置位置，保持淡色显示
	update_placement_status()

# 显示提示框
func show_tooltip():
	print("尝试显示网格单元格提示框...")
	
	# 检查管理器是否已加载
	if tooltip_manager == null or mod_data == null:
		print("警告: 管理器未初始化，重新加载")
		
		# 重新加载管理器
		tooltip_manager = TooltipManager.get_instance()
		mod_data = ModDataManagerLegacy.get_instance()
	
	# 再次检查
	if tooltip_manager == null:
		push_error("无法加载提示框管理器!")
		return
		
	if mod_data == null:
		push_error("无法加载配件数据管理器!")
		return
		
	if current_mod == null:
		push_error("当前没有配件数据!")
		return
	
	# 获取配件数据
	var mod_resource = mod_data.get_modification_data(current_mod["name"])
	print("获取到配件数据: ", mod_resource.name)
	
	# 显示提示框
	tooltip_manager.show_tooltip(mod_resource.name, mod_resource.description)
	
	print("显示网格配件提示框: ", current_mod["name"])

# 更新放置状态视觉显示
func update_placement_status():
	# 如果已经有改装件或是玩家位置，不需要更新
	if current_mod != null or (grid_position.x == 2 and grid_position.y == 2):
		return
	
	# 检查是否是有效放置位置
	if modification_screen and modification_screen.can_place_at_position(grid_position):
		# 使用淡绿色背景表示可以放置
		modulate = COLOR_PLACEMENT_VALID
	else:
		# 恢复正常显示
		modulate = COLOR_NORMAL

# 检查是否可以接收拖放数据
func _can_drop_data(position, data):
	# 检查是否是改装件数据
	if data is Dictionary and data.has("type") and data["type"] == "modification_part_placeholder":
		# 检查当前格子是否已经有改装件
		if current_mod != null:
			return false
			
		# 检查是否可以放置在这个位置（必须与玩家直接或间接连接）
		if modification_screen and modification_screen.can_place_at_position(grid_position):
			# 高亮显示可放置状态
			modulate = COLOR_DROP_VALID
			return true
		else:
			# 显示不可放置状态
			modulate = COLOR_DROP_INVALID
			return false
	
	return false

# 处理拖放数据
func _drop_data(position, data):
	# 恢复正常显示
	modulate = COLOR_NORMAL
	
	# 处理放置操作
	if data.has("name") and data.has("color"):
		# 创建改装件显示
		place_modification(data["name"], data["color"])
		
		# 记录格子被占据
		if modification_screen:
			modification_screen.mark_cell_occupied(grid_position, {
				"name": data["name"],
				"color": data["color"]
			})
			
			# 更新网格可放置状态
			modification_screen.update_grid_placement_visuals()
		
		# 根据来源类型处理不同的情况
		if data.has("source_type"):
			# 如果是从物品栏拖过来的，删除物品栏中的原始道具
			if data["source_type"] == "inventory" and data.has("source_item"):
				# 获取源物品并删除
				var source_item = data["source_item"]
				if is_instance_valid(source_item) and source_item.has_method("remove_from_inventory"):
					source_item.remove_from_inventory()
					print("已从物品栏移除原始道具: " + data["name"])

				# 同时从改装仓库的物品列表中移除
				if modification_screen and modification_screen.has_method("remove_from_inventory_list"):
					modification_screen.remove_from_inventory_list(data["name"])
			# 如果是从网格中其他位置拖过来的，网格原位置的道具已经被handle_mod_drag处理
			elif data["source_type"] == "grid":
				print("道具已从其他网格位置移动到此处")
		
		# 清除全局拖拽跟踪器，因为道具已成功放置
		if get_tree().root.has_node("GlobalDragTracker"):
			get_tree().root.get_node("GlobalDragTracker").queue_free()
		
		# 打印放置信息
		print("放置 [" + data["name"] + "] 到网格格子 " + str(grid_position))
	
	# 确保鼠标移开也能恢复正常颜色
	is_hovering = false

# 放置改装件显示
func place_modification(mod_name: String, mod_color: Color):
	# 清除现有内容
	for child in get_children():
		child.queue_free()
	
	# 创建一个容器
	var container = VBoxContainer.new()
	container.size_flags_horizontal = Control.SIZE_FILL
	container.size_flags_vertical = Control.SIZE_FILL
	container.alignment = BoxContainer.ALIGNMENT_CENTER
	container.mouse_filter = Control.MOUSE_FILTER_PASS  # 确保鼠标事件传递给父节点
	
	# 创建图标
	var icon = ColorRect.new()
	icon.color = mod_color
	icon.custom_minimum_size = Vector2(80, 80)
	icon.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	icon.size_flags_vertical = Control.SIZE_SHRINK_CENTER
	icon.mouse_filter = Control.MOUSE_FILTER_PASS  # 确保鼠标事件传递给父节点
	
	# 创建标签
	var label = Label.new()
	label.text = mod_name
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART  # 自动换行
	label.custom_minimum_size = Vector2(40, 0)  # 限制宽度，允许高度自适应
	label.mouse_filter = Control.MOUSE_FILTER_PASS  # 确保鼠标事件传递给父节点
	
	# 调整字体大小
	var font_size = 10  # 更小的字体
	var font = label.get_theme_font("font")
	if font:
		label.add_theme_font_size_override("font_size", font_size)
	
	# 组装界面
	container.add_child(icon)
	container.add_child(label)
	add_child(container)
	
	# 记录当前改装件
	current_mod = {
		"name": mod_name,
		"color": mod_color
	}
	
	# 重置颜色
	modulate = COLOR_NORMAL

# 清除当前改装件
func clear_modification():
	for child in get_children():
		child.queue_free()
	current_mod = null
	
	# 更新占据状态
	if modification_screen:
		modification_screen.clear_cell_occupied(grid_position)
		
		# 更新网格可放置状态
		modification_screen.update_grid_placement_visuals()
	
	# 更新自身状态
	update_placement_status()

# 静默清除改装件（不更新全局状态）
func clear_modification_silent():
	for child in get_children():
		child.queue_free()
	current_mod = null
	
	# 更新自身状态
	update_placement_status()

# 获取拖拽数据
func _get_drag_data(position):
	# 如果当前没有改装件，不能拖拽
	if current_mod == null:
		return null
		
	# 创建拖拽数据字典
	var drag_data = {
		"type": "modification_part_placeholder",
		"name": current_mod["name"],
		"color": current_mod["color"],
		"source_cell": self,  # 记录源单元格，用于后续处理
		"grid_position": grid_position,
		"source_type": "grid"  # 标记来源为网格
	}
	
	# 创建拖拽预览
	var preview = create_drag_preview()
	set_drag_preview(preview)
	
	# 创建全局拖拽跟踪器，用于捕获未成功放置的拖拽操作
	if not get_tree().root.has_node("GlobalDragTracker"):
		var tracker = Node.new()
		tracker.name = "GlobalDragTracker"
		tracker.set_meta("drag_data", drag_data)
		get_tree().root.add_child(tracker)
		print("创建全局拖拽跟踪器: [" + current_mod["name"] + "]")
	
	# 检查是否会导致其他改装件断开连接
	var will_disconnect_others = false
	if modification_screen and modification_screen.has_method("handle_mod_drag"):
		# 第二个参数设置为true，表示不要清除被拖拽的道具
		will_disconnect_others = modification_screen.handle_mod_drag(grid_position, true)
	
	# 从格子中移除改装件，但保持拖拽数据
	clear_modification()
	
	print("从网格格子 " + str(grid_position) + " 拖出: [" + drag_data["name"] + "]")
	return drag_data

# 创建拖拽预览
func create_drag_preview():
	# 确保有改装件可以预览
	if current_mod == null:
		return null
		
	# 创建一个控制节点作为预览容器
	var preview_container = Panel.new()
	preview_container.custom_minimum_size = Vector2(140, 140)
	
	# 设置半透明效果
	preview_container.modulate = Color(1, 1, 1, 0.7)
	
	# 创建图标
	var icon = ColorRect.new()
	icon.color = current_mod["color"]
	icon.custom_minimum_size = Vector2(80, 80)
	icon.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	icon.size_flags_vertical = Control.SIZE_SHRINK_CENTER
	
	# 创建标签
	var label = Label.new()
	label.text = current_mod["name"]
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.vertical_alignment = VERTICAL_ALIGNMENT_BOTTOM
	
	# 创建容器布局
	var vbox = VBoxContainer.new()
	vbox.add_child(icon)
	vbox.add_child(label)
	vbox.alignment = BoxContainer.ALIGNMENT_CENTER
	
	# 添加到预览容器
	preview_container.add_child(vbox)
	
	vbox.anchors_preset = Control.PRESET_FULL_RECT
	vbox.offset_right = 0
	vbox.offset_bottom = 0
	
	return preview_container 
