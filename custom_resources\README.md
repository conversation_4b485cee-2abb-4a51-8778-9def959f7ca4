# 自定义资源系统使用说明

本游戏支持通过简单地替换图片文件来自定义游戏中的视觉元素。无需修改代码，只需将您的图片放入对应的文件夹中即可。

## 文件夹结构

```
custom_resources/
  ├── health/        # 血量图标
  ├── player/        # 玩家模型
  ├── enemies/       # 敌人模型
  ├── bullets/       # 子弹模型
  └── powerups/      # 道具模型
      ├── triple_shot/   # 三连射道具
      ├── auto_aim/      # 自动追踪道具
      ├── spread_shot/   # 散射道具
      ├── health_up/     # 回血道具
      └── bomb/          # 炸弹道具
```

## 使用方法

1. 将您想要的图片文件（PNG、JPG、JPEG或WEBP格式）放入对应的文件夹中
2. 游戏将自动加载并使用这些图片，替换默认的游戏元素
3. 如果文件夹为空或不存在，游戏将使用内置的默认资源

## 示例

- 要替换血量图标，将您的心形图标图片放入 `custom_resources/health/` 文件夹
- 要替换玩家模型，将您的玩家图片放入 `custom_resources/player/` 文件夹
- 要替换敌人模型，将您的敌人图片放入 `custom_resources/enemies/` 文件夹
- 要替换子弹模型，将您的子弹图片放入 `custom_resources/bullets/` 文件夹
- 要替换道具模型：
  - 三连射道具：将图片放入 `custom_resources/powerups/triple_shot/` 文件夹
  - 自动追踪道具：将图片放入 `custom_resources/powerups/auto_aim/` 文件夹
  - 散射道具：将图片放入 `custom_resources/powerups/spread_shot/` 文件夹
  - 回血道具：将图片放入 `custom_resources/powerups/health_up/` 文件夹
  - 炸弹道具：将图片放入 `custom_resources/powerups/bomb/` 文件夹

## 注意事项

- 如果一个文件夹中有多个图片，只会使用找到的第一个图片
- 图片大小会自动调整以适应游戏需要，但为了最佳效果，建议使用以下尺寸：
  - 血量图标：32x32像素
  - 玩家模型：32x32像素
  - 敌人模型：32x32像素
  - 子弹模型：16x16像素
  - 道具模型：24x24像素
- 透明背景的PNG图片效果最佳 