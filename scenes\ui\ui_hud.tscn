[gd_scene load_steps=8 format=3]

[ext_resource type="Script" path="res://scenes/ui/ui_hud.gd" id="1_1qwlb"]
[ext_resource type="PackedScene" path="res://scenes/ui/health_display.tscn" id="2_8ynlq"]
[ext_resource type="PackedScene" path="res://scenes/ui/shield_display.tscn" id="3_n2xmb"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_h2hnj"]
bg_color = Color(0.0784314, 0.0784314, 0.0784314, 0.784314)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8dj2k"]
bg_color = Color(0.141176, 0.686275, 0.945098, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uh7ew"]
bg_color = Color(0.133333, 0.133333, 0.133333, 0.686275)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.4, 0.4, 0.4, 1)
corner_radius_top_left = 12
corner_radius_top_right = 12
corner_radius_bottom_right = 12
corner_radius_bottom_left = 12

[sub_resource type="RectangleShape2D" id="RectangleShape2D_jts4r"]
size = Vector2(140, 720)

[node name="UI_HUD" type="CanvasLayer"]
script = ExtResource("1_1qwlb")

[node name="LeftSidebar" type="Panel" parent="."]
anchors_preset = 9
anchor_bottom = 1.0
offset_right = 280.0
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_h2hnj")

[node name="LeftSidebarCollider" type="StaticBody2D" parent="LeftSidebar"]
collision_layer = 4
collision_mask = 7

[node name="CollisionShape2D" type="CollisionShape2D" parent="LeftSidebar/LeftSidebarCollider"]
position = Vector2(140, 360)
shape = SubResource("RectangleShape2D_jts4r")

[node name="ScoreLabel" type="Label" parent="LeftSidebar"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_top = 20.0
offset_bottom = 46.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 28
text = "SCORE: 0"
horizontal_alignment = 1

[node name="DistanceLabel" type="Label" parent="LeftSidebar"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_top = 60.0
offset_bottom = 86.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 28
text = "DIST: 0 M"
horizontal_alignment = 1

[node name="PowerupContainer" type="VBoxContainer" parent="LeftSidebar"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -300.0
grow_horizontal = 2
grow_vertical = 0
theme_override_constants/separation = 20

[node name="PowerupLabel" type="Label" parent="LeftSidebar/PowerupContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 28
text = "POWER-UPS"
horizontal_alignment = 1

[node name="TripleShotIcon" type="Panel" parent="LeftSidebar/PowerupContainer"]
custom_minimum_size = Vector2(220, 60)
layout_mode = 2
size_flags_horizontal = 4

[node name="Background" type="ColorRect" parent="LeftSidebar/PowerupContainer/TripleShotIcon"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 10.0
offset_top = -25.0
offset_right = 60.0
offset_bottom = 25.0
grow_vertical = 2
color = Color(0, 0.498039, 1, 1)

[node name="Letter" type="Label" parent="LeftSidebar/PowerupContainer/TripleShotIcon"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 10.0
offset_top = -25.0
offset_right = 60.0
offset_bottom = 25.0
grow_vertical = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 28
text = "T"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="LeftSidebar/PowerupContainer/TripleShotIcon"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -15.0
offset_right = -10.0
offset_bottom = 15.0
grow_horizontal = 0
grow_vertical = 2
theme_override_styles/fill = SubResource("StyleBoxFlat_8dj2k")
value = 50.0

[node name="AutoAimIcon" type="Panel" parent="LeftSidebar/PowerupContainer"]
custom_minimum_size = Vector2(120, 40)
layout_mode = 2
size_flags_horizontal = 4

[node name="Background" type="ColorRect" parent="LeftSidebar/PowerupContainer/AutoAimIcon"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 5.0
offset_top = -15.0
offset_right = 35.0
offset_bottom = 15.0
grow_vertical = 2
color = Color(0.701961, 0.270588, 1, 1)

[node name="Letter" type="Label" parent="LeftSidebar/PowerupContainer/AutoAimIcon"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 5.0
offset_top = -15.0
offset_right = 35.0
offset_bottom = 15.0
grow_vertical = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 16
text = "A"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="LeftSidebar/PowerupContainer/AutoAimIcon"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -80.0
offset_top = -10.0
offset_right = -5.0
offset_bottom = 10.0
grow_horizontal = 0
grow_vertical = 2
theme_override_styles/fill = SubResource("StyleBoxFlat_8dj2k")
value = 50.0

[node name="SpreadShotIcon" type="Panel" parent="LeftSidebar/PowerupContainer"]
custom_minimum_size = Vector2(120, 40)
layout_mode = 2
size_flags_horizontal = 4

[node name="Background" type="ColorRect" parent="LeftSidebar/PowerupContainer/SpreadShotIcon"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 5.0
offset_top = -15.0
offset_right = 35.0
offset_bottom = 15.0
grow_vertical = 2
color = Color(1, 0.6, 0, 1)

[node name="Letter" type="Label" parent="LeftSidebar/PowerupContainer/SpreadShotIcon"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 5.0
offset_top = -15.0
offset_right = 35.0
offset_bottom = 15.0
grow_vertical = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 16
text = "S"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="LeftSidebar/PowerupContainer/SpreadShotIcon"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -80.0
offset_top = -10.0
offset_right = -5.0
offset_bottom = 10.0
grow_horizontal = 0
grow_vertical = 2
theme_override_styles/fill = SubResource("StyleBoxFlat_8dj2k")
value = 50.0

[node name="SpeedBoostIcon" type="Panel" parent="LeftSidebar/PowerupContainer"]
custom_minimum_size = Vector2(220, 60)
layout_mode = 2
size_flags_horizontal = 4

[node name="Background" type="ColorRect" parent="LeftSidebar/PowerupContainer/SpeedBoostIcon"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 10.0
offset_top = -25.0
offset_right = 60.0
offset_bottom = 25.0
grow_vertical = 2
color = Color(0.2, 1, 0.4, 1)

[node name="Letter" type="Label" parent="LeftSidebar/PowerupContainer/SpeedBoostIcon"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 10.0
offset_top = -25.0
offset_right = 60.0
offset_bottom = 25.0
grow_vertical = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 28
text = "B"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="LeftSidebar/PowerupContainer/SpeedBoostIcon"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -15.0
offset_right = -10.0
offset_bottom = 15.0
grow_horizontal = 0
grow_vertical = 2
theme_override_styles/fill = SubResource("StyleBoxFlat_8dj2k")
value = 50.0

[node name="RightSidebar" type="Panel" parent="."]
anchors_preset = 11
anchor_left = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -280.0
grow_horizontal = 0
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_h2hnj")

[node name="RightSidebarCollider" type="StaticBody2D" parent="RightSidebar"]
collision_layer = 4
collision_mask = 7

[node name="CollisionShape2D" type="CollisionShape2D" parent="RightSidebar/RightSidebarCollider"]
position = Vector2(140, 360)
shape = SubResource("RectangleShape2D_jts4r")

[node name="MarginContainer" type="MarginContainer" parent="RightSidebar"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 20

[node name="BottomContainer" type="VBoxContainer" parent="RightSidebar/MarginContainer"]
layout_mode = 2
size_flags_vertical = 8
theme_override_constants/separation = 20
alignment = 2

[node name="ShieldDisplay" parent="RightSidebar/MarginContainer/BottomContainer" instance=ExtResource("3_n2xmb")]
layout_mode = 2
size_flags_vertical = 8
custom_minimum_size = Vector2(0, 50)

[node name="HealthDisplay" parent="RightSidebar/MarginContainer/BottomContainer" instance=ExtResource("2_8ynlq")]
layout_mode = 2
size_flags_vertical = 8
custom_minimum_size = Vector2(0, 50)

[node name="GameOverContainer" type="CenterContainer" parent="."]
visible = false
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="PanelContainer" type="PanelContainer" parent="GameOverContainer"]
custom_minimum_size = Vector2(400, 300)
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_uh7ew")

[node name="MarginContainer" type="MarginContainer" parent="GameOverContainer/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 20
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 20

[node name="VBoxContainer" type="VBoxContainer" parent="GameOverContainer/PanelContainer/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 15
alignment = 1

[node name="GameOverLabel" type="Label" parent="GameOverContainer/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 32
text = "GAME OVER"
horizontal_alignment = 1

[node name="FinalScoreLabel" type="Label" parent="GameOverContainer/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "FINAL SCORE: 0"
horizontal_alignment = 1

[node name="FinalDistanceLabel" type="Label" parent="GameOverContainer/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "DIST: 0 M"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="GameOverContainer/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="RetryButton" type="Button" parent="GameOverContainer/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 20
text = "RETRY"

[connection signal="pressed" from="GameOverContainer/PanelContainer/MarginContainer/VBoxContainer/RetryButton" to="." method="_on_retry_button_pressed"] 