extends Node2D

# 主游戏场景脚本

# 预加载游戏枚举
const GameEnums = preload("res://scenes/globals/game_enums.gd")

# 设计分辨率
const DESIGN_WIDTH: int = 2560
const DESIGN_HEIGHT: int = 1440

# 侧边栏宽度
const SIDEBAR_WIDTH: int = 280

# 屏幕边界
var screen_width: float
var screen_height: float
var game_area_width: float  # 实际游戏区域宽度（减去侧边栏）

# 游戏状态
var score: int = 0
var distance: float = 0.0
var distance_timer: float = 0.0
var distance_update_rate: float = 1.0  # 每秒更新距离
var game_over: bool = false
var pause_allowed: bool = true  # 是否允许暂停

# 组件引用
@onready var player = $Player
@onready var ui_hud = $UI_HUD
@onready var modification_screen = $UILayer/ModificationScreen

# 调试信息
var debug_enabled: bool = true  # 是否启用调试
var last_special_wave_check: float = 0.0  # 上次检查特殊波次的时间

# 防止重复处理道具
var processed_powerups = {}

func _ready():
	# 初始化输入映射
	var autoloader = load("res://scenes/globals/autoload.gd").new()
	add_child(autoloader)
	
	# 设置窗口大小
	DisplayServer.window_set_size(Vector2i(DESIGN_WIDTH, DESIGN_HEIGHT))
	
	# 设置窗口标题
	DisplayServer.window_set_title("几何射击 (Geometric Shooter)")
	
	# 设置窗口居中
	var screen_size = DisplayServer.screen_get_size()
	var window_size = DisplayServer.window_get_size()
	var centered_position = (screen_size - window_size) / 2
	DisplayServer.window_set_position(centered_position)
	
	# 设置窗口拉伸模式
	# 这将确保游戏内容在不同分辨率下保持正确的比例
	# 使用 "keep_aspect" 模式，保持设计分辨率的宽高比
	get_tree().root.content_scale_mode = Window.CONTENT_SCALE_MODE_VIEWPORT
	get_tree().root.content_scale_aspect = Window.CONTENT_SCALE_ASPECT_KEEP
	
	# 通知背景更新屏幕大小
	var starfield = $Starfield
	if starfield:
		starfield.update_screen_size() 
	
	# 获取屏幕尺寸
	var viewport_rect = get_viewport_rect()
	screen_width = viewport_rect.size.x
	screen_height = viewport_rect.size.y
	
	# 计算实际游戏区域宽度（减去两侧的侧边栏）
	game_area_width = screen_width - (SIDEBAR_WIDTH * 2)
	
	# 调整玩家初始位置到新的游戏区域中心
	player.position.x = screen_width / 2
	player.position.y = 1200  # 调整到屏幕下方合适位置
	
	# 设置健康显示组件的最大生命值
	var health_display = ui_hud.get_health_display()
	if health_display:
		health_display.set_max_health(player.hp)
		health_display.update_health(player.hp)
	
	# 设置护盾显示组件的最大护盾值
	var shield_display = ui_hud.get_shield_display()
	if shield_display:
		shield_display.set_max_shield(player.max_shield_points)
		shield_display.update_shield(player.shield_points)
	
	# 连接玩家信号
	player.connect("health_changed", _on_player_health_changed)
	player.connect("player_died", _on_player_died)
	player.connect("power_up_status_changed", _on_player_power_up_status_changed)
	player.connect("shield_changed", _on_player_shield_changed)
	
	# 重置波次管理器
	WaveManager.reset()
	
	# 重置作弊管理器
	if CheatManager != null:
		CheatManager.reset_cheats()
	
	# 连接波次管理器的敌人生成信号
	WaveManager.connect("enemy_spawned", _on_wave_manager_enemy_spawned)
	
	# 设置PowerupManager的屏幕尺寸
	if PowerupManager:
		PowerupManager.set_screen_size(screen_width, screen_height, SIDEBAR_WIDTH)
		PowerupManager.connect("powerup_spawned", _on_powerup_spawned)
	
	# 连接现有道具的信号
	connect_existing_powerups()
	
	# 初始化改装界面连接
	if modification_screen:
		modification_screen.modification_screen_opened.connect(_on_modification_screen_opened)
		modification_screen.modification_screen_closed.connect(_on_modification_screen_closed)
		
	# 确保改装界面使用最新设置
	call_deferred("ensure_modification_screen_is_updated")

func _process(delta):
	if game_over:
		return
	
	# 检测修改界面输入 - B键打开和关闭修改界面
	if Input.is_action_just_pressed("ui_b"):
		if modification_screen:
			modification_screen.toggle()
	
	# 更新距离计数器
	distance_timer += delta
	if distance_timer >= distance_update_rate:
		distance_timer = 0
		distance += 1
		ui_hud.set_distance(distance)
	
	# 更新波次管理器的距离
	WaveManager.set_distance(distance)
	
	# 调试信息 - 每5秒打印一次特殊波次系统的状态
	if debug_enabled:
		if Time.get_ticks_msec() - last_special_wave_check > 5000:  # 每5秒
			last_special_wave_check = Time.get_ticks_msec()
			print("--- 特殊波次系统状态 ---")
			print("当前游戏时间: " + str(WaveManager.game_time) + " 秒")
			print("特殊波次系统启用: " + str(WaveManager.special_wave_enabled))
			print("下一个特殊波次时间: " + str(WaveManager.next_special_wave_time) + " 秒")
			print("特殊波次间隔: " + str(WaveManager.special_wave_interval) + " 秒")
			print("特殊波次数量: " + str(WaveManager.special_waves.size()))
			print("基础敌人生成计时器: " + str(WaveManager.base_enemy_timer))
			print("活跃脚本化波次数量: " + str(WaveManager.active_scripted_waves.size()))
			print("------------------------")

# 处理波次管理器生成的敌人
func _on_wave_manager_enemy_spawned(enemy):
	# 添加敌人到场景
	add_child(enemy)
	
	# 连接敌人死亡信号
	enemy.connect("enemy_died", _on_enemy_died)

# 处理敌人死亡
func _on_enemy_died(enemy_score):
	# 增加分数
	score += enemy_score
	print("Score: ", score)
	
	# 更新HUD分数
	ui_hud.add_score(enemy_score)

# 处理玩家血量变化
func _on_player_health_changed(new_health):
	# 更新UI中的血量显示
	ui_hud.update_health_display(new_health)

# 处理玩家死亡
func _on_player_died():
	# 设置游戏结束状态
	game_over = true
	
	# 通知波次管理器游戏结束
	WaveManager.set_game_over()
	
	# 减慢游戏速度（可选）
	Engine.time_scale = 0.5
	
	# 延迟调用游戏结束函数，给死亡动画时间播放
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = 1.0
	timer.one_shot = true
	timer.timeout.connect(func(): show_game_over())
	timer.start()

# 显示游戏结束界面
func show_game_over():
	# 恢复正常游戏速度
	Engine.time_scale = 1.0
	
	# 显示游戏结束界面
	ui_hud.show_game_over()

# 处理道具收集
func _on_power_up_collected(power_up_type):
	# 生成唯一ID（基于时间戳）
	var unique_id = str(Time.get_ticks_msec())
	
	# 检查是否已处理过此道具
	if processed_powerups.has(unique_id):
		print("警告：道具已被处理，忽略重复信号")
		return
		
	# 标记为已处理
	processed_powerups[unique_id] = true
	
	# 延迟清理处理记录，避免内存泄漏
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = 1.0
	timer.one_shot = true
	timer.timeout.connect(func(): processed_powerups.erase(unique_id); timer.queue_free())
	timer.start()
	
	print("主游戏场景收到道具收集信号，类型: ", power_up_type, " ID: ", unique_id)
	
	# 将道具效果应用到玩家
	if player:
		player.apply_power_up(power_up_type)
	else:
		print("错误：找不到玩家节点")

# 处理玩家道具状态变化
func _on_player_power_up_status_changed(type, active, time_left):
	# 更新UI中的道具状态显示
	ui_hud.update_powerup_status(type, active, time_left)

# 处理玩家护盾变化
func _on_player_shield_changed(new_shield, max_shield):
	# 更新UI中的护盾显示
	ui_hud.update_shield_display(new_shield, max_shield)

# 连接现有道具的信号
func connect_existing_powerups():
	# 查找场景中所有道具
	var powerups = get_tree().get_nodes_in_group("powerups")
	for powerup in powerups:
		# 检查是否已经连接了信号
		if not powerup.is_connected("power_up_collected", _on_power_up_collected):
			# 连接信号
			powerup.connect("power_up_collected", _on_power_up_collected)
			print("已连接道具信号: " + powerup.get_power_up_type_name())

# 处理PowerupManager生成的道具
func _on_powerup_spawned(powerup):
	# 添加道具到场景
	add_child(powerup)
	
	# 连接道具信号
	if not powerup.is_connected("power_up_collected", _on_power_up_collected):
		powerup.connect("power_up_collected", _on_power_up_collected)
		print("已连接PowerupManager生成的道具信号: " + powerup.get_power_up_type_name())

# 处理改装界面打开
func _on_modification_screen_opened():
	# 改装界面打开后的处理
	print("改装界面已打开")

# 处理改装界面关闭
func _on_modification_screen_closed():
	# 改装界面关闭后的处理
	print("改装界面已关闭")

# 脚本的末尾添加一个新函数
func ensure_modification_screen_is_updated():
	if modification_screen:
		print("正在更新主游戏中的修改界面...")
		# 确保修改界面组已经添加到正确的组
		add_to_group("main_game")
		
		# 延迟更新改装界面
		var timer = Timer.new()
		add_child(timer)
		timer.wait_time = 1.0
		timer.one_shot = true
		timer.timeout.connect(func(): 
			# 更新改装界面设置
			if ui_hud:
				ui_hud.ensure_modification_screen_updated()
			timer.queue_free()
		)
		timer.start()
