[gd_resource type="GradientTexture2D" load_steps=2 format=3 uid="uid://cqbr5a5qvdq5l"]

[sub_resource type="Gradient" id="Gradient_ioqn4"]
offsets = PackedFloat32Array(0, 1)
colors = PackedColorArray(1, 0.2, 0.2, 1, 1, 0.2, 0.2, 0.2)

[resource]
gradient = SubResource("Gradient_ioqn4")
width = 32
height = 32
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(0.5, 0)
metadata/_generator_type = &"enemy_triangle" 