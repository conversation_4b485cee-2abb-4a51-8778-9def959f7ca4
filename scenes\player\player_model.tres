[gd_resource type="GradientTexture2D" load_steps=2 format=3 uid="uid://b4o3bjy1qwlhj"]

[sub_resource type="Gradient" id="Gradient_2xgvn"]
offsets = PackedFloat32Array(0, 1)
colors = PackedColorArray(0, 0.498039, 1, 1, 0, 0.2, 0.8, 1)

[resource]
gradient = SubResource("Gradient_2xgvn")
width = 32
height = 32
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(0.5, 0)
metadata/_generator_type = &"player" 