extends Area2D

# 子弹属性
@export var speed: float = GameConstants.PLAYER_BULLET_SPEED  # 使用游戏常量中定义的子弹速度
@export var damage: int = 1  # 子弹伤害，GDD中定义为1点
var damage_multiplier: float = 1.0  # 伤害倍率，由改装系统提供

# 追踪相关
var is_homing: bool = false  # 是否为追踪子弹
var homing_strength: float = 5.0  # 追踪强度（从3.0增强到5.0）
var max_turn_rate: float = 0.08  # 最大转向速率（从0.05增强到0.08）
var direction: Vector2 = Vector2(0, -1)  # 子弹方向，默认向上

# 屏幕边界参考
var viewport_rect: Rect2
var target_enemy = null  # 追踪的目标敌人

func _ready():
	# 获取视口大小用于边界检测
	viewport_rect = get_viewport_rect()
	
	# 如果是追踪子弹，找到最近的敌人作为目标
	if is_homing:
		find_target()
	
	# 设置初始旋转以匹配移动方向
	update_visual_rotation()

# 设置伤害倍率
func set_damage_multiplier(multiplier: float):
	damage_multiplier = multiplier
	# 打印调试信息
	if multiplier > 1.0:
		print("子弹伤害倍率设置为: x" + str(damage_multiplier))

func _process(delta):
	if is_homing:
		# 追踪逻辑
		track_enemy(delta)
	
	# 根据方向移动
	position += direction * speed * delta
	
	# 更新子弹视觉旋转
	update_visual_rotation()
	
	# 检查是否离开屏幕
	if position.y < viewport_rect.position.y - 20 or \
	   position.y > viewport_rect.end.y + 20 or \
	   position.x < viewport_rect.position.x - 20 or \
	   position.x > viewport_rect.end.x + 20:
		queue_free()  # 销毁子弹

# 更新子弹视觉旋转，使其与飞行方向一致
func update_visual_rotation():
	# 设置子弹旋转以匹配移动方向
	# 假设子弹默认朝上（+Y轴负方向），需要加上90度偏移
	rotation = direction.angle() + PI/2

# 启用追踪功能
func enable_homing():
	is_homing = true
	find_target()

# 查找最近的敌人作为追踪目标
func find_target():
	var enemies = get_tree().get_nodes_in_group("enemies")
	
	# 如果没有敌人，不追踪
	if enemies.size() == 0:
		return
	
	var closest_enemy = null
	var closest_distance = INF
	
	# 找到最近的敌人
	for enemy in enemies:
		var distance = position.distance_to(enemy.position)
		if distance < closest_distance:
			closest_distance = distance
			closest_enemy = enemy
	
	target_enemy = closest_enemy

# 追踪敌人
func track_enemy(delta):
	# 如果目标不存在或已被销毁，尝试找新目标
	if not is_instance_valid(target_enemy):
		find_target()
		if not is_instance_valid(target_enemy):
			return  # 如果没有找到新目标，保持当前方向
	
	# 计算朝向目标的方向
	var target_direction = (target_enemy.position - position).normalized()
	
	# 平滑转向目标方向
	var turn_amount = min(homing_strength * delta, max_turn_rate)
	direction = direction.lerp(target_direction, turn_amount).normalized()
	
	# 子弹旋转在update_visual_rotation中处理

# 当子弹与敌人碰撞时调用
func _on_area_entered(area):
	# 如果碰到敌人，造成伤害并销毁自己
	if area.is_in_group("enemies"):
		if area.has_method("take_damage"):
			# 应用伤害倍率
			var final_damage = round(damage * damage_multiplier)
			area.take_damage(final_damage)
		queue_free() 