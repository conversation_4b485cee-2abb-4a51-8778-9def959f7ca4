[gd_resource type="GradientTexture2D" load_steps=2 format=3 uid="uid://bvw5kcfhbk7ej"]

[sub_resource type="Gradient" id="Gradient_qkx1h"]
offsets = PackedFloat32Array(0, 1)
colors = PackedColorArray(1, 0.3, 0.3, 1, 0.8, 0.2, 0.2, 0.8)

[resource]
gradient = SubResource("Gradient_qkx1h")
width = 16
height = 16
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(0.5, 0)
metadata/_generator_type = &"enemy_bullet" 