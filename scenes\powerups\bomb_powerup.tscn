[gd_scene load_steps=5 format=3 uid="uid://c8g6jvqnvfgxp"]

[ext_resource type="Script" uid="uid://nxdcr3qs0yge" path="res://scenes/powerups/base_power_up.gd" id="1_bw1a7"]

[sub_resource type="CircleShape2D" id="CircleShape2D_qc4lj"]
radius = 24.0

[sub_resource type="Animation" id="Animation_tnkv6"]
resource_name = "collect"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SpriteContainer:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(0.5, 2),
"update": 0,
"values": [Vector2(1, 1), Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SpriteContainer:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1aq8h"]
_data = {
&"collect": SubResource("Animation_tnkv6")
}

[node name="BombPowerUp" type="Area2D" groups=["power_up"]]
collision_layer = 8
collision_mask = 2
script = ExtResource("1_bw1a7")
power_up_type = 4
lifetime = 8.0

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_qc4lj")

[node name="SpriteContainer" type="Node2D" parent="."]

[node name="SeaMine" type="Node2D" parent="SpriteContainer"]

[node name="MainBody" type="Polygon2D" parent="SpriteContainer/SeaMine"]
color = Color(0.4, 0.4, 0.4, 1)
polygon = PackedVector2Array(0, -20, 14, -14, 20, 0, 14, 14, 0, 20, -14, 14, -20, 0, -14, -14)

[node name="Center" type="Polygon2D" parent="SpriteContainer/SeaMine"]
color = Color(0.2, 0.2, 0.2, 1)
polygon = PackedVector2Array(0, -10, 7, -7, 10, 0, 7, 7, 0, 10, -7, 7, -10, 0, -7, -7)

[node name="Spike1" type="Polygon2D" parent="SpriteContainer/SeaMine"]
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -20, 4, -26, 0, -32, -4, -26)

[node name="Spike2" type="Polygon2D" parent="SpriteContainer/SeaMine"]
rotation = 0.785398
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -20, 4, -26, 0, -32, -4, -26)

[node name="Spike3" type="Polygon2D" parent="SpriteContainer/SeaMine"]
rotation = 1.5708
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -20, 4, -26, 0, -32, -4, -26)

[node name="Spike4" type="Polygon2D" parent="SpriteContainer/SeaMine"]
rotation = 2.35619
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -20, 4, -26, 0, -32, -4, -26)

[node name="Spike5" type="Polygon2D" parent="SpriteContainer/SeaMine"]
rotation = 3.14159
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -20, 4, -26, 0, -32, -4, -26)

[node name="Spike6" type="Polygon2D" parent="SpriteContainer/SeaMine"]
rotation = 3.92699
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -20, 4, -26, 0, -32, -4, -26)

[node name="Spike7" type="Polygon2D" parent="SpriteContainer/SeaMine"]
rotation = 4.71239
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -20, 4, -26, 0, -32, -4, -26)

[node name="Spike8" type="Polygon2D" parent="SpriteContainer/SeaMine"]
rotation = 5.49779
color = Color(0.3, 0.3, 0.3, 1)
polygon = PackedVector2Array(0, -20, 4, -26, 0, -32, -4, -26)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_1aq8h")
}

[node name="LifetimeTimer" type="Timer" parent="."]
wait_time = 10.0
one_shot = true
autostart = true

[node name="BlinkTimer" type="Timer" parent="."]
wait_time = 0.2

[connection signal="area_entered" from="." to="." method="_on_area_entered"]
[connection signal="timeout" from="LifetimeTimer" to="." method="_on_lifetime_timer_timeout"]
[connection signal="timeout" from="BlinkTimer" to="." method="_on_blink_timer_timeout"]
