[gd_scene load_steps=3 format=3 uid="uid://c2mnr2pck4tyh"]

[ext_resource type="PackedScene" uid="uid://hlwjav75j4k7" path="res://scenes/powerups/base_power_up.tscn" id="1_0ykpg"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1ht0x"]
size = Vector2(48, 48)

[node name="PowerupHealth" instance=ExtResource("1_0ykpg")]
power_up_type = 3

[node name="CollisionShape2D" parent="." index="0"]
shape = SubResource("RectangleShape2D_1ht0x")

[node name="Background" type="ColorRect" parent="SpriteContainer" index="0"]
offset_left = -24.0
offset_top = -24.0
offset_right = 24.0
offset_bottom = 24.0
color = Color(1, 1, 1, 1)

[node name="Heart" type="Node2D" parent="SpriteContainer" index="1"]

[node name="HeartPolygon" type="Polygon2D" parent="SpriteContainer/Heart" index="0"]
color = Color(1, 0, 0.2, 1)
polygon = PackedVector2Array(0, 0, -12, -8, -12, -16, -8, -20, 0, -16, 8, -20, 12, -16, 12, -8, 0, 0)

[node name="HeartPolygon2" type="Polygon2D" parent="SpriteContainer/Heart" index="1"]
position = Vector2(0, 8)
color = Color(1, 0, 0.2, 1)
polygon = PackedVector2Array(0, 0, 12, -8, 0, 12, -12, -8)
