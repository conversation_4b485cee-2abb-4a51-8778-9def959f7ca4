[gd_scene load_steps=4 format=3]

[ext_resource type="Script" path="res://scenes/ui/health_display.gd" id="1_3u7s0"]
[ext_resource type="PackedScene" path="res://scenes/ui/health_icon.tscn" id="2_ij5vc"]

[sub_resource type="LabelSettings" id="LabelSettings_y6p0v"]
font_size = 36
font_color = Color(1, 0.9, 0.8, 1)
outline_size = 2
outline_color = Color(0, 0, 0, 1)
shadow_size = 2
shadow_color = Color(0, 0, 0, 0.498039)

[node name="HealthDisplay" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_3u7s0")

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -30.0
offset_right = 100.0
offset_bottom = 30.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20
alignment = 1

[node name="HeartIcon" parent="HBoxContainer" instance=ExtResource("2_ij5vc")]
custom_minimum_size = Vector2(60, 60)
layout_mode = 2

[node name="HealthLabel" type="Label" parent="HBoxContainer"]
layout_mode = 2
text = "3/3"
label_settings = SubResource("LabelSettings_y6p0v") 