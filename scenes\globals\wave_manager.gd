extends Node

# 波次管理器
# 负责游戏中敌人波次的生成和难度调整

# 引用波次定义
const WaveDefinitions = preload("res://scenes/globals/wave_definitions.gd")

# 敌人场景
var enemy_triangle_scene = preload("res://scenes/enemies/enemy_triangle.tscn")

# 游戏状态
var game_time: float = 0.0  # 游戏进行时间（秒）
var distance: float = 0.0   # 玩家行进距离
var difficulty_multiplier: float = 1.0  # 难度系数
var game_over: bool = false  # 游戏是否结束

# 波次状态
var active_continuous_waves = []  # 当前活跃的持续生成波次
var active_scripted_waves = []    # 当前活跃的脚本化波次
var processed_triggers = {}       # 已处理的触发器，避免重复触发
var processed_special_waves = {}  # 已处理的特殊波次，避免重复触发

# 特殊波次系统
var special_wave_enabled: bool = false  # 特殊波次系统是否启用
var next_special_wave_time: float = 40.0  # 下一个特殊波次的触发时间（初始为40秒）
var special_wave_interval: float = 20.0  # 特殊波次间隔（秒）
var special_waves = []  # 可用的特殊波次列表

# 基础敌人生成系统
var base_enemy_timer: float = 0.0  # 基础敌人生成计时器
var base_enemy_interval: float = 2.0  # 基础敌人生成间隔（秒）

# 屏幕尺寸和边界
var screen_width: float = 1280.0  # 默认值
var screen_height: float = 720.0  # 默认值
const SIDEBAR_WIDTH: float = 280.0  # 侧边栏宽度

# 波次定义实例
var wave_defs

# 信号
signal enemy_spawned(enemy)  # 当敌人生成时发出信号

func _ready():
	# 初始化波次定义
	wave_defs = WaveDefinitions.new()
	
	# 获取屏幕尺寸
	var viewport_rect = get_viewport().get_visible_rect()
	screen_width = viewport_rect.size.x
	screen_height = viewport_rect.size.y
	
	# 加载特殊波次列表
	special_waves = wave_defs.get_special_random_waves()
	
	# 初始化位置缓存
	update_position_cache()

func _process(delta):
	if game_over:
		return
	
	# 更新游戏时间
	game_time += delta
	
	# 处理基础敌人生成（每2秒一个）
	base_enemy_timer += delta
	if base_enemy_timer >= base_enemy_interval:
		base_enemy_timer = 0.0
		spawn_basic_enemy()
	
	# 处理特殊波次系统
	process_special_wave_system(delta)
	
	# 处理活跃的脚本化波次
	process_scripted_waves(delta)

# 生成基础敌人
func spawn_basic_enemy():
	var enemy = enemy_triangle_scene.instantiate()
	
	# 计算生成位置（顶部随机位置）
	var spawn_position = calculate_spawn_position(WaveDefinitions.SpawnPositionLogic.RANDOM_TOP)
	enemy.position = spawn_position
	
	# 设置移动模式为随机漂移（默认）
	enemy.set_movement_mode(WaveDefinitions.EnemyMovementMode.RANDOM_DRIFT)
	
	# 发出敌人生成信号
	emit_signal("enemy_spawned", enemy)

# 处理特殊波次系统
func process_special_wave_system(delta):
	# 检查是否应该触发特殊波次
	if game_time >= next_special_wave_time:
		# 如果是第一次触发特殊波次，处理初始特殊波次（倒三角形）
		if not special_wave_enabled:
			special_wave_enabled = true
			# 处理40秒时的第一个特殊波次（倒三角形）
			var first_wave = null
			for wave in wave_defs.get_time_based_waves():
				if wave["trigger_value"] == 40:
					first_wave = wave
					break
			
			if first_wave:
				start_scripted_wave(first_wave)
				print("触发初始特殊波次：倒三角形")
		else:
			# 随机选择一个特殊波次
			if special_waves.size() > 0:
				var random_index = randi() % special_waves.size()
				var special_wave = special_waves[random_index]
				
				# 启动特殊波次
				start_scripted_wave(special_wave)
				print("触发随机特殊波次：" + special_wave["name"])
		
		# 计算下一个特殊波次的触发时间
		next_special_wave_time = game_time + special_wave_interval
		print("下一个特殊波次将在 " + str(next_special_wave_time) + " 秒后触发")

# 开始一个脚本化波次
func start_scripted_wave(wave_data):
	# 创建一个脚本化波次实例
	var scripted_wave = {
		"wave_data": wave_data,
		"start_time": game_time,
		"current_event_index": 0,
		"next_event_time": game_time,  # 立即处理第一个事件
		"elapsed": 0.0,
		"id": wave_data["name"] + "_" + str(game_time)  # 创建唯一的波次ID
	}
	
	# 添加到活跃波次列表
	active_scripted_waves.append(scripted_wave)

# 处理脚本化波次
func process_scripted_waves(delta):
	var waves_to_remove = []
	
	for i in range(active_scripted_waves.size()):
		var wave = active_scripted_waves[i]
		wave["elapsed"] += delta
		
		# 检查是否有事件需要处理
		if game_time >= wave["next_event_time"]:
			# 获取当前事件
			var events = wave["wave_data"]["events"]
			var current_event = events[wave["current_event_index"]]
			
			# 处理事件（生成敌人）
			spawn_enemy_for_scripted_event(wave, current_event)
			
			# 移动到下一个事件
			wave["current_event_index"] += 1
			
			# 检查是否所有事件都已处理
			if wave["current_event_index"] >= events.size():
				waves_to_remove.append(i)
			else:
				# 计算下一个事件的时间
				var next_event = events[wave["current_event_index"]]
				wave["next_event_time"] = game_time + next_event["delay"]
	
	# 移除已结束的波次
	for i in range(waves_to_remove.size() - 1, -1, -1):
		active_scripted_waves.remove_at(waves_to_remove[i])

# 为脚本化事件生成敌人
func spawn_enemy_for_scripted_event(wave, event):
	var enemy_type = event["enemy_type"]
	var spawn_position_logic = event["spawn_position_logic"]
	var wave_id = wave["id"]
	
	# 确定移动模式：优先使用事件中指定的，其次使用波次中指定的，最后使用默认值
	var movement_mode = WaveDefinitions.EnemyMovementMode.RANDOM_DRIFT  # 默认值
	
	if "movement_mode" in wave["wave_data"]:
		movement_mode = wave["wave_data"]["movement_mode"]
	
	if "movement_mode" in event:
		movement_mode = event["movement_mode"]
	
	# 根据敌人类型、生成位置逻辑和移动模式生成敌人
	spawn_enemy(enemy_type, spawn_position_logic, movement_mode, wave_id)

# 生成敌人
func spawn_enemy(enemy_type, spawn_position_logic, movement_mode = WaveDefinitions.EnemyMovementMode.RANDOM_DRIFT, wave_id = ""):
	# 目前只支持基础三角形敌人
	if enemy_type == WaveDefinitions.EnemyType.BASIC_TRIANGLE:
		var enemy = enemy_triangle_scene.instantiate()
		
		# 计算生成位置
		var spawn_position = calculate_spawn_position(spawn_position_logic)
		enemy.position = spawn_position
		
		# 设置移动模式
		enemy.set_movement_mode(movement_mode)
		
		# 设置波次ID
		if wave_id != "":
			enemy.set_wave_id(wave_id)
		
		# 发出敌人生成信号
		emit_signal("enemy_spawned", enemy)

# 计算敌人生成位置 - 使用预计算的位置，减少每次计算的负担
var position_cache = {}

func calculate_spawn_position(position_logic):
	# 检查是否已经有缓存的位置
	if position_cache.has(position_logic):
		# 对于随机位置，仍然需要重新计算
		if position_logic == WaveDefinitions.SpawnPositionLogic.RANDOM_TOP:
			var game_area_width = screen_width - (SIDEBAR_WIDTH * 2)
			var pos = Vector2()
			pos.x = SIDEBAR_WIDTH + randf_range(20, game_area_width - 20)
			pos.y = -20
			return pos
		else:
			return position_cache[position_logic]
	
	# 如果没有缓存，计算并缓存位置
	var pos = Vector2()
	var game_area_width = screen_width - (SIDEBAR_WIDTH * 2)
	
	match position_logic:
		WaveDefinitions.SpawnPositionLogic.RANDOM_TOP:
			pos.x = SIDEBAR_WIDTH + randf_range(20, game_area_width - 20)
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.CENTER_TOP:
			pos.x = screen_width / 2
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.LEFT_TOP:
			pos.x = SIDEBAR_WIDTH + game_area_width * 0.25
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.RIGHT_TOP:
			pos.x = SIDEBAR_WIDTH + game_area_width * 0.75
			pos.y = -20
		
		# 特殊位置
		WaveDefinitions.SpawnPositionLogic.LEFT_CORNER:
			pos.x = SIDEBAR_WIDTH + 30
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.RIGHT_CORNER:
			pos.x = screen_width - SIDEBAR_WIDTH - 30
			pos.y = -20
		
		# 倒三角形阵列位置
		WaveDefinitions.SpawnPositionLogic.INV_TOP:
			pos.x = screen_width / 2
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.INV_LEFT_1:
			pos.x = screen_width / 2 - 40
			pos.y = -40
		
		WaveDefinitions.SpawnPositionLogic.INV_RIGHT_1:
			pos.x = screen_width / 2 + 40
			pos.y = -40
		
		WaveDefinitions.SpawnPositionLogic.INV_LEFT_2:
			pos.x = screen_width / 2 - 80
			pos.y = -60
		
		WaveDefinitions.SpawnPositionLogic.INV_MID_2:
			pos.x = screen_width / 2
			pos.y = -60
		
		WaveDefinitions.SpawnPositionLogic.INV_RIGHT_2:
			pos.x = screen_width / 2 + 80
			pos.y = -60
		
		WaveDefinitions.SpawnPositionLogic.INV_LEFT_3:
			pos.x = screen_width / 2 - 120
			pos.y = -80
		
		WaveDefinitions.SpawnPositionLogic.INV_MID_LEFT_3:
			pos.x = screen_width / 2 - 40
			pos.y = -80
		
		WaveDefinitions.SpawnPositionLogic.INV_MID_RIGHT_3:
			pos.x = screen_width / 2 + 40
			pos.y = -80
		
		WaveDefinitions.SpawnPositionLogic.INV_RIGHT_3:
			pos.x = screen_width / 2 + 120
			pos.y = -80
		
		# 直线阵列位置
		WaveDefinitions.SpawnPositionLogic.LINE_1:
			pos.x = SIDEBAR_WIDTH + 50
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.LINE_2:
			pos.x = SIDEBAR_WIDTH + 150
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.LINE_3:
			pos.x = SIDEBAR_WIDTH + 250
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.LINE_4:
			pos.x = SIDEBAR_WIDTH + 350
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.LINE_5:
			pos.x = SIDEBAR_WIDTH + 450
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.LINE_6:
			pos.x = SIDEBAR_WIDTH + 550
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.LINE_7:
			pos.x = SIDEBAR_WIDTH + 650
			pos.y = -20
		
		WaveDefinitions.SpawnPositionLogic.LINE_8:
			pos.x = SIDEBAR_WIDTH + 750
			pos.y = -20
		
		_:
			# 默认位置
			pos.x = screen_width / 2
			pos.y = -20
	
	# 缓存非随机位置
	if position_logic != WaveDefinitions.SpawnPositionLogic.RANDOM_TOP:
		position_cache[position_logic] = pos
	
	return pos

# 设置游戏距离（由主游戏场景调用）
func set_distance(new_distance):
	distance = new_distance

# 游戏结束
func set_game_over():
	game_over = true
	active_continuous_waves.clear()
	active_scripted_waves.clear()

# 重置波次管理器（开始新游戏时调用）
func reset():
	game_time = 0.0
	distance = 0.0
	difficulty_multiplier = 1.0
	game_over = false
	active_continuous_waves.clear()
	active_scripted_waves.clear()
	processed_triggers.clear()
	processed_special_waves.clear()  # 清除特殊波次处理记录
	base_enemy_timer = 0.0
	
	# 重置特殊波次系统
	special_wave_enabled = false
	next_special_wave_time = 40.0  # 初始特殊波次在40秒后触发
	
	# 加载特殊波次列表
	special_waves = wave_defs.get_special_random_waves()
	
	# 重新计算位置缓存
	update_position_cache()

# 更新位置缓存
func update_position_cache():
	position_cache.clear()
	
	# 预先计算所有固定位置
	var game_area_width = screen_width - (SIDEBAR_WIDTH * 2)
	
	# 基本位置
	position_cache[WaveDefinitions.SpawnPositionLogic.CENTER_TOP] = Vector2(screen_width / 2, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.LEFT_TOP] = Vector2(SIDEBAR_WIDTH + game_area_width * 0.25, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.RIGHT_TOP] = Vector2(SIDEBAR_WIDTH + game_area_width * 0.75, -20)
	
	# 特殊位置
	position_cache[WaveDefinitions.SpawnPositionLogic.LEFT_CORNER] = Vector2(SIDEBAR_WIDTH + 30, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.RIGHT_CORNER] = Vector2(screen_width - SIDEBAR_WIDTH - 30, -20)
	
	# 倒三角形阵列位置
	position_cache[WaveDefinitions.SpawnPositionLogic.INV_TOP] = Vector2(screen_width / 2, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.INV_LEFT_1] = Vector2(screen_width / 2 - 40, -40)
	position_cache[WaveDefinitions.SpawnPositionLogic.INV_RIGHT_1] = Vector2(screen_width / 2 + 40, -40)
	position_cache[WaveDefinitions.SpawnPositionLogic.INV_LEFT_2] = Vector2(screen_width / 2 - 80, -60)
	position_cache[WaveDefinitions.SpawnPositionLogic.INV_MID_2] = Vector2(screen_width / 2, -60)
	position_cache[WaveDefinitions.SpawnPositionLogic.INV_RIGHT_2] = Vector2(screen_width / 2 + 80, -60)
	position_cache[WaveDefinitions.SpawnPositionLogic.INV_LEFT_3] = Vector2(screen_width / 2 - 120, -80)
	position_cache[WaveDefinitions.SpawnPositionLogic.INV_MID_LEFT_3] = Vector2(screen_width / 2 - 40, -80)
	position_cache[WaveDefinitions.SpawnPositionLogic.INV_MID_RIGHT_3] = Vector2(screen_width / 2 + 40, -80)
	position_cache[WaveDefinitions.SpawnPositionLogic.INV_RIGHT_3] = Vector2(screen_width / 2 + 120, -80)
	
	# 直线阵列位置
	position_cache[WaveDefinitions.SpawnPositionLogic.LINE_1] = Vector2(SIDEBAR_WIDTH + 50, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.LINE_2] = Vector2(SIDEBAR_WIDTH + 150, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.LINE_3] = Vector2(SIDEBAR_WIDTH + 250, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.LINE_4] = Vector2(SIDEBAR_WIDTH + 350, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.LINE_5] = Vector2(SIDEBAR_WIDTH + 450, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.LINE_6] = Vector2(SIDEBAR_WIDTH + 550, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.LINE_7] = Vector2(SIDEBAR_WIDTH + 650, -20)
	position_cache[WaveDefinitions.SpawnPositionLogic.LINE_8] = Vector2(SIDEBAR_WIDTH + 750, -20) 
