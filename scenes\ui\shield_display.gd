extends Control

# 玩家最大护盾值
var max_shield: int = 3

# 组件引用
@onready var shield_icon = $HBoxContainer/ShieldIcon
@onready var shield_label = $HBoxContainer/ShieldLabel

func _ready():
	# 初始化护盾图标
	shield_icon.modulate.a = 1.0

# 更新显示的护盾值
func update_shield(current_shield: int):
	# 确保护盾值在有效范围内
	current_shield = clamp(current_shield, 0, max_shield)
	
	# 更新护盾值文本显示
	shield_label.text = str(current_shield) + "/" + str(max_shield)
	
	# 如果护盾值为0，使护盾图标变暗
	if current_shield <= 0:
		shield_icon.modulate.a = 0.3
	else:
		shield_icon.modulate.a = 1.0

# 设置最大护盾值
func set_max_shield(new_max_shield: int):
	max_shield = max(1, new_max_shield)  # 确保最大护盾值至少为1 
