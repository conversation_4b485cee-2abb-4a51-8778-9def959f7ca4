; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="几何射击"
run/main_scene="res://scenes/main/main_game.tscn"
config/features=PackedStringArray("4.4", "Forward Plus")
config/icon="res://icon.svg"

[autoload]

ResourceManager="*res://scenes/globals/resource_manager.tscn"
WaveManager="*res://scenes/globals/wave_manager.tscn"
CheatManager="*res://scenes/globals/cheat_manager.tscn"
PowerupManager="*res://scenes/globals/powerup_manager.tscn"
GameConstants="*res://scenes/globals/game_constants.tscn"
BoundaryManager="*res://scenes/globals/boundary_manager.tscn"
ParticleManager="*res://scenes/globals/particle_manager.tscn"
FileSystemUtils="*res://scenes/globals/file_system_utils.tscn"
SignalUtils="*res://scenes/globals/signal_utils.tscn"
GameManager="*res://scenes/globals/game_manager.tscn"
DebugManager="*res://scenes/globals/debug_manager.tscn"
ModificationManager="*res://scenes/globals/ModificationManagerAutoload.gd"

[display]

window/size/viewport_width=2560
window/size/viewport_height=1440
window/size/window_width_override=1280
window/size/window_height_override=720
window/stretch/mode="viewport"
window/stretch/aspect="keep_height"

[input]

ui_w={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":87,"physical_keycode":0,"key_label":0,"unicode":119,"location":0,"echo":false,"script":null)
]
}
ui_a={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":65,"physical_keycode":0,"key_label":0,"unicode":97,"location":0,"echo":false,"script":null)
]
}
ui_s={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":83,"physical_keycode":0,"key_label":0,"unicode":115,"location":0,"echo":false,"script":null)
]
}
ui_d={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":68,"physical_keycode":0,"key_label":0,"unicode":100,"location":0,"echo":false,"script":null)
]
}
ui_b={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":66,"physical_keycode":0,"key_label":0,"unicode":98,"location":0,"echo":false,"script":null)
]
}
ui_left_mouse={
"deadzone": 0.5,
"events": [Object(InputEventMouseButton,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"button_mask":1,"position":Vector2(0, 0),"global_position":Vector2(0, 0),"factor":1.0,"button_index":1,"canceled":false,"pressed":true,"double_click":false,"script":null)
]
}

[rendering]

textures/default_filters/use_nearest_mipmap_filter=true
textures/default_filters/anisotropic_filtering_level=4
anti_aliasing/quality/msaa_2d=3
environment/defaults/default_clear_color=Color(0.0588235, 0.0784314, 0.168627, 1)
anti_aliasing/quality/screen_space_aa=1
anti_aliasing/quality/use_debanding=true
