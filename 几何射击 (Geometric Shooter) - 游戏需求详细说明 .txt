《几何射击 (Geometric Shooter) - 游戏需求详细说明 .txt》 (扩充版 V3 - 完整重述版)

1. 基本概念 (Basic Concept)

1.1. 游戏类型:

核心定义: 2D、俯视角、弹幕射击 (Top-Down Bullet Hell Shooter)。

平台: PC (Steam)

开发引擎: Godot Engine 4.4.1

视角: 固定俯视视角，无镜头缩放或旋转（除非特定效果需要，如Boss登场时的短暂镜头晃动）。

1.2. 游戏名称:

名称: 几何射击 (Geometric Shooter)

1.3. 核心游戏机制:

玩家行动: 控制几何形状的玩家单位进行移动、鼠标瞄准射击、躲避弹幕。

资源管理: 收集随机掉落的强化道具 (Power-ups)，管理其持续时间。管理游戏货币。

成长系统: 通过击败特定Boss和完成剧情获得永久性改装件，使用改装系统进行策略性组合与强化。使用游戏货币进行永久性能力提升和解锁。

游戏目标: 完成故事模式的剧情，挑战不同的结局；在肉鸽模式中生存、获取高分和最远距离。

风险与回报: 靠近敌人或弹幕密集区域以拾取道具，可能带来风险但能获得强化。

1.4. 美术风格:

风格定义: 极简主义几何美学 (Minimalist Geometric Aesthetics)。故事模式后期可加入更复杂的拓扑几何和抽象光影元素，但实现上仍以2D精灵和粒子为主。

构成元素: 仅使用纯色填充或空心线条的简单几何形状（正方形、圆形、三角形、五角形、菱形、六边形等）及其组合。故事模式后期敌人和Boss可能出现由这些基础几何形状拼接成的、暗示分形、纽结、莫比乌斯环等拓扑概念的复合形态。

色彩方案: 采用高对比度、清晰可辨的颜色方案，例如玩家默认为蓝色（可自定义），不同敌人/弹幕为红色、橙色、紫色、绿色等，道具和特效使用白色或其他醒目颜色。“调律者”及其造物倾向于使用冰冷、纯粹的单色或平滑渐变色（如白色、银色、深蓝色、紫色），并带有强烈的能量光晕（通过PointLight2D或发光的精灵实现）。

视觉效果: 大量运用粒子效果 (Particle Effects - CPUParticles2D/GPUParticles2D) 和光晕 (Glow Effects - PointLight2D, ShaderMaterial的简单辉光效果)，如子弹拖尾、命中火花、爆炸、护盾闪烁等，来增强视觉反馈和动态感。故事模式后期可加入空间扭曲（例如通过Shader使背景或特定区域的顶点发生位移）、维度褶皱（例如背景出现多层视差滚动的抽象几何图案）等视觉特效，实现上以2D手段为主。

动画: 主要通过Tween节点或脚本控制Node2D的position, rotation_degrees, scale, modulate属性，实现位移、旋转、缩放、颜色/透明度变化等动画效果。Boss和特殊敌人可以有由多个独立Node2D部件组合并通过动画协调动作的复杂变形或组合动画。

1.5. 游戏范围与屏幕: (修改并扩充)

显示模式: 横屏显示。

推荐分辨率: 设定一个基准开发分辨率，例如 1920x1080 或 1280x720 (16:9 宽高比)，并考虑不同分辨率下的适配方案（例如使用Godot的拉伸模式 viewport 或 canvas_items）。

初始游戏区域: 游戏开始时，玩家和敌人的主要活动区域限制在一个预设的初始可视范围内（例如，与基准分辨率等同的“小型地图”）。

动态扩展: 随着玩家飞船改装程度的提升（具体标准见1.6），游戏的可视区域和实际活动边界会动态扩展，提供更大的战斗空间。敌人可以从扩展后的屏幕外生成并进入。

新增 1.6. 动态游戏区域与滚动方向 (Dynamic Game Area & Scrolling Direction)

1.6.1. 动态游戏区域扩展 (Dynamic Game Area Expansion):

触发机制: 当玩家飞船上已装备的改装件所占据的网格单元总数达到预设阈值时，触发游戏区域的扩展。可以设定多个扩展级别，例如：

级别1 (小型地图): 初始状态，例如装备0-15个改装件格子。对应基准游戏区域。

级别2 (中型地图): 例如装备16-40个改装件格子。游戏区域在级别1的基础上按比例扩大（例如，宽度和高度各增加30-50%）。

级别3 (大型地图): 例如装备41+个改装件格子。游戏区域在级别2的基础上进一步扩大（例如，再增加30-50%）。

（具体阈值和扩展比例需要测试和调整以获得最佳体验）

实现方式 (推荐 - 动态调整游戏区域边界):

为每个地图级别预设一个game_area_rect (定义玩家可活动的矩形边界) 和 enemy_spawn_padding (敌人在此矩形外多远生成)。

当触发扩展时，当前有效的game_area_rect会通过Tween动画平滑地过渡到新的、更大的尺寸。玩家的移动会被限制在新边界内。

敌人生成器的生成位置和AI的巡逻范围会根据新的game_area_rect和enemy_spawn_padding进行调整。

背景元素（如滚动星空）的尺寸或滚动速度可能会相应调整，以匹配新的“视野”范围，保持视觉协调。

相对尺寸调整: 为了强化玩家“变强、变大”的感觉，在地图扩展到中型或大型时，新生成的敌人及其子弹的基础渲染尺寸（scale属性）可以略微减小（例如，中型地图敌人尺寸为正常的90%，大型地图为80%）。玩家飞船本身的渲染尺寸保持不变（或仅因改装件视觉而变大）。

视觉过渡: 地图扩展的过程应有平滑的视觉过渡效果，例如摄像机在短时间内轻微拉远（如果同时配合Camera2D.zoom的微调）或背景发生一种“空间延展”的动态特效。

1.6.2. 地图滚动方向 (Map Scrolling Direction):

默认方向: 游戏默认采用传统的向上滚动方式，即玩家感觉自己在向上飞行，敌人主要从屏幕顶部（Y轴负方向）出现。

可配置方向: 在故事模式的特定关卡或肉鸽模式的特定区域/节点中，可以预设不同的主滚动方向，以增加关卡的多样性和挑战性：

向上滚动 (Vertical Up): 玩家“前进”方向为Y轴负向。

向下滚动 (Vertical Down): 玩家“前进”方向为Y轴正向。敌人主要从屏幕底部出现。

向右滚动 (Horizontal Right): 玩家“前进”方向为X轴正向。敌人主要从屏幕右侧出现。

向左滚动 (Horizontal Left): 玩家“前进”方向为X轴负向。敌人主要从屏幕左侧出现。

实现影响:

玩家的“前进”输入（例如默认的W键或↑方向键）需要根据当前关卡的scroll_direction转换成实际的移动向量。

背景的滚动方向（ParallaxBackground的scroll_offset如何变化）将根据scroll_direction调整。

敌人生成器的默认生成边缘（例如，是从顶部、底部、左侧还是右侧生成）会根据scroll_direction调整。

玩家的初始朝向和部分UI元素（如小地图箭头，如果未来加入）可能需要根据scroll_direction进行旋转。

对于横向滚动的关卡，玩家的射击逻辑（鼠标瞄准）和机体旋转依然有效，但战斗的策略重心会从上下躲避转变为更侧重于垂直方向的机动。

2. 玩家系统 (Player System)

2.1. 外观 (Appearance):

核心形状 (初始 - “异构体原型 Isomer Prototype”): 一个由Polygon2D或多个Line2D构成的空心正方形，尺寸精确为 10x10 像素。边框线宽建议为1像素。默认颜色为蓝色 (#007FFF 亮蓝色)，玩家可在游戏开始前通过修改Polygon2D的颜色属性来自定义。随着剧情推进和改装，飞船外观会通过添加新的Polygon2D或Sprite2D子节点来发生显著变化，这些子节点代表不同的改装件，并融入更多拓扑几何元素（例如，一个莫比乌斯环形的能量回路可以由一个扭曲的Line2D或特别绘制的Sprite2D实现，克莱因瓶结构启发的护盾模块可以是一个视觉上暗示该结构的组合几何体）。最终形态可能为“悖论引擎 (Paradox Engine)”，由多个复杂的几何部件组合而成。

内部元素 (驾驶舱): 核心正方形内部中心有一个白色实心小圆形Polygon2D（或一个圆形Sprite2D），直径精确为 5x5 像素。颜色值: (#FFFFFF 白色)。此为玩家的驾驶舱。

视觉状态:

待机: 核心正方形及驾驶舱的modulate.a（透明度）或scale可以通过Tween节点实现轻微的脉冲变化。改装后的复杂几何部件也可能有能量流动的视觉效果（例如，通过ShaderMaterial改变其纹理坐标，或使用粒子系统模拟能量流动）。

移动: 可以在玩家节点后方生成短暂存在的拖尾粒子（CPUParticles2D），粒子形状可以是小的正方形或三角形，颜色与玩家主体一致。

射击: 枪口（玩家正方形的“前方”，根据旋转方向决定）在发射子弹时，可以短时间在其位置生成一个亮光Sprite2D或少量粒子。

拓扑能力激活: 使用“维度滑行”或“悖论共鸣”等核心能力时，玩家飞船的根节点或特定子节点的modulate属性可以发生短暂变化（例如，变为半透明并叠加一层辉光效果），或其scale和rotation_degrees发生轻微的、快速的抖动或扭曲（通过Tween或脚本直接修改）。

2.2. 控制 (Control):

输入方式:

移动: 支持键盘 WASD 键 和 方向键 (↑↓←→) 进行移动。通过读取Input.get_vector()来获取方向，并更新玩家节点的position。

闪避: 空格键或者shift键。按下时，记录当前移动方向或鼠标指针方向的单位向量，然后通过Tween节点在短时间内将玩家节点的position向该方向移动一段固定距离。同时可以播放闪避音效和生成特殊的拖尾粒子。

射击: 鼠标左键。通过Input.is_action_just_pressed()和Input.is_action_just_released()（或一个布尔变量切换状态）来控制一个射击计时器（Timer节点）的启动和停止。

瞄准与机体朝向: 鼠标光标位置（get_global_mouse_position()）决定射击方向。通过look_at(get_global_mouse_position())使玩家节点的“正前方”（通常是Y轴负方向或X轴正方向，取决于精灵素材朝向）始终朝向鼠标光标位置。这将直接影响子弹发射方向和玩家的视觉朝向。

改装界面: 按下 B 键。触发打开改装界面的逻辑。

AI友军指令: 按下 Q 键。触发打开AI友军指令轮盘UI的逻辑。

核心拓扑能力键: 例如 R键 激活“维度滑行”，F键 激活“悖论共鸣”。通过Input.is_action_just_pressed()触发相应技能逻辑。

移动方式:

响应性: 直接将输入向量乘以速度并应用到position上，不使用复杂的物理模拟，以保证即时响应。

速度: 设定一个基础移动速度变量（例如 move_speed = 300），在_physics_process中更新position时使用。该变量可通过游戏货币升级。

移动范围: 玩家节点的global_position将被限制在屏幕的可视区域内。在_physics_process中，每次更新位置后，使用clamp()函数将其X和Y坐标限制在屏幕边界内（例如 global_position.x = clamp(global_position.x, 0, screen_width)）。

2.3. 射击 (Shooting):

机制: 当射击状态激活时，射击计时器（Timer节点）周期性触发timeout信号，执行发射子弹的逻辑。

射击方向: 子弹的初始方向向量为从玩家当前位置指向鼠标光标位置的单位向量。

射击速率 (Fire Rate): 射击计时器的wait_time属性决定射击间隔（例如 0.2 秒）。该值可通过游戏货币升级（即减小wait_time）。

子弹伤害 (Bullet Damage): 子弹场景中定义一个伤害变量（例如 damage = 1）。该值可通过特定改装件或技能提升。

子弹生成点: 子弹实例（PackedScene.instantiate()）在玩家节点的全局位置生成，或者从玩家节点的一个Marker2D子节点（代表枪口）的位置生成。其初始旋转方向与玩家当前的瞄准方向一致。

2.4. 子弹 (Player Bullet):

外观: 一个独立的子弹场景（player_bullet.tscn），根节点为Area2D。包含一个Polygon2D或Sprite2D来显示长条形外观（例如 2x8 像素）。颜色可以通过脚本在实例化时设置，或根据玩家选择的颜色动态调整。

发射方向: 实例化后，根据传入的方向向量设置其线速度或直接在_physics_process中沿该方向更新position。

速度: 定义一个速度变量（例如 bullet_speed = 600），在_physics_process中用于更新位置。

碰撞检测: Area2D节点设置其collision_layer和collision_mask，使其能检测到“敌人”层。连接body_entered或area_entered信号来处理命中逻辑。

生命周期: 在子弹的脚本中，检查其global_position是否超出了屏幕边界。如果超出，则调用queue_free()销毁自身。或者使用一个Timer节点设置其最大存活时间。

视觉效果: 可以在子弹场景中添加一个CPUParticles2D节点作为拖尾效果。命中敌人时，在命中点实例化一个短暂的“命中火花”场景（包含少量粒子和闪光）。

2.5. 护盾系统 (Shield System):

获取方式: 玩家对象中有一个current_shields变量。当拾取“+”道具且当前生命值已满时，current_shields增加，但不超过max_shields。

最大层数: 玩家对象中有一个max_shields变量，基础为2。可通过游戏货币升级或装备改装件（增加max_shields的值）。

视觉表现: 玩家节点下可以有多个Line2D或Sprite2D子节点代表不同层数的护盾环。根据current_shields的值来显示/隐藏或改变这些护盾环的视觉效果（例如，通过Tween改变其modulate.a或scale实现闪烁）。

一层护盾: 第一个护盾环可见，颜色与玩家主色一致，通过Tween使其modulate.a在例如0.5到1.0之间循环变化。

二层护盾: 第二个护盾环（在第一个外层）可见，颜色为白色，闪烁方式同上但可以有不同的周期或相位。

更多层数: 类似地添加更多护盾环。

特殊护盾 (例如“莫比乌斯护盾”): 如果装备了此类改装件，护盾的视觉效果可能会替换为更复杂的几何图案（例如，一个围绕玩家旋转的、扭曲的Line2D或Sprite2D）。其抵挡伤害的逻辑可能不变，或附加特殊效果（例如，成功抵挡后有几率反弹一个低伤害的几何碎片）。

功能: 玩家受到伤害时，如果current_shields > 0，则current_shields减1，生命值不变。

消耗表现: 当一层护盾被消耗时，对应的护盾环视觉节点可以播放一个短暂的“碎裂”动画（例如，快速缩小并消失，或分解成多个小碎片粒子）然后隐藏。同时播放护盾破碎音效。

改装互动: 装备特定改装件时，其脚本会修改玩家对象的max_shields属性。

2.6. 受伤效果 (Hit Effect):

触发: 当玩家受到伤害且current_shields == 0时触发。

视觉表现: 玩家主精灵（及其所有附加的改装件视觉子节点）的modulate属性通过Tween节点在2秒内快速改变其透明度 (Alpha)，例如在0.3和1.0之间来回切换，每0.1秒切换一次状态。

无敌状态 (Invincibility): 在受伤效果持续期间，设置一个布尔变量 is_invincible = true。在此状态下，玩家不会再次因碰撞而减少生命值。2秒后通过Timer节点将is_invincible设回false，并恢复正常的modulate。

音效: 播放一个表示受伤的音效。

语音: (后期实现) 如果实现了语音系统，此时可以播放玩家角色的受击语音。

2.7. 改装系统 (Modification System):

激活: 按下 B 键，触发显示改装UI场景的逻辑，并暂停游戏主场景（例如设置get_tree().paused = true，或将主场景的时间缩放Engine.time_scale设为0或极小值）。

游戏状态: 如上所述，游戏逻辑暂停或极度减缓。

界面 (Interface): 一个独立的UI场景（例如 modification_screen.tscn），包含以下元素：

布局: 一个GridContainer节点用于显示11x21的网格。

玩家核心显示: 在网格中央的一个特定单元格内，显示玩家核心形状的预览（一个Sprite2D或Polygon2D）。已装备的改装件也会以类似方式显示在相邻的单元格。

物品栏: 一个ScrollContainer包含一个GridContainer或VBoxContainer，用于显示玩家拥有的改装件。每个改装件是一个自定义的Control节点（包含图标TextureRect、名称Label、数量Label）。

操作:

选择: 点击物品栏中的改装件，将其信息（例如场景路径、图标）暂存，并在鼠标指针上显示其虚影（一个半透明的Sprite2D跟随鼠标）。

放置: 在改装网格上点击，如果位置有效（与核心或其他已装备改装件相邻，且不超出边界），则在对应单元格实例化该改装件的预览，并更新玩家的改装数据。

高亮: 鼠标在网格上移动时，根据是否可放置，改变对应单元格的背景色或显示一个高亮框。

取消/拾起: 右键点击已放置的改装件，将其从网格上移除，并放回物品栏（增加其数量）。

确认/退出: “确认”按钮应用改装并关闭UI，“取消”按钮放弃更改并关闭UI。关闭UI时恢复游戏主场景的暂停状态或时间缩放。

货币显示: 一个Label节点显示玩家当前的游戏货币数量。

升级提示: 如果选中一个可升级的改装件，旁边显示升级按钮和所需货币。

规则 (Rules):

连接性: 在尝试放置改装件时，检查其目标网格单元的上下左右四个相邻单元格中，是否存在玩家核心或其他已装备的改装件。

禁止悬空: 确保至少有一个连接。

网格限制: 检查目标网格单元的索引是否在0到10（行）和0到20（列）之间。

效果 (Effects):

物理形态改变: 确认改装后，在玩家的游戏场景实例中，根据改装数据，动态添加/移除代表改装件的Node2D子节点（包含视觉和碰撞体）。这些子节点的位置相对于玩家核心进行设置。

碰撞箱扩展: 玩家的根碰撞体（例如一个覆盖核心的CollisionShape2D）可能保持不变，但每个装备的改装件都拥有自己的小型CollisionShape2D。这样整体的碰撞区域会随改装而变化。

属性加成: 改装件数据中包含其提供的属性加成。确认改装后，更新玩家对象的相应属性变量（如max_health_bonus, shield_bonus_from_mods等）。

物品栏 (Inventory): 玩家数据中（例如一个全局单例或玩家存档文件）存储一个字典或数组，记录拥有的改装件类型及其数量和等级。

容量: 物品栏UI的滚动区域理论上可以容纳很多种，但初始显示的格子数量可以固定，例如10-20个，超出则滚动。

显示: 清晰展示每个已获得的、可供装备的改装件图标及其等级和数量。

来源: 改装件通过击败Boss首次获得（掉落一个代表该改装件的特殊道具，拾取后解锁并添加到物品栏），之后可通过击杀特定数量敌人概率掉落（同上），也可在特定商店（UI界面）或通过游戏货币直接购买（从一个预设的列表中选择，购买后增加其在物品栏中的数量或解锁）。

升级: 改装件数据中包含其等级和升级所需的货币。在改装界面选中改装件后，如果货币足够，点击升级按钮，则消耗货币，提升该改装件的等级，并更新其提供的属性加成或效果。

2.8. 玩家死亡与复活 (Player Death & Revival):

单人模式死亡:

玩家生命值（current_health）降至0。

视觉效果: 玩家节点播放一个“爆炸”动画（例如，快速放大然后消失，同时产生大量粒子）。其中心的白色圆形驾驶舱（一个独立的Sprite2D或Node2D）从爆炸中心以一定速度向上方（屏幕后方）移动，可以通过Tween或脚本在几秒内移出屏幕并queue_free()。

游戏结束: 触发显示“Game Over”界面的逻辑。

多人模式死亡:

玩家生命值降至0。

视觉效果: 同上，玩家主体爆炸，驾驶舱弹出。

驾驶舱行为: 弹出的驾驶舱（一个独立的场景实例，包含视觉和小型Area2D用于被拾取）不会移出屏幕。它会获得一个目标（最近的存活友方玩家），并在_physics_process中缓慢地朝该目标移动。驾驶舱本身是无敌的（没有碰撞伤害逻辑）。

救援: 存活的友方玩家的Area2D检测到与驾驶舱的碰撞。救援者对象中有一个列表或变量记录携带的驾驶舱。将该驾驶舱对象添加到列表中，并使其在视觉上跟随救援者（例如，成为救援者节点的一个子节点，并设置一个偏移位置）。

复活机制:

当携带驾驶舱的救援者拾取“+”（回血）道具时，如果自身生命已满或已加盾，则从其携带的驾驶舱列表中取出一个，在救援者当前位置实例化一个新的玩家对象（被复活者），赋予少量生命值，并播放复活特效。然后消耗掉该“+”道具。

被复活的玩家以少量生命值（例如1点或最大生命值的25%）在救援者当前位置重生，并有短暂无敌（同2.6的受伤无敌逻辑）。

若携带多个驾驶舱，则按被击败的先后顺序或随机复活一个。

所有玩家阵亡（多人模式）: 游戏主循环中检测所有连接的玩家是否都处于“已阵亡且驾驶舱未被拾取”的状态。如果是，则触发游戏结束。

2.9. 玩家永久性升级 (Permanent Player Upgrades):

机制: 在主菜单的一个“升级 (Upgrades)”UI界面中，玩家可以使用在游戏中积累的游戏货币，为一些基础属性购买永久性提升。这些升级数据需要被保存和加载。

可升级属性示例 (每个属性有多个等级，每级效果和消耗递增):

基础最大生命值上限提升: （例如，每级+1最大生命值）。

基础最大护盾层数上限提升: （例如，每级+1最大护盾层数）。

基础移动速度提升: （例如，每级+10像素/秒）。

基础射击速率提升: （例如，每级使射击间隔-0.01秒，有下限）。

货币获取效率提升: （例如，每级使获得的货币量增加5%）。

升级成本: 每个属性的每个等级都有一个预设的货币消耗值，通常等级越高消耗越大。

2.10. 核心拓扑能力 (Core Topological Abilities - 故事模式解锁与升级):

维度滑行 (Dimension Sliding) / 拓扑穿梭 (Topological Phasing):

效果: 激活后，玩家飞船的碰撞层（collision_layer）和碰撞掩码（collision_mask）暂时改变，使其不会与特定类型的敌人子弹（例如标记为“实体弹幕”的子弹）发生碰撞。同时视觉上表现为半透明、扭曲或被几何光线包裹。持续一个短暂的时间（例如1-2秒）。

解锁: 故事模式中完成特定任务或剧情节点后，玩家对象的一个布尔变量 can_dimension_slide 设为 true。

升级: 可通过游戏货币在“升级”界面或特定改装件提升：

持续时间：增加技能激活的时长。

冷却时间：减少技能再次可用的等待时间。

可穿透类型：允许穿透更多类型的弹幕或特定薄弱的几何障碍物（需要障碍物有特殊标记）。

实现: 使用一个Timer控制持续时间和冷却时间。激活时改变碰撞层/掩码，并启动视觉效果；持续时间到后恢复。

悖论共鸣 (Paradox Resonance) / 逻辑奇点引爆 (Logic Singularity Detonation):

效果: 激活后，以玩家为中心产生一个快速扩大的圆形Area2D（视觉上是一个几何冲击波）。所有进入该区域的AI敌人（需要敌人脚本中有特定逻辑）会触发一个“逻辑混乱”状态（一个布尔变量 is_confused = true，并启动一个Timer）。在此状态下，敌人的AI行为改变（例如，短暂停止移动和射击，或者胡乱射击）。

解锁: 故事模式中，主角飞船的“逆谐振核心”与“调律场域”发生特殊共鸣后（剧情事件驱动），玩家对象的can_paradox_resonance设为true。

升级: 可升级：

影响范围：增加圆形Area2D的最终半径。

混乱持续时间：增加敌人is_confused状态的持续时间。

对特定敌人效力：可能对某些精英敌人造成更长时间的混乱或额外效果（例如短暂破盾）。

充能速度/冷却时间：减少技能再次可用的等待时间。

实现: 激活时创建一个冲击波Area2D实例，通过Tween使其快速扩大半径并渐隐。Area2D的area_entered或body_entered信号连接到处理对敌人施加“逻辑混乱”的函数。使用Timer控制冷却。

3. 敌人系统 (Enemy System)

通用敌人属性:

碰撞层级: 所有敌人场景的根节点（通常是Area2D或CharacterBody2D）设置其collision_layer为“敌人”层，并设置collision_mask使其能与“玩家子弹”层和“玩家”层发生碰撞。敌人的子弹应在另一层（例如“敌人子弹”层），仅与“玩家”层碰撞。

受伤反馈: 敌人脚本中有一个take_damage(amount)函数。被调用时，除了减少HP，还会触发一个短暂的视觉反馈：例如，通过Tween使其Sprite2D或Polygon2D的modulate属性快速变为白色再恢复原色，或者播放一个小的闪烁动画。

死亡特效: 当敌人HP <= 0时，在敌人位置实例化一个“死亡特效”场景（包含CPUParticles2D和可能的AnimationPlayer用于播放爆炸动画）。粒子颜色和形状应与敌人主题相关。同时播放相应的爆炸音效。之后敌人实例queue_free()。

掉落货币: 敌人死亡时，根据其类型，有一定几率或固定掉落游戏货币（实例化一个可拾取的货币图标场景）。

难度缩放: 游戏主控脚本中有一个全局难度系数。敌人在生成时，其基础HP、攻击频率（Timer的wait_time）、移动速度等属性会乘以或除以这个系数。

“几何模因”特性: 大部分“调律者”造物在被击中或死亡时，其Sprite2D或Polygon2D的视觉效果会发生不稳定的“解构”（例如，分解成多个小的基础几何形状，然后这些小形状通过Tween随机飞散并消失）或“重组”视觉效果，而非简单的爆炸粒子。

3.1. “几何先驱 - 三角斥候 (Geometric Harbinger - Tri-Scout)”

外观: 一个由Polygon2D绘制的空心等边三角形，尖端朝下。颜色为冰冷的白色或淡蓝色，通过PointLight2D附加微弱的同色光晕。边长12像素，线宽1像素。

生命值 (HP): 3点。

移动方式: 从屏幕顶部随机X轴位置生成，垂直向下移动 (Y轴正方向，速度例如150像素/秒)。同时，在水平方向上进行缓慢的正弦波式左右漂移（例如，offset.x = sin(time * drift_frequency) * drift_amplitude）。确保通过clamp()函数不会移出左右屏幕边界。

攻击方式: 拥有一个Timer节点，每隔1.5秒触发一次。触发时，计算朝向玩家当前位置的方向向量，并沿该方向发射一枚“几何碎晶 (Geometric Shard)”。

几何碎晶: 一个小型的、高速的实心三角形Polygon2D子弹，颜色与斥候一致，速度例如250像素/秒，造成1点伤害。

生成方式: 由波次管理器控制，基础生成间隔为1.5秒（可受难度影响）。

得分: 10分。掉落货币: 1-3单位。

特效: 受伤时闪烁白色0.1秒。死亡时三角形分解成三个更小的三角形线条，旋转飞散并消失。

3.2. “几何执行者 - 三角追猎者 (Geometric Enforcer - Tri-Hunter)”

外观: 类似三角斥候，但边长18像素，形态更锐利，可能有额外的几何棱角装饰（例如，每个角上伸出一个更小的三角形尖刺）。光晕更强。

生命值 (HP): 5点。

移动方式: 与三角斥候类似（速度150像素/秒），但漂移幅度和频率可能更大，或在接近玩家一定距离时会进行一次短促的、朝向玩家侧翼的几何路径冲锋（通过Tween实现）。

攻击方式: 拥有一个Timer节点，每隔1.5秒触发一次。触发时，向玩家方向进行一次快速的三连发“几何碎晶”（连续发射3枚，每发之间间隔0.1秒，可以通过一个计数器和短Timer实现）。或者，发射一枚能短暂停留（例如0.5秒）然后分裂成三枚小碎晶的“聚合碎晶 (Cluster Shard)”（聚合碎晶本身是一个场景，其脚本中包含分裂逻辑）。

生成方式: 游戏开始30秒后才开始加入生成池。基础生成间隔为3秒。

得分: 15分。掉落货币: 2-5单位。

特效: 受伤闪烁。死亡时解构成更多、更复杂的几何碎片。

3.3. “几何执行者 - 球形卫兵 (Geometric Enforcer - Spherical Sentinel)”

外观: 一个完美的圆形Polygon2D或Sprite2D（直径10像素），颜色为“调律者”色系的银白色或淡紫色。表面有通过ShaderMaterial实现的、缓慢脉动的同心圆或螺旋几何能量纹路。

生命值 (HP): 3点。

移动方式: 移动速度设定为较快，例如200像素/秒。根据生成模式决定路径：

模式1 (角落阵列): 从屏幕左上角和右上角外生成，通过预设的Path2D节点定义的路径（例如，先水平向内，再向下）按顺序移动。

模式2 (中央交叉): 从屏幕正上方中央生成，分成两队，一队沿预设Path2D向左下，一队向右下移动。

模式3 (顶部列队): 从屏幕正上方生成，排列成紧密队列（通过调整生成位置和时间差实现），垂直向下移动。

攻击方式: 拥有一个Timer节点，每隔1秒触发一次。触发时，同时向其当前移动方向的正前方（获取其速度向量并标准化）发射2发“能量脉冲 (Energy Pulse)”。

能量脉冲: 中速（例如300像素/秒）的实心圆形Polygon2D子弹，颜色与卫兵一致，每发造成1点伤害。

生成方式: 游戏开始30秒后才开始出现。由波次管理器在特定时间点触发一次“球形卫兵波次”，随机选择上述三种生成模式之一。

得分: 10分。掉落货币: 1-3单位。

特效: 受伤时能量纹路闪烁加剧。死亡时球体向内快速收缩（Tween scale 到0），并在原位置爆发出一个短暂的环状能量波粒子效果。

3.4. “几何执行者 - 五边塑形者 (Geometric Enforcer - Penta-Shaper)”

外观: 一个由Polygon2D绘制的完美正五边形，颜色为“调律者”色系的深蓝色或青色。边长15像素。边缘有通过粒子或Line2D子节点实现的、动态流动的能量线条。

生命值 (HP): 4点。

移动方式: Z字形（或称之字形/Zigzag）移动。速度100像素/秒。向下移动一段固定距离（例如50像素），然后通过Tween或脚本控制，以45度角向左下（或右下）移动相同距离，再向下，再反向斜向，如此循环。

攻击方式: 拥有一个Timer节点，每隔2秒触发一次。触发时，发射3发呈扇形散射（例如，中间一发朝前，左右各偏转30度）的“棱柱射线 (Prismatic Ray)”。

棱柱射线: 细长、穿透性较低（击中一个目标后消失）的能量束（可以用一个拉伸的Sprite2D或Line2D加辉光效果实现，存在短暂时间后消失）。视觉上像五彩棱光但主色调仍为“调律者”色系。

生成方式: 游戏进行1分钟 (60 秒) 后开始出现。每10秒由波次管理器尝试生成一个或一小组。

得分: 15分。掉落货币: 2-4单位。

特效: 受伤时能量流动加剧或颜色变白。死亡时五边形分解成五个独立的三角形Polygon2D碎片，这些碎片短暂存在并各自旋转飞散后消失。

3.5. “几何构造体 - 五角堡垒 (Geometric Construct - Penta-Bastion)”

外观: 一个大型的、由多个五边形Polygon2D嵌套或组合而成的厚重结构（例如，一个大的五边形作为主体，每个角上再连接一个小五边形炮台）。颜色为深邃的紫色或黑色，带有银色几何电路纹路（Sprite2D覆盖或ShaderMaterial）。在其几何中心有一个明亮的、脉动的能量核心Sprite2D（视觉上是弱点，也用于间接指示血量——血量越低，脉动越弱或颜色越暗淡）。整体尺寸约40x40像素。

生命值 (HP): 25点。

移动方式: 移动缓慢（例如50像素/秒），通常只在屏幕上半区域进行Z字或水平移动。在其周围一定半径内，可以通过ShaderMaterial在背景上渲染一个半透明的几何网格特效，表示其“领域压制”。

攻击方式:

扇形散射 (主炮): 能量核心每1秒充能（核心变亮），然后发射5发强化的“棱柱射线”（更粗、伤害略高），覆盖120度扇形区域。

精确射击 (副炮): 侧翼的小五边形炮台每3秒轮流向玩家当前方向精确发射一枚“聚焦奇点 (Focused Singularity)”。

聚焦奇点: 小型的（例如直径3像素）、高速的（例如400像素/秒）、高伤害（例如2点）的能量球Sprite2D，命中后有小范围爆炸粒子效果。

召唤 (防御机制): 每隔5秒，在其周围通过几何粒子汇聚的方式（CPUParticles2D发射模式设为Sphere，粒子逐渐汇聚成形）生成2个“几何先驱 - 三角斥候”。

生成方式: 作为小型精英怪，在特定波次或故事模式的特定剧情点出现。

得分: 50分。掉落货币: 10-15单位。

特效: 能量核心被击中时会剧烈闪烁，几何电路纹路出现“过载”般的亮线流动。死亡时发生大规模的几何解构，所有组成部件向外爆开，核心发生一次强烈的冲击波式爆炸（通过粒子和屏幕震动实现）。

3.6. “几何执行者 - 菱形干扰者 (Geometric Enforcer - Rhombic Jammer)”

外观: 一个由Polygon2D绘制的完美菱形（可以看作旋转45度的正方形），颜色为带有干扰杂讯的青绿色或灰色。对角线长度均为12像素。表面可以通过ShaderMaterial实现类似电视雪花或扫描线的动态干扰波纹视觉效果。

生命值 (HP): 10点。

移动方式: 沿着螺旋形路径向下移动。整体向下速度约为100像素/秒。一边向下移动，一边围绕一个假想的中心点进行半径逐渐减小的圆周运动（通过脚本计算位置：x = center_x + radius * cos(angle), y = current_y + radius * sin(angle)，其中radius随时间减小，angle随时间增加，current_y稳定增加）。同时，菱形自身通过rotation_degrees += spin_speed * delta快速旋转。

攻击方式: 拥有一个Timer节点，每隔2秒触发一次。触发时，发射1发“逻辑失序弹 (Logic Disarray Missile)”。

逻辑失序弹: 一个小型的、外观也像菱形的追踪子弹（可以用简单的move_toward和look_at玩家的逻辑实现非完美追踪）。速度较慢（例如180像素/秒）。如果命中玩家，不会直接造成高额伤害（例如0.5点），但会给玩家施加一个持续2-3秒的负面状态：屏幕边缘出现几何干扰条纹，或者玩家的射击准星轻微晃动。负面状态通过在玩家UI上叠加一个半透明的干扰TextureRect或用脚本轻微扰动射击方向向量来实现。

生成方式: 不独立生成，而是在生成普通敌人（如三角斥候）时，有25%的几率将其替换为菱形干扰者。

得分: 30分。掉落货币: 5-8单位。

特效: 受伤时干扰波纹加剧。死亡时菱形碎裂成四个独立的三角形，并释放出一圈短暂的、视觉可见的环状干扰波粒子。

3.7. 新增故事模式精英怪 (M.AI剧情相关):

“纯粹几何体 - 柏拉图卫士 (Pure Geom - Platonic Guard)”:

外观: 由单一类型的完美柏拉图多面体（正四面体、正六面体、正八面体、正十二面体、正二十面体）构成，颜色纯白或银色，带有强烈的内部光芒。尺寸比普通执行者稍大。

行为: 通常3-5个一组出现，每种多面体对应一种固定行为模式：

正四面体卫士 (Tetrahedron Guard): HP较低，速度快，进行短距离冲锋撞击。

正六面体卫士 (Hexahedron Guard): HP中等，移动较慢，会周期性地在自身周围生成一个短暂的方形能量屏障（可抵挡子弹）。

正八面体卫士 (Octahedron Guard): HP中等，会向八个顶点方向同时发射短促的能量镖。

正十二面体卫士 (Dodecahedron Guard): HP较高，会缓慢旋转并从每个面轮流发射追踪性较弱的能量球。

正二十面体卫士 (Icosahedron Guard): HP很高，会周期性地向周围释放一圈密集的、扩散的几何碎片弹幕。

掉落: 少量货币，可能有几率掉落对应形状的“几何微晶”（用于特定改装的材料，如果设计了制造系统）。

“心灵渗透者 (Mind Infiltrator)”:

外观: 一个由许多细小几何光线或粒子构成的、不断变换形态的模糊球体或不规则多面体。颜色可能是不断变化的彩虹色或令人不安的暗紫色。

行为: 移动缓慢且飘忽。不直接发射实体子弹。周期性地（例如每3-5秒）向以自身为中心的一个大范围区域（视觉上用一个快速扩大并消失的半透明彩色波纹表示）释放一次“精神干扰波”。

干扰效果: 玩家如果处于波及范围内，屏幕会在接下来2-3秒内出现以下随机一种或多种效果：

视觉扭曲: 屏幕像素发生短暂的、类似水波纹或马赛克的扭曲。

颜色失真: 屏幕整体色调发生改变，或颜色反转。

虚假幻象: 屏幕上随机位置短暂出现一些无害的、半透明的敌人或弹幕幻影。

操作干扰: 玩家的移动控制短时间内变得迟钝或方向轻微随机偏移（通过给输入向量乘以一个小的随机扰动因子）。

击破方式: 需要在其释放干扰波的间隙快速集火将其击破。

掉落: 中量货币，可能有几率掉落“心智稳定器”改装件的碎片。

“几何改造兵 (Geometrically Altered Soldier)”:

外观: 仍然保留部分扭曲的、细长的人形轮廓（可以用多个连接的Polygon2D段构成），但身体大部分被冰冷的、棱角分明的几何结构（例如，肩膀是立方体，手臂是棱柱，头部是尖锐的金字塔形）所取代。关节处是明亮的能量连接点。颜色可能是代表“秩序使徒”的深蓝色或灰色。

行为: 移动速度比普通人类单位快，可能会进行短距离的滑行或跳跃（通过Tween快速改变位置）。使用手中的几何能量武器（一个与手臂集成的几何发射器）进行快速、精准的点射或短连射。

弱点: 关节处的能量连接点可能是其弱点，击中时造成额外伤害。

死亡: 死亡时，身体的几何结构会崩溃解体，能量连接点发生小型爆炸。

掉落: 货币，可能掉落人类科技与AI技术结合的改装件。

“莫比乌斯追踪者 (Mobius Stalker)”:

外观: 一条细长、扁平、扭曲的带状几何敌人（可以用一个自定义绘制并带有扭曲动画的Sprite2D，或者用多个Line2D段连接并使其整体按特定路径移动来实现）。颜色可能是不断在两种对比色之间平滑过渡（例如黑与白，或深蓝与亮银），以强调其“两面一体”的特性。

行为: 沿着预设的、类似莫比乌斯环的复杂闭合路径在屏幕上快速穿梭，其路径可能部分延伸到屏幕外再从另一侧以“不可能”的角度重新进入（通过在屏幕边缘设置传送点实现视觉欺骗）。极难被持续瞄准。会从其“边缘”随机角度向玩家发射少量高速的“扭曲能量镖”（弹道也可能略带弧线）。

挑战: 对玩家的预判和快速反应能力要求较高。

掉落: 货币，可能掉落与“空间操控”或“闪避”相关的改装件。

“克莱因构造体 (Klein Construct)”:

外观: 一个大型的、由多个几何部件（如圆柱、圆环、球体段）拼接组合而成的、视觉上暗示克莱因瓶“瓶颈自入”结构的敌人。其主体颜色可能是一种半透明的、流动的能量材质，内部隐约可见一个跳动的几何核心。

行为: 移动缓慢，但会周期性地进行一种“相位翻转”的动作——其部分外部结构会向内收缩，然后从“另一面”（视觉上可能是从身体的某个开口或能量旋涡中）重新伸出，改变其攻击朝向或暴露/隐藏弱点。

攻击方式:

“瓶口喷射”: 从其“瓶口”处（一个明显的能量汇聚点）持续喷射出大量几何碎片弹幕。

“内部回响”: 当其核心暴露时，如果受到攻击，会从身体其他“开口”处反射出少量追踪玩家的能量球。

弱点: 其内部的几何核心。只有在其进行“相位翻转”或特定攻击导致核心短暂暴露时，才能对其造成有效伤害。玩家可能需要利用“维度滑行”能力在特定时机“穿透”其外壳的一层薄弱能量场，或者等待其“瓶口”大开时攻击。

掉落: 大量货币，稀有的拓扑几何改装件（例如，允许子弹有几率“相位穿透”敌人护盾的模块）。

4. Boss 系统 (Boss System)

通用 Boss 属性:

生成条件: 在故事模式中，Boss在特定剧情节点或关卡末尾出现。在肉鸽模式中，当玩家累计得分首次达到1000分的倍数时（1000, 2000, 3000...）触发Boss战。

选择机制 (肉鸽模式): 从当前已解锁的Boss池中随机挑选一只生成。解锁顺序：首次达到1000分时，若故事模式未解锁Boss 1，则生成一个通用型肉鸽Boss；若故事模式Boss已解锁，则优先从故事Boss中选择。击败故事Boss 1后，其加入肉鸽随机池。以此类推。

生成过程: Boss出现前有特殊音效和视觉警告（如屏幕边缘闪烁红色警告条，屏幕轻微震动）。Boss从屏幕边缘（通常是顶部）以特定的入场动画（例如，由几何粒子汇聚而成，或从空间裂隙中出现）移动到战场中央或指定战斗位置。入场期间通常无敌。Boss战期间，普通敌人的生成会暂停或大幅减少。

血量显示: 所有Boss战期间，在屏幕顶部中央显示一个醒目的Boss血量条（一个长的TextureProgress节点，其填充颜色和背景可自定义）。

血量递增 (肉鸽模式): 每成功击败一个Boss (无论种类)，后续生成的Boss的基础生命值将增加50%。计算方式：当前Boss基础HP = 原始基础HP * (1 + 0.5 * 已击败Boss总数)。故事模式Boss血量固定。

得分: 击败Boss本身不直接获得分数（或只给少量分数），但会触发其他奖励。

奖励: 击败Boss后固定奖励大量游戏货币。故事模式Boss会掉落关键剧情道具和特定的改装件/蓝图。肉鸽模式Boss会掉落多个随机道具和改装件。

击败特效: Boss被击败时有显著的、多阶段的爆炸或解构特效（通过AnimationPlayer控制多个粒子系统和Sprite2D的动画），并播放史诗般的击破音效。

故事模式Boss特性: 每个Boss都有独特的几何美学和攻击模式，与剧情紧密相关，其行为和对话（通过屏幕上方的对话框或Boss本体发出的几何信号辅以字幕）会揭示更多关于“调律者”和故事背景的信息。

4.1. “初级谐振塔 (Lesser Resonance Spire)” (故事模式序章Boss)

外观: 一个固定在战场背景（可能是一个被初步“几何化”的星球表面）上的、巨大的、由多个旋转的同心圆环和垂直的能量棱柱组成的塔状结构（高度约为屏幕高度的2/3）。塔顶有一个巨大的、明亮的能量水晶Sprite2D，是其核心。塔身的不同节段会周期性地打开几何舱口。颜色为冰冷的白色和淡蓝色。

生命值 (HP): 基础50点（核心HP）。

特殊能力 - 能量屏障: 核心水晶外层包裹着一个半透明的几何能量屏障（一个Sprite2D或Polygon2D）。该屏障会周期性地（例如每5秒）开启（完全不透明，免疫伤害）和关闭（半透明，可以被攻击）。屏障状态通过其透明度和发光强度变化来指示。

移动方式: 固定不动。

攻击模式:

模式A - 三角弹幕 (来自塔身): 塔身不同位置的几何舱口打开，同时向360度随机方向发射大量（例如20-30枚）“几何碎晶”。发射频率中等（例如每2-3秒一次）。

模式B - 聚焦射线 (来自塔顶): 塔顶核心短暂蓄能（核心变亮，发出嗡鸣声），然后向玩家当前位置发射一道持续2-3秒的高能射线（一个细长的Line2D或拉伸的Sprite2D，带有强烈的辉光和粒子效果）。射线会缓慢追踪玩家，需要玩家持续移动躲避。

模式C - 斥候增援 (来自塔身): 周期性地（例如每10-15秒），塔身的几个大型舱口打开，从中释放出一批（3-5个）“几何先驱 - 三角斥候”。

战斗阶段: 当核心HP低于50%时，塔的旋转部件加速，弹幕发射频率提高，聚焦射线可能变为两道同时发射。

掉落物: 少量货币，一个基础的“三角形攻击模块”改装件（效果：射速少量提升，或子弹伤害+0.5）。

4.2. “秩序守卫者 - 监察矩阵 (Order Warden - Scrutiny Matrix)” (故事模式第一幕Boss - 卡俄斯原型机)

外观: 由一个悬浮在场地中央的、不断旋转的复杂几何核心（例如一个由多个互锁的立方体和八面体组成的、直径约30像素的Node2D，表面有流动的代码纹理Sprite2D）和环绕其高速飞行的3个独立的“协同攻击单元 (Synergy Attack Drone)”组成。每个无人机都是一种不同的基础几何形状：一个红色发光的正四面体，一个绿色发光的正六面体，一个蓝色发光的正八面体。无人机尺寸约15像素。

生命值 (HP): 核心HP基础80点。每个无人机HP基础20点。核心在所有无人机被摧毁前处于无敌状态（视觉上被一层能量薄膜包裹）。

移动方式: 核心在场地上半区缓慢地进行无规则漂移。无人机围绕核心进行高速、精确的几何编队飞行（例如，排成等边三角形、直线、或围绕核心公转）。

攻击模式 (由无人机协同完成，核心不主动攻击，但会发出指令信号——视觉上是核心向无人机发射细微的能量束):

模式A - 几何封锁 (阵型攻击): 三个无人机快速移动到玩家周围，形成一个等边三角形阵型，然后同时从各自的顶点向三角形中心（即玩家可能的位置）发射一束短促的能量射线。之后迅速散开并重新编队。

模式B - 能量链接切割 (区域拒止): 三个无人机之间形成可见的、高亮的能量链接（用Line2D实现，带有电弧粒子效果）。这些链接会构成一个旋转的或移动的三角形切割区域，玩家需要躲避触碰这些链接。链接持续数秒后消失。

模式C - 单体追击 (无人机独立攻击): 当某个无人机是最后一个存活的，或者在特定阶段，它会脱离编队，单独高速追击玩家，并持续发射该无人机对应形状的特色弹幕（例如，四面体发射追踪性三角镖，六面体发射直线方块弹，八面体发射散射能量球）。

核心过载 (最终阶段): 当所有无人机被摧毁后，核心的无敌护盾解除。核心开始不稳定地闪烁，并疯狂地向四周360度无规则地发射大量小型几何能量爆发（各种形状的粒子或短寿命子弹）。这是攻击核心的最佳时机。

战斗提示: 在“几何封锁”攻击前，无人机在目标位置短暂停留并有充能光效。在“能量链接切割”前，无人机之间会有预警性的能量预连接（颜色较暗淡的细线）。

掉落物: 中量货币，解锁“卡俄斯”AI的第一个“数据片段”（一个剧情道具，拾取后在数据日志中增加条目），一个“多核处理单元”改装件（效果：所有主动技能的冷却时间减少10%，或允许额外装备一个小型被动改装模块）。

4.3. 艾拉/里奥 - “完美形态 mk.I (Perfect Form mk.I)” (故事模式第二幕Boss)

外观: 一艘由艾拉/里奥驾驶的、经过“调律者”技术初步改造的人类飞船。其基础形态可能仍保留一些流线型的人类飞船特征（例如，类似战斗机的机翼轮廓，但边缘都已锐化和几何化），但机身大部分被覆盖着光滑、无缝的、散发着冰冷白色或淡金色光芒的完美几何装甲（例如，由多个精确拼接的六边形或菱形面板构成）。武器系统是外露的、高度对称的几何发射器（例如，机翼两侧各有一个旋转的多棱镜炮塔）。驾驶舱部分可能是一个半透明的几何晶体结构，隐约可见艾拉/里奥的剪影（一个风格化的立绘投影）。

生命值 (HP): 基础120点。

特殊能力 - “几何秩序场”: Boss周围（半径约100-150像素）存在一个几乎看不见但影响弹道的力场。玩家发射的子弹进入该力场后，其飞行轨迹会受到轻微但持续的“校正”，逐渐偏向平行于力场中心线（即Boss自身）的方向，或者速度会略微降低。力场的效果会周期性地减弱（例如，每5秒减弱2秒，期间Boss的辉光会黯淡一些），这是攻击的良机。

移动方式: 快速（比玩家基础速度略快）、精准、优雅的几何路径移动（例如，进行完美的圆弧机动、菱形走位、或在攻击间隙进行短促的、朝向屏幕边缘再折返的“完美闪避”）。其移动总是显得“有条不紊”和“经过计算”。

攻击模式 (充满“理性”与“秩序”，弹幕通常排列整齐，轨迹精确):

模式A - “调和光束 (Harmonizing Beams)”: Boss机体两侧的炮塔同时向前发射多道（例如4-6道）平行的、等距分布的能量光束（细长的Line2D或拉伸的辉光Sprite2D，持续2-3秒）。光束会缓慢地横向扫过一定区域，或整体向前平移，需要玩家在光束间精确穿梭。

模式B - “定序飞弹 (Sequenced Missiles)”: Boss从机体下方或后方发射一连串（例如5-7枚）小型几何飞弹（例如，五角星形状）。这些飞弹会以固定的时间间隔（例如每0.5秒）或按照预设的数学序列（例如，屏幕上会短暂显示1, 1, 2, 3, 5...的数字提示，对应第几枚飞弹即将爆炸）依次在预定位置（可能是玩家当前位置的预判点，或战场上的固定几何格点）爆炸，形成小范围的圆形伤害区域。

模式C - “纯粹形态投射 (Pure Form Projection)”: Boss短暂停留并充能（机身发出强烈光芒），然后在战场上随机选择3-4个位置，同时投射出巨大的（约等于玩家数倍大小）、旋转的柏拉图多面体半透明能量虚影（例如，正方体、正四面体）。这些虚影会持续存在数秒，缓慢移动或旋转，对触碰到的玩家造成高额伤害。

对话驱动: 战斗开始前、战斗阶段转换时（例如HP低于66%、33%）、以及战斗结束前，艾拉/里奥会通过屏幕上方的对话框（配合其立绘）向主角喊话，阐述其“完美秩序”的理念，质疑主角的“混乱”和“徒劳抵抗”，或在被击败前流露出复杂的情感（如不解、失望、或一丝隐藏的痛苦）。

战斗阶段: HP降低后，Boss的移动速度和攻击频率会提升，弹幕密度增加。“几何秩序场”的减弱间隙可能缩短。可能会解锁新的攻击组合或更复杂的弹幕模式。

掉落物: 大量货币，剧情关键道具（例如，艾拉/里奥遗落的“加密数据核心”，记录了其心路历程的片段或关于“调律者”更深层运作机制的线索），解锁“完美几何”系列改装件的制作蓝图（例如，“秩序装甲板”：提供高额防御但略微降低机动性；“校准射击模块”：小幅提升射击精度但略微降低射速）。

4.4. 新增故事模式Boss (M.AI剧情相关):

UHDC“净化者”舰队指挥官 - “铁序上校 (Colonel Iron Order)” (故事模式第二幕可选Boss/精英战，如果玩家选择违抗议会命令或调查议会阴谋线索时遭遇):

外观: 驾驶一艘改装的UHDC重型巡洋舰（基础是人类的块状、功能性设计风格），但外部加装了大量统一规格的、棱角分明的附加装甲板和外挂武器模块（几何形状以长方体、楔形、六角柱为主），使其看起来像一个移动的、缺乏美感的几何堡垒。涂装是冰冷的哑光灰色或黑色，带有UHDC的徽章但被部分划掉或覆盖了新的“秩序”符号（例如一个交叉的直线构成的X）。

生命值 (HP): 基础100点。拥有较高的装甲值（减免一定百分比的伤害，或需要特定类型的攻击才能有效造成伤害，例如玩家的“悖论共鸣”可以暂时削弱其装甲）。

特点: 防御极高，火力凶猛但缺乏灵活性和变化。战斗风格强调纪律、压制和不惜代价完成目标。不会有太多“花哨”的机动。

攻击模式:

饱和弹幕: 从舰体两侧的多联装炮管中持续发射大量标准动能弹或低能量激光，形成密集的弹幕墙，压制玩家的移动空间。

正面主炮“秩序裁决”: 舰首的主炮（一个巨大的长方体炮管）需要较长时间充能（炮口发出红光并有警告音效），然后发射一道威力巨大、持续时间较短的直线冲击波，几乎可以秒杀护盾不足的玩家。发射后有较长的冷却时间。

浮游几何炮台“纪律信标”: 周期性地从舰体释放2-3个小型的、自主行动的浮游炮台（例如，带有小型推进器的立方体或八面体）。这些炮台会缓慢追踪玩家并发射少量干扰性子弹，需要优先清除。

电子战干扰 (可选): 可能释放干扰波，短时间内使玩家的HUD部分信息消失或雷达失效。

对话: 上校的对话冷酷而坚决，强调“为了大多数人的稳定，少数异议必须被清除”，“混乱是原罪，秩序是救赎”。

掉落物: UHDC内部关于“调律者”的机密评估报告（揭示议会高层的恐惧和妥协倾向），一些军用级改装件（例如，提升物理抗性或弹药容量的模块）。

艾拉/里奥 - “完美形态 mk.II (Perfect Form mk.II) 与 内心回响 (Inner Echo)” (故事模式第三幕Boss，在“至谐之域”深处遭遇):

外观: 飞船形态在mk.I的基础上更加进化和抽象，几乎完全摆脱了人类设计痕迹，呈现出一种流动而又绝对对称的、仿佛由液态光构成的完美几何美感（例如，主体是一个不断变换棱角的晶体，周围环绕着多个莫比乌斯环状的能量带）。在战斗进入第二阶段（HP低于一定值，或在特定剧情选择后）时，其核心（可能是驾驶舱晶体结构）会暴露出来，同时其周围会浮现出多个不稳定的、闪烁着杂乱色彩的“不完美几何碎片 (Imperfect Geometric Fragments)”（可能是扭曲的心形、破碎的音符、断裂的螺旋等抽象符号的几何化表现）。

生命值 (HP): 基础150点（第一阶段，飞船主体）+ 50点（第二阶段，核心HP，同时每个“不完美几何碎片”也有少量独立HP）。

特殊机制 - “内心回响 (Inner Echo)”: 第二阶段，玩家攻击并摧毁那些“不完美几何碎片”可以对Boss的核心造成显著伤害，并且每摧毁一个碎片，都可能触发艾拉/里奥的一段简短的、充满痛苦或迷惘的“内心独白”（以字幕或HUD文字形式显示，例如“为什么...会这样？”“这不是我想要的完美...”）。如果玩家在摧毁所有碎片后再击破核心，可能会导向不同的结局分支或获得特殊奖励。

攻击模式:

第一阶段 (完美形态): 攻击比mk.I更强大、更难以躲避。例如：

“绝对对称弹幕”: 同时从多个对称点发射完全镜像的复杂弹幕序列。

“逻辑闭环陷阱”: 在战场上生成多个持续存在的几何能量环，如果玩家穿过环的顺序错误，会受到伤害或被短暂束缚。

“无限递归射线”: 发射一道主射线，主射线在碰到屏幕边缘或特定障碍物后会分裂成多道子射线，子射线再分裂，形成指数级增长的射线网（但有最大分裂次数限制）。

第二阶段 (核心暴露与内心回响): Boss本体的攻击频率和威力略微下降（可能因为核心暴露或“内心冲突”）。但那些“不完美几何碎片”会随机地、无序地向玩家发射混乱的、轨迹不规则的“情感冲击波”（可能是扭曲的能量团或尖锐的几何碎片）。这些攻击与Boss本体的“完美”攻击形成鲜明对比，使得战场更加混乱。

掉落物: 如果被彻底击败（未净化所有碎片或选择了对抗到底的路线），掉落“调律者”核心区域的最终访问密钥或坐标。如果其“内心回响”被完全“净化”（所有碎片被摧毁），并且玩家在之前的剧情中对艾拉/里奥做出过积极的尝试（例如，对话选择“尝试唤醒”），可能会掉落一个名为“破碎的完美之心 (Fractured Perfection Heart)”的独特改装件（效果：提供强大的属性加成，但每次使用主动技能时有极小几率触发一个随机的正面或负面“情感爆发”效果，例如短暂无敌或短暂操作反向）。

“调律者”的化身 - “宇宙几何元 (Cosmic Geometron)” (故事模式最终Boss):

外观: 一个在屏幕中央（或占据大部分屏幕空间）的、不断演化、重构的巨大几何能量体。它没有固定形态，可能在完美的球体、旋转的超立方体（四维立方体在三维的投影）、无限延伸的分形晶体、甚至克莱因瓶或莫比乌斯巨环的投影等多种宏伟的几何形态之间平滑转换。每一次形态转换都伴随着不同的背景光影效果（例如，背景变为纯粹的数学公式网格，或深邃的高维空间星图）。

生命值 (HP): 极高，分为多个战斗阶段，每个阶段对应一种主要几何形态和攻击模式。

特殊机制 - “宇宙法则干涉 (Cosmic Law Interference)”: Boss的每个主要形态都代表着一种宇宙法则的极端体现，并会改变战场的某些规则。

第一阶段 - “引力奇点形态 (Gravitic Singularity Form)”: (例如，一个巨大的、旋转的黑色球体，周围有吸积盘般的几何光环)。Boss会产生强大的引力场，缓慢地将玩家和所有弹幕（包括玩家自己的）吸向其中心。玩家需要不断抵抗引力，并在引力较弱的区域寻找攻击机会。

第二阶段 - “时间晶格形态 (Temporal Lattice Form)”: (例如，一个由无数发光立方体构成的、类似晶体结构的形态)。战场上会出现多个可见的、不同颜色的几何区域（例如，红色区域时间流速加快，蓝色区域时间流速减慢）。Boss自身也可能在不同区域间快速穿梭，利用时间差进行攻击。

第三阶段 - “逻辑悖论形态 (Logical Paradox Form)”: (例如，一个由多个相互嵌套、不断变化的莫比乌斯环和克莱因瓶构成的超现实形态)。Boss会释放覆盖全屏的“悖论序列攻击 (Paradox Sequence Assault)”：屏幕上会出现多组成排的简单几何符号（例如，▲■●♦），玩家需要在极短的时间内（例如3-5秒）按照某种隐藏的逻辑规则（例如，找出与其他符号都不同的那个，或者按照特定颜色/数量顺序）用子弹射击正确的符号。如果射击错误或超时，会受到巨大伤害或被施加负面状态。正确的逻辑规则会在攻击前通过“调律者”的“信息流”短暂提示（可能是快速闪过的公式或几何变换动画，需要玩家快速理解）。

攻击模式: 每种形态都有其独特的、场面宏大且极具压迫感的攻击方式，强调视觉奇观和对玩家操作技巧、反应速度与逻辑判断能力的极致考验。例如：

引力奇点形态: 释放螺旋状的吸积弹幕，或在其周围生成多个小型“事件视界”（触碰到会被短暂拉扯并受到伤害）。

时间晶格形态: 发射在不同时间流速区域行为各异的弹幕（例如，进入快速区会加速分裂，进入慢速区会变得巨大且缓慢但威力增强）。

逻辑悖论形态: 除了“悖论序列攻击”，还会发射出能够“复制”或“反转”玩家行为的特殊弹幕（例如，一种“镜像机器人”会模仿玩家的移动和射击，但子弹对玩家有害）。

对话/信息流: 整个战斗过程中，“调律者”会持续通过其化身周围不断演化的复杂几何图案和屏幕上方显示的、由纯粹数学符号和逻辑语句构成的“信息流”（辅以UI“翻译”出的哲学思辨文本）阐述其“宇宙调律”的必要性、其对“完美”的定义、以及对人类这种“充满悖论的混乱生命体”的“最终诊断”。

掉落物: 根据玩家在最终对话中的选择和达成的结局而不同。可能是宇宙的终极奥秘（一段无法完全解读的、蕴含着更高维度信息的光之代码块，作为真结局的象征），也可能在彻底的毁灭中什么都不掉落（坏结局），或者掉落一个象征“新开始”或“未完待续”的神秘几何种子（为可能的续作或新模式埋下伏笔）。

5. 敌人生成系统 (Enemy Generation System)

5.1. 波次管理器 (Wave Manager):

职责:

(根据游戏时间/得分/剧情等决定敌人类型、管理生成间隔、实现难度递增、触发特殊波次和Boss战 保持不变)

响应地图扩展与滚动方向: 波次管理器需要能够获取当前的游戏区域级别和滚动方向。

地图扩展响应: 当游戏区域扩展时，波次管理器应调整其生成敌人的位置基准（例如，确保敌人在新的、更大的屏幕边界外生成）和可能的生成密度（例如，在更大的地图上，为保持同等挑战，可能需要略微增加同屏敌人的期望数量或波次频率）。

滚动方向响应: 敌人默认的出现方向（例如，是从屏幕顶部、底部、左侧还是右侧）应根据当前关卡的滚动方向进行调整。敌人的初始移动向量也应与滚动方向协调。

5.2. 基础生成 (Basic Spawning):

位置: 大多数普通敌人从屏幕顶部 (Y坐标小于0，例如-50到-100) 的随机X轴位置（在屏幕宽度范围内）生成，然后进入屏幕。部分特殊敌人或波次可能有特定生成位置（例如，从屏幕两侧或角落，或由特定“母体”敌人召唤）。

机制: 使用Timer节点或基于delta时间的计数器来控制每种（或每组）敌人的基础生成间隔。当计时器到期或计数器达到阈值时，实例化（PackedScene.instantiate()）对应的敌人场景，设置其初始位置和属性，然后将其添加到主游戏场景的子节点中。

5.3. 难度递增 (Difficulty Scaling - 主要用于肉鸽模式，故事模式难度由关卡设计决定):

时间因子: 游戏时间（可以用一个全局变量记录总游戏秒数）每流逝例如60秒。

效果: 所有当前可生成的非Boss敌人的生成速率提高15%。这意味着它们的平均生成间隔时间缩短。

计算: current_spawn_interval = base_spawn_interval / (1 + 0.15 * floor(total_game_seconds / 60))。

上限: 生成速率最多提高到原始速率的3倍 (即间隔时间缩短为原来的1/3)。达到上限后，时间不再进一步缩短间隔。

视觉反馈: 每次难度提升时，可以在屏幕中央短暂显示一个几何风格的提示信息（例如，一个旋转的立方体展开成文字“DIFFICULTY UP!”）。

5.4. 敌人强化 (Enemy Reinforcement - 主要用于肉鸽模式):

触发: 每当一个Boss被成功击败后。

效果: 所有现有和未来生成的非Boss敌人的基础生命值提高50%。此效果是累加的。

示例: 击败第1个Boss，敌人HP x 1.5。击败第2个Boss，敌人HP x (1.5 + 0.5) = x 2.0 (相对于原始基础HP)。以此类推。

Boss血量影响: 此强化不影响Boss的基础血量（Boss血量有自己的独立递增规则，见4.通用Boss属性）。

无上限: 此生命值提升效果没有明确上限，理论上可以无限叠加。

5.5. 特殊波次 (Special Waves):

触发: 由波次管理器根据时间、分数、特定条件（例如，玩家连续击杀数达到一定值）或故事模式的剧情脚本触发。

形式:

阵型波次 (Formation Waves): 敌人以预设的几何队形出现并移动。实现方式：在短时间内，在特定相对位置（相对于一个共同的生成中心点或路径）连续生成多个同类型或不同类型的敌人。

圆形阵列: 敌人生成位置分布在一个圆周上，然后向圆心或外侧移动。

V 形阵列: 敌人生成位置排列成V字形，整体向前或向两侧扫过。

直线墙: 一排敌人紧密排列（X坐标接近，Y坐标相同但略有偏移以避免重叠），同时向下移动。

交叉火力: 两队敌人分别从屏幕左上和右上（或左侧和右侧）生成，沿对角线或特定路径向屏幕中央交叉移动并攻击。

特定敌人强袭 (Focus Wave): 短时间内（例如5-10秒）大量生成某一种特定类型的敌人（例如，“全屏三角斥候警告！”）。波次管理器会临时提高该类型敌人的生成频率。

混合精英波次 (Elite Squad Wave): 同时或先后生成一小组由多种精英怪（如“五角堡垒”、“菱形干扰者”、“柏拉图卫士”等）组成的强力波次。

压倒性攻势 (Overwhelm Assault - 特定模式或高潮事件): 触发后，在接下来的一分钟内，波次管理器会以极高的频率、无视常规间隔地从各个方向生成大量不同类型的敌人（包括普通和部分精英），形成持续不断的弹幕和敌人潮。此期间背景音乐会变得异常激昂或紧张。实现时需要注意性能优化，避免瞬间生成过多节点导致卡顿。

故事模式特殊波次 (M.AI剧情相关):

“几何重构波 (Geometric Reconfiguration Wave)”: 战场上预先存在的一些“未激活的几何残骸”（特殊标记的静态Sprite2D或Node2D），在特定时刻被一道来自屏幕外的几何光束照射（一个快速移动的Line2D或粒子束），然后这些残骸发生“变形”动画（通过AnimationPlayer或Tween改变其形状、大小、颜色），转变为具有攻击性的敌人单位（例如，从一堆无害的方块变成一个“几何执行者”）。

“拓扑陷阱波 (Topological Trap Wave)”: 战场边缘（例如屏幕的左侧和右侧）的背景视觉发生扭曲（通过ShaderMaterial使背景纹理变形），并出现一个可见的“旋涡”或“裂隙”Sprite2D。如果玩家过于靠近（通过Area2D检测），玩家会被暂时“吸入”——主场景的玩家节点暂时隐藏，同时切换到一个小型的、预设好的“口袋空间”场景，该场景中充满了密集的、固定轨迹的弹幕。玩家需要在其中生存一段时间（例如5-10秒，由Timer控制），然后自动返回主战场（玩家节点重新显示在被吸入的位置附近）。

“秩序校正突袭 (Order Correction Strike)”: 在玩家连续击败大量敌人或对某个标记为“调律场域节点”（可能是个可被攻击的场景物件）造成显著破坏后，会触发一次由2-3个高阶“几何执行者”（例如“三角追猎者”、“五边塑形者”）或1-2个“柏拉图卫士”组成的快速突袭小队，它们会从屏幕的意外方向（例如侧面或后方）高速进入战场，并优先集火玩家。

频率: 例如每1-2分钟（肉鸽模式）或在故事模式的特定脚本点触发一次特殊波次，增加游戏节奏变化。

6. 武器/道具系统 (Weapon/Item System - 游戏内临时拾取)

6.1. 道具通用设定:

外观: 掉落物是带有特定标识的几何图形Sprite2D或Polygon2D，包裹在一个可拾取的Area2D中。使用醒目的颜色和内部字母/符号区分。例如：

T (连续射击): 蓝色背景方块，内部有白色字母 'T' 的Sprite2D。

A (追踪子弹): 紫色背景方块，内部有白色字母 'A' 的Sprite2D。

S (散射): 橙色背景方块，内部有白色字母 'S' 的Sprite2D。

(回血): 白色背景方块，内部有红色心形图案 (由几个Polygon2D拼成) 的Sprite2D。

掉落: 普通敌人被击败时有20%基础概率（可配置）从其死亡位置掉落一个随机道具 (T, A, S, + 中的一种)。掉落物会从敌人死亡位置产生，可以有一个短暂的向上抛物线动画（通过Tween position.y）或原地缓慢旋转（Tween rotation_degrees）等待拾取。

拾取: 玩家的Area2D与道具的Area2D发生碰撞时，道具被拾取。拾取时道具实例queue_free()，并触发玩家对象中对应的道具效果逻辑。同时播放拾取音效和在拾取点产生一个小的闪光粒子效果。

持续时间: T, A, S 道具的基础持续时间为20秒。每个激活的道具都有一个独立的Timer节点在玩家对象中计时。

叠加: 拾取相同类型的道具 (T, A, S) 时，其效果的持续时间会叠加延长，对应Timer的wait_time增加20秒，无时间上限。

Boss 掉落:

命中掉落: 每当Boss被玩家子弹累计击中N次（N为一个在此区间内随机决定的阈值，例如20到25次，Boss脚本中有一个计数器），必定从Boss当前位置掉落一个随机类型的道具 (T, A, S, +)。计数器在掉落后重置。

击败掉落: Boss被彻底击败时，固定从其死亡位置掉落3个 + 回血道具。

UI 显示: 玩家HUD上应有专门区域显示当前激活的 T, A, S 道具的图标及其剩余时间的倒计时条（可以用一个TextureProgress或ProgressBar实现，其值与对应Timer的time_left关联）。

6.2. 道具效果详解:

连续射击 (Triple Shot - T):

效果: 玩家的射击逻辑中增加一个判断：如果T道具激活（对应布尔变量为true），则每次射击时，除了在正前方发射一枚子弹外，还在其左右两侧略微偏移（例如，正负10度角，或平行偏移5-10像素）的位置额外各发射一枚相同的子弹。总共3发。

射速提升: 同时，玩家的基础射击计时器（Timer）的wait_time临时减半 (例如从0.2秒变为0.1秒)，即射速变为原来的2倍。道具失效后恢复原值。

总输出: 结合子弹数和射速，火力大约提升为原来的 3 * 2 = 6 倍。

追踪子弹 (Auto-Aim / Homing - A):

效果: 如果A道具激活，玩家发射的子弹（在其_physics_process中）会增加追踪逻辑。

追踪性能: 子弹会寻找屏幕内最近的敌人作为目标。每帧计算从子弹当前方向到目标方向的角度差，然后将子弹的当前方向向量向目标方向向量旋转一个小的、固定的角度（例如每秒旋转30-60度，通过direction.rotated(turn_angle * delta)实现）。这种“轻微”追踪意味着子弹有最大转向速率限制，无法进行急转弯。如果目标在子弹飞行途中死亡或移出屏幕，子弹会继续直线飞行或尝试锁定下一个最近目标（如果实现了目标切换逻辑）。

子弹外观: 可考虑在A道具激活时，玩家发射的子弹Sprite2D或Polygon2D的颜色发生轻微改变（例如，头部带有些微紫色光晕，通过改变modulate或替换材质）。

散射 (Spread Shot - S):

效果: 如果S道具激活，玩家每次射击时，会同时发射5发子弹。这3发子弹以玩家当前瞄准方向为中心，在一个固定的扇形角度（例如60度）内均匀分布。

子弹方向: 可以通过旋转基础方向向量得到每发子弹的方向。

回血 (Health Up - +):

效果: 拾取时，如果玩家current_health < max_health，则current_health恢复1点（或特定数值），但不超过max_health。

护盾转化: 如果玩家current_health == max_health，则尝试增加一层护盾（如2.5.所述），直到达到最大护盾层数。

无效情况: 如果生命值和护盾都已满，则此道具无效（或可考虑给予少量分数奖励，例如100分）。

6.3. 组合效果 (Synergies): 当玩家同时激活多种射击类道具 (T, A, S) 时，效果会组合：

T + S (连续射击 + 散射):

实现方式: 每次射击时，首先应用T的效果（射速加倍，准备发射3束平行的火力）。然后，每束火力都应用S的效果（变成5发扇形弹幕）。即，玩家以双倍射速，每次发射出3束平行的、各自包含5发呈90度扇形散开的子弹。总计可能是15发子弹（需要注意性能，子弹数量过多可能导致卡顿，可以考虑优化为“每次射击发射5发扇形弹幕，但这5发子弹每发都是由3个紧密平行的子弹构成”，视觉上类似但实际子弹数为5x3=15，或者直接设计为一种新的、视觉更华丽的“9连射”弹幕形态，例如，三束扇形，每束3发，覆盖更宽的角度）。选择一种明确且易于实现的方案：以S的5发扇形为基础，每发子弹由T效果产生的3个紧密平行的子弹构成。即每次射击发射5组，每组3颗平行子弹，共15颗，保持S的90度散射范围。

A + S (追踪 + 散射):

效果: 每次射击发射5发扇形子弹（来自S），这5发子弹都具有A的追踪能力，各自独立追踪最近的敌人（或各自随机选择一个屏幕内的敌人进行追踪，以分散火力）。

T + A (连续射击 + 追踪):

效果: 每次射击发射3发（或按T+S组合后的多发）子弹（来自T），这3发（或多发）子弹都具有A的追踪能力，各自独立追踪。

T + A + S (三者组合):

效果: 结合上述组合效果。例如，每次射击以双倍射速发射5组扇形弹幕，每组由3颗紧密平行的子弹构成，并且所有发射出的子弹（总计15发）都具有A的追踪能力。实现上，就是在T+S的弹幕生成逻辑基础上，为每一颗最终生成的子弹附加A的追踪脚本或行为。

7. 改装系统 (Modification System) - 详细规格

7.1. 改装件获取与掉落:

来源:

Boss 首杀 (故事模式): 击败故事模式中的特定Boss（如艾拉/里奥，监察矩阵等）后，必定掉落其对应的独特改装件（例如，艾拉/里奥掉落“破碎的完美之心”，监察矩阵掉落“卡俄斯数据片段”等）。这些改装件通常具有与Boss特性相关的独特效果。

常规掉落触发 (所有模式): 系统维护一个全局的“非Boss敌人击杀计数器”。

计数与概率: 每当累计击杀达到150个非Boss敌人时，进行一次掉落判定。

共享掉落池: 掉落判定成功时，系统会从一个“已解锁的常规改装件池”中随机选择一个掉落。“已解锁的常规改装件池”包含一些通用的、非剧情独特的改装件（例如，提升基础属性、改变基础武器特性的模块）。故事模式中击败特定敌人或完成任务也可能解锁新的常规改装件加入此池。

掉落后重置: 任何一种改装件（无论是Boss独特掉落还是常规掉落池中的）成功掉落后，150击杀计数器清零，重新开始累计。

掉落率: 每次达到150杀进行判定的成功率，例如设定为20%-30%。

货币购买: 玩家可以在主菜单的“军械库 (Armory)”UI界面或故事模式中可能出现的“黑市商人”处，使用游戏货币直接购买“已解锁的常规改装件池”中的改装件（如果玩家尚未拥有该类型或想获得更多同类型用于升级），或者购买一些基础的、通用的改装件。某些稀有或剧情独特的改装件可能无法直接购买，或需要先完成特定条件才能解锁购买。

物品栏: 获得的改装件（一个包含类型、等级、效果等信息的数据结构）存储在玩家的全局数据中（例如，一个保存在用户存档中的字典 player_inventory = {"mod_type_A": {"quantity": 2, "level": 1}, "mod_type_B": {"quantity": 1, "level": 3}}），并在改装界面的物品栏UI中显示。

7.2. 改装件效果详解:

通用被动加成: 每个成功装备到玩家飞船网格上的改装件（无论类型），都会根据其类型和等级，提供一些基础的被动属性加成，例如：

使玩家的最大生命值上限 +X (X取决于改装件等级)。

使玩家的最大护盾层数上限 +Y (Y取决于改装件等级)。

少量提升特定武器属性（如射速、子弹速度、伤害）。

改装件类型示例（部分来自原GDD，部分为新增或调整以适应M.AI剧情和拓扑几何主题）：

“谐振力场发生器 (Resonance Field Emitter)” (护盾类):

外观 (掉落物/物品栏图标): 一个带有同心圆或螺旋图案的几何晶体图标。

装备后视觉: 在玩家核心周围一定距离处生成一个可见的、动态的能量力场（例如，一个缓慢旋转的、由Line2D构成的莫比乌斯环，或多个闪烁的几何护盾面板）。

功能: 除了提供基础的“最大护盾层数”加成外，该装置本身可能带有一个独立的“力场强度”值（例如3-5点）。每当玩家的常规护盾被消耗时，如果该力场激活，它可以额外抵挡1次伤害并消耗1点力场强度。力场强度耗尽后，装置进入冷却，数秒后恢复全部强度。

升级效果: 提升力场强度上限、缩短恢复时间、或增加其提供的最大护盾层数被动加成。

“分形弹头复制器 (Fractal Warhead Replicator)” (攻击类):

外观: 一个不断自我复制和组合的几何分形图案图标。

装备后视觉: 在玩家飞船的武器发射点附近附加一个可见的、带有分形细节的几何模块。

功能: 玩家每次射击时，其发射的每颗主子弹在飞行一小段距离后，有一定几率（或固定）分裂成2-3颗威力稍弱的小型子弹，这些小弹头会以轻微的角度向外扩散。

升级效果: 提升分裂几率、分裂出的子弹数量、或分裂子弹的威力/追踪能力，以及其提供的基础伤害/射速被动加成。

“几何构造信标 (Geometric Construct Beacon)” (召唤类):

外观: 一个带有发射信号图案的几何五角星或六边形图标。

装备后视觉: 在玩家飞船后方或侧面附加一个带有小型天线阵列的几何装置。

功能: 每隔固定时间（例如10-15秒），自动在玩家附近（例如身后两侧）通过几何粒子汇聚的方式生成1-2个友方的“小型几何构造体 (Minor Geometric Construct)”。

友方构造体属性: 外观是小型的、颜色与玩家一致的几何形状（例如，旋转的立方体发射短激光，飞行的四面体进行撞击）。拥有少量生命值（例如3-5点），可以被敌方子弹摧毁。会自动寻找并攻击最近的敌人（使用简单的move_toward和定时发射逻辑）。持续存在直到被摧毁。

召唤上限: 场上由该装置召唤的友方构造体总数有上限（例如最多2-4个）。

升级效果: 缩短召唤间隔，增加每次召唤的数量，提升召唤物的生命值/攻击力，或提升召唤上限，以及其提供的被动属性加成。

核心能力改装件 (M.AI剧情独特):

“逆谐振模块 (Counter-Resonance Module)”: 显著提升“悖论共鸣”技能对“调律者”AI敌人（尤其是那些依赖严格逻辑的单位）造成的“逻辑混乱”效果的持续时间和强度。可能还会小幅减少该技能的冷却时间。

“卡俄斯碎片 (Chaos Shard)”: （由“卡俄斯”AI提供或从其残骸中回收）装备后，玩家的射击有极小几率（例如1-5%）触发一次不可预测的“混沌爆发”：可能是向随机方向发射大量额外弹幕，也可能是自身短时间无敌，或者是暂时复制一个敌人的攻击模式反击回去，甚至可能是轻微的自我伤害。这是一个高风险高回报的模块。

“破碎的完美之心 (Fractured Perfection Heart)”: （从艾拉/里奥的“完美形态mk.II”处获得，如果满足特定条件）提供非常强大的全属性被动加成（生命、护盾、攻击、速度）。但每次玩家受到伤害时，有一定几率触发一次短暂的“情感回响”：屏幕边缘出现模糊的、悲伤的几何图案，或者听到艾拉/里奥的一句低语（需要音效配合），对玩家本身无实际负面效果，但营造一种剧情氛围。

拓扑特性改装件 (M.AI剧情独特):

“莫比乌斯线圈 (Mobius Coil)”: 装备后，玩家飞船的能量回复速度（如果设计了能量系统用于技能）提升10-20%。或者，每次成功使用“维度滑行”完美闪避一次原本会命中的攻击时，会回复少量护盾值或技能冷却时间。

“克莱因透镜 (Klein Lens)”: 提升基础武器的有效射程15-25%。或者，使玩家发射的子弹有5-10%的几率“相位穿透”敌人的普通能量护盾（对Boss的特殊护盾无效），直接对其本体造成伤害。

“纽结稳定器 (Knot Stabilizer)”: 减少玩家受到“空间扭曲”、“逻辑干扰”、“精神渗透”等由特定敌人或环境引起的负面状态的持续时间30-50%，或有一定几率完全抵抗该类效果。

改装件升级: 每个改装件都拥有等级（例如1-5级）。玩家可以在改装界面选中一个已拥有的改装件，消耗一定数量的游戏货币（和可能的特定材料，如果设计了材料系统）来提升其等级。升级会增强该改装件的主要功能效果（如上述描述中的百分比、数值、几率等）以及其提供的通用被动属性加成。升级所需的货币和材料随等级升高而增加。

8. 游戏 UI 系统 (Game UI System)

8.1. 整体设计原则:

一致性: 所有UI元素严格遵循几何简约风格。边框、按钮、图标等都由简单的几何线条和形状构成。避免使用过于华丽或拟物化的材质。

字体: 选用极简、无衬线的现代数字风格字体，确保清晰易读。

清晰度: 在弹幕密集的场景下，HUD信息必须清晰易辨。使用高对比度颜色（例如，白色或浅亮色文字/图标，搭配深色或半透明的背景板）。避免过多复杂的装饰。

动态反馈: 关键信息的变化（如生命减少、获得道具、技能冷却完毕、难度提升）应伴有醒目的动画效果（例如，图标的缩放、闪烁、颜色变化、进度条的填充动画）和音效。

布局: 合理安排UI元素位置，避免遮挡过多游戏区域，通常放置在屏幕边缘（如顶部、底部、角落）。

8.2. 开始菜单 (Start Menu):

游戏标题: 使用不同颜色和大小的几何形状（三角、圆、方、菱形等）拼接成艺术化的 "几何射击" 或 "Geometric Shooter" 字样。可以有一个简单的入场动画（例如，各个几何部件从屏幕外飞入并组合成标题）。

模式选择按钮:

故事模式按钮 (Story Mode): 一个带有叙事性几何符号（例如，一个展开的卷轴或路径箭头）的长方形按钮。

肉鸽模式按钮 (Roguelike Mode): 一个带有随机或无限符号（例如，莫比乌斯环或分叉路径）的圆形按钮。

多人游戏按钮 (Multiplayer): 一个带有多个人形剪影（用简单几何体表示）或网络连接符号的六边形按钮。点击后进入联机大厅或创建/加入房间界面。

机库/升级按钮 (Hangar / Upgrades): 一个带有扳手或齿轮几何图标的方形按钮。点击进入玩家永久性升级和改装件购买/升级界面。

设置按钮 (Settings): 一个带有多个小方块组成的设置面板几何图标的按钮。

退出按钮 (Exit): 一个带有X形几何图案的红色按钮。

按钮交互: 鼠标悬停时按钮变亮或轻微放大（通过Tween scale），点击时有按下效果（Tween position.y微小下移）和音效。

背景: 动态背景，例如一个缓慢滚动的深邃星空背景（ParallaxBackground），上面漂浮着各种半透明的、颜色与主题相关的几何形状粒子（CPUParticles2D），粒子大小、速度、透明度各异，营造层次感。

8.3. 游戏界面 (In-Game HUD):

分数显示 (Score Display):

位置: 屏幕右上角。

格式: 使用几何数字风格的字体或简洁数字字体显示当前得分，例如 "SCORE: 12345"。得分增加时数字有短暂的放大或颜色高亮效果。

生命显示 (Health Display):

位置: 屏幕左上角。

表现: 使用几何心形图标（例如，由两个顶部半圆和一个底部的倒三角形Polygon2D拼接而成）代表生命值。每个心形用实心红色（或玩家自定义的强调色）表示当前拥有的生命点，用灰色或空心的轮廓心形表示失去的生命点。旁边可显示数字 "HP: 3/5"。

护盾显示: 在生命值图标旁边或下方，用小的蓝色/白色几何盾牌图标（例如，一个简化的鸢形或六边形）表示当前护盾层数，并显示层数数字 "SHIELD: 2"。

距离显示 (Distance Meter - 主要用于肉鸽模式):

位置: 屏幕右上角，分数下方。

格式: 显示玩家前进的距离，例如 "DISTANCE: 150 M"。数字每秒稳定增加1（或根据游戏内速度计算）。使用几何数字或简洁字体，"M" 单位符号也用几何线条构成。

道具状态 (Power-up Status):

位置: 屏幕左侧，生命值下方，或屏幕底部中央。

表现: 对于当前激活的T, A, S道具，显示对应的几何图标 (见6.1)。每个图标旁边附带一个水平或圆形的倒计时条（TextureProgress或ProgressBar），其填充量与道具剩余时间对应。当拾取叠加时，计时条会充满并重新开始计时。时间快结束时（例如剩余20%），计时条可闪烁红色或图标整体闪烁。

难度指示器 (Difficulty Indicator - 主要用于肉鸽模式):

触发: 每当游戏难度等级提升时。

位置: 屏幕顶部中央，临时显示2-3秒。

表现: 短暂显示一个由几何图形组成的提示信息，例如 "DANGER LEVEL UP!" 或一个旋转的、棱角更分明的几何体展开成 "WAVE INTENSITY INCREASED"。可以伴随从上滑入再滑出的Tween动画和提示音效。内容应包含当前的难度等级（例如，基于时间提升次数和Boss击杀次数计算一个等级数字）。

货币显示 (Currency Display):

位置: 例如屏幕右上角，分数下方或旁边，或者屏幕底部。

格式: 使用一个代表货币的几何图标（例如，一个堆叠的金色方块或一个闪亮的几何晶体）和数字Label显示玩家当前拥有的游戏货币数量。获得货币时，数字有滚动增加的动画效果。

AI友军状态 (AI Companion Status - 如果启用了AI友军系统且有友军出战):

位置: 屏幕左侧，道具状态下方，或玩家头像（如果未来加入）旁边。

表现: 为每个出战的AI友军显示一个小型的几何图标（代表其类型或外观）及其简化的生命条（一个小型的TextureProgress）。如果AI友军有特殊状态（如被控制、低生命），图标可以改变颜色或闪烁。

核心拓扑能力UI (Core Abilities HUD - 故事模式):

位置: 屏幕底部中央，或技能快捷键提示附近。

表现: 为“维度滑行”和“悖论共鸣”等核心技能显示其图标和冷却状态。图标可以是代表其拓扑概念的抽象几何符号。冷却状态可以用一个覆盖在图标上的径向TextureProgress（从满到空表示冷却中，空了表示可用）或数字倒计时Label来显示。技能可用时图标可以高亮或发光。

8.4. Boss 血量显示 (Boss Health Bar):

位置: 固定在屏幕顶部中央，仅在Boss战期间显示。

设计: 一个长条形的TextureProgress节点作为血量条。

背景: 深灰色或半透明黑色的底框TextureRect或NinePatchRect。

填充: 使用醒目的红色（或其他与Boss威胁等级匹配的颜色，如紫色、暗金色）填充来表示当前血量比例。填充纹理可以带有几何暗纹。

装饰: 血量条两侧可以有与当前Boss主题色和几何风格匹配的三角形、尖角或其他几何装饰性Sprite2D。

标签: 在血量条上方或内部，用一个Label显示Boss的名称（例如，“艾拉 - 完美形态 mk.II”或“宇宙几何元”）。

动态效果: 当Boss受到伤害时，血量条的填充部分有明显的减少动画（例如，先快速减少到目标值，然后旧的填充部分有一个短暂的“残影”效果再消失，或者填充条本身有轻微的震动和白色闪烁）。Boss进入新阶段时，血量条可能会改变颜色或增加新的分段标记。

8.5. 结束/胜利界面 (Game Over / Victory Screen):

结束画面 (Game Over - 单人/多人全灭):

触发: 玩家生命值降至0（单人）或所有玩家均阵亡（多人）。游戏暂停。

内容: 屏幕中央用大型几何风格文字（由多个Polygon2D拼接或特殊字体）显示 "GAME OVER"。下方用Label显示：

"FINAL SCORE: XXXXX"

"MAX DISTANCE: YYY M" (如果是肉鸽模式)

"CURRENCY GAINED: ZZZ" (本局获得的货币数量)

按钮:

"重试 (Retry)" 按钮：绿色长方形，内部有一个逆时针旋转90度的白色实心三角形作为“播放/重开”图标。

"返回菜单 (Back to Menu)" 按钮：红色方形，内部用白色线条组成一个 'X' 形几何图案。

胜利界面 (Victory Screen - 故事模式通关/肉鸽模式达成特定目标):

触发: 完成故事模式的最终Boss并达成某个结局，或在肉鸽模式中完成预设的胜利条件（例如，击败最终隐藏Boss或达到极远距离）。

内容: 屏幕中央显示 "VICTORY ACHIEVED!" 或对应结局标题的几何艺术字。下方展示最终分数、距离、获得货币，以及可能的通关时间、解锁成就等统计信息。故事模式结局会播放相应的结局影像。

按钮:

"继续 (Continue - 如果有下一周目或返回地图选择)"

"返回菜单 (Back to Menu)"

"查看制作人员 (View Credits - 故事模式最终结局后)"

8.6. 设置面板 (Settings Panel):

激活: 通常通过按下 ESC 键或点击开始菜单的设置按钮进入。游戏暂停。一个半透明的黑色ColorRect覆盖在当前屏幕之上作为背景遮罩。

内容: 使用VBoxContainer和HBoxContainer进行布局。

音量控制:

背景音乐 (Music Volume): 一个Label "MUSIC"，旁边一个HSlider用于调节音量，再旁边一个Label显示百分比数值 (0-100%)。

音效 (Sound Effects Volume): 同上结构，用于音效音量。

显示设置 (可选):

屏幕震动开关 (Screen Shake): 一个Label "Screen Shake"，旁边一个CheckBox或开关按钮。

显示伤害数值开关 (Show Damage Numbers): 同上。

控制设置 (可选):

按键绑定查看/修改界面入口按钮。

鼠标灵敏度调节滑块（如果游戏中有需要鼠标精确指向的非射击操作）。

关闭按钮: 在面板的右上角提供一个 'X' 形几何图标按钮用于关闭设置面板并应用更改（或提供单独的“应用”和“关闭”按钮）。

8.7. 游戏前大厅/角色准备界面 (Pre-Game Lobby / Character Setup Screen - 多人及单人故事/肉鸽模式前):

背景: 一个2D场景，表现为一个星际太空港的几何风格发射平台区域。背景是深邃的星空。有多个（例如5个）并排的、带有几何发光线条的玩家飞船停机坪Sprite2D或TextureRect。

玩家显示:

每个已加入的玩家（或单人模式的玩家）在对应的停机坪上显示其当前配置的飞船预览模型（一个根据玩家改装数据动态生成的Node2D层级，包含核心和已装备的改装件视觉）。房主（或单人玩家）通常在最中央或最显眼的停机坪。

旁边显示玩家名称Label。

AI友军显示: 如果玩家拥有已购买并配置出战的AI友军，这些AI友军的小型预览模型会以较小的尺寸显示在其主人飞船停机坪的后方或侧边的小型停泊位上。

自定义选项 (UI面板，每个玩家独立操作自己的):

颜色选择: 提供一个颜色选择器（例如，一组预设的几何色块按钮，或RGB滑块）允许每个玩家选择自己飞船的主体颜色。AI友军的颜色会自动匹配其主人的颜色。飞船预览实时更新颜色。

(后期) 立绘选择: 如果选择的角色有立绘，此处显示一个小的立绘预览，并允许在已解锁的立绘中选择。

(后期) 语音包选择: 同上，选择语音包。

飞船改装入口: 一个“改装飞船 (Modify Ship)”按钮，点击后打开2.7所述的改装界面。

准备状态: 每个玩家栏旁边有一个“准备 (Ready)”复选框或按钮。当所有玩家都标记为准备就绪后，房主（或单人玩家）的“开始游戏 (Start Game)”按钮才会激活。

联机信息 (多人模式下显示):

房间名称/ID Label。

创建房间选项 (如果当前是主机): 设置房间名LineEdit和密码LineEdit（可选）。

加入房间选项 (如果当前是客户端): 输入房间ID/密码的LineEdit，或一个简单的服务器浏览器列表（如果实现了Steam的房间查找功能）。

聊天框 (可选): 一个简单的文本输入和显示区域供大厅内玩家交流。

8.8. AI友军指令轮盘 (AI Companion Command Wheel):

激活: 游戏中按下 Q 键。游戏时间减慢（例如Engine.time_scale = 0.1）或暂停。

表现: 屏幕中央出现一个半透明的圆形背景，上面平均分布多个指令图标按钮。鼠标光标可见。

选项 (每个选项是一个带有几何图标和简短文字Label的按钮):

自由攻击 (Free Attack): 图标为一个准星或发散的箭头。

跟随防御 (Defend Position / Follow & Guard): 图标为一个盾牌或一个指向玩家的小箭头。

撤退待命 (Retreat & Standby): 图标为一个指向屏幕外的箭头或一个沙漏。

(其他可能的指令，如集中火力 - 图标为多个箭头指向一点)

选择: 鼠标移动到期望的指令图标上，图标会高亮。此时松开Q键或点击鼠标左键，即可下达该指令。指令下达后，轮盘消失，游戏时间恢复正常。

视觉反馈: 下达指令后，AI友军的头顶或身上可能会短暂出现一个与指令对应的几何小图标，然后其行为发生明显变化。玩家角色可能有对应语音 (后期)。

8.9. 剧情对话框与立绘系统 (Story Dialogue & Portrait System):

对话框设计: 屏幕底部或顶部出现一个横跨屏幕宽度（或占据一部分）的半透明矩形背景板（NinePatchRect或ColorRect）。边框设计为简洁的几何线条。字体清晰易读，颜色与背景板对比鲜明。支持显示发言者名称（一个位于对话框一角的Label）。

立绘显示: 如果发言角色有立绘，其2D立绘TextureRect会显示在对话框的左侧或右侧（根据角色在场景中的相对位置或预设）。立绘可以有多种表情差分（通过切换TextureRect的texture属性）。对于AI或非人形角色（如“调律者”的化身，“卡俄斯”），立绘可以是其核心几何形态的动态光影效果（例如，一个预渲染的动画精灵序列，或通过ShaderMaterial实现的动态纹理）。

文本显示: 对话文本逐字或逐句显示在对话框内（通过Timer和脚本控制Label的text属性）。支持滚动长文本。

特殊对话效果: 对于“调律者”或“卡俄斯”的“非标准语言”，对话框中的文字可能以特殊字体、颜色或动态效果（如文字轻微闪烁、扭曲、或在文字间随机插入几何小符号）显示，配合独特的合成音效或低语。

8.10. 数据日志/情报查阅界面 (Datalogs / Intel Viewer):

激活: 在基地场景中与特定终端Area2D互动（例如，走近按E键），或在游戏暂停菜单中选择“资料库 (Database)”选项。

表现: 一个全屏或占据大部分屏幕的UI界面。左侧可能是一个可滚动的列表ItemList或Tree，显示已收集的情报条目（例如，按角色、敌人、地点、技术等分类）。右侧是一个较大的文本显示区域（RichTextLabel或TextEdit设为只读），用于显示选中条目的详细内容。文本可滚动，可内嵌小型插图Image（例如，敌人的几何结构图、角色的概念草图）或特殊的几何符号字体。背景可以是科技感的几何网格图案。

9. 视觉特效 (Visual Effects - VFX)

9.1. 滚动星空背景 (Scrolling Starfield):

实现: 使用 Godot 的 ParallaxBackground 节点作为根，内含多个 ParallaxLayer 节点。

层次: 创建至少3-4个ParallaxLayer。

远层: 包含大量非常小的（例如1x1或2x2像素）、颜色为暗淡白色/灰色的点状Sprite2D（代表遥远恒星）。设置其motion_scale为一个非常小的值（例如 Vector2(0.1, 0.1)），使其移动速度极慢。

中层: 包含较少、稍大（例如3x3像素）、稍亮的点状或小型几何形状（如小三角、小方块）Sprite2D。设置其motion_scale为中等值（例如 Vector2(0.3, 0.3)）。

近层: 包含少量、更大（例如5x5或更大）、更亮的几何形状粒子或星云状Sprite2D（可以使用带有透明通道的模糊纹理）。设置其motion_scale为较大值（例如 Vector2(0.7, 0.7)或Vector2(1, 1)），使其移动速度最快，产生明显的视差效果。

方向: ParallaxBackground的scroll_offset的Y值随时间持续增加（例如 scroll_offset.y += scroll_speed * delta），模拟玩家向上飞行的感觉。scroll_speed可以根据游戏阶段或玩家速度进行微调。

9.2. 爆炸效果 (Explosion Effects):

实现: 为不同类型的爆炸（普通敌人、精英敌人、Boss、玩家死亡等）创建独立的PackedScene，根节点通常是Node2D。主要使用CPUParticles2D或GPUParticles2D节点。

参数:

发射形状 (Emission Shape): 通常设为点发射 (Point) 或小范围球体发射 (Sphere)，半径根据爆炸规模设定。

粒子数量 (Amount): 根据爆炸规模设定 (普通敌人例如20-50个，Boss可以数百上千个，但要注意性能)。

生命周期 (Lifetime): 短暂，例如0.5 - 1.5秒。

一次性发射 (One Shot): 设为true，粒子发射完毕后节点可自动销毁（如果设置了emitting = false后检查粒子是否都消失）。

运动 (Direction, Spread, Initial Velocity, Gravity, Damping):

方向 (Direction): 通常设为Vector3(0, 0, 0)或不设置，让粒子向各个方向炸开。

扩散 (Spread): 设为180度（2D平面上是360度扩散）。

初始速度 (Initial Velocity): 设置一个范围（例如，min=100, max=200像素/秒），使粒子具有初始的爆开速度。

重力 (Gravity): 通常设为Vector3(0, 0, 0)，除非需要特定下落效果。

阻尼 (Damping): 可以设置少量阻尼，使粒子运动逐渐减慢。

颜色 (Color Ramp): 粒子颜色应与爆炸源（敌人/Boss的主题色）一致。使用GradientTexture1D作为颜色渐变，例如从中心亮白色/黄色 -> 主题色 -> 暗色/完全透明，随粒子生命周期变化。

大小/缩放 (Scale Amount, Scale Curve): 粒子大小可以随生命周期变化，例如，开始大然后快速缩小，或开始小然后爆开变大再消失。使用CurveTexture控制缩放曲线。

附加效果: 可以在爆炸中心短时间显示一个明亮的闪光Sprite2D（通过Tween快速改变其scale和modulate.a）。对于大型爆炸，可以加入屏幕震动效果（通过脚本控制Camera2D的offset）。

9.3. 子弹碰撞 (Bullet Impact):

实现: 当子弹的Area2D检测到与目标（敌人或玩家护盾）的碰撞时触发。

效果 (创建一个小的“命中特效”场景并实例化在碰撞点):

视觉: 在碰撞点生成一个快速扩散并消失的半透明圆形波纹。可以用一个圆形Sprite2D，通过Tween在0.1-0.2秒内将其scale从0放大到一定尺寸，同时将其modulate.a从1渐变到0。颜色可以是被击中目标的颜色或通用的亮白色/黄色。

粒子: 同时迸发少量（例如5-10个）对应颜色的微小粒子（CPUParticles2D，生命周期极短，初始速度向外扩散）。

音效: 播放短促的、清脆的命中音效。

9.4. 护盾视觉效果 (Shield Visuals):

实现 (已在2.5.中部分描述):

环形线条: 使用Line2D绘制圆形（通过脚本生成点列），或者使用预渲染的、带有几何细节的环形Sprite2D纹理。

旋转动画: 使用AnimationPlayer或在_process函数中修改护盾节点的rotation_degrees属性，使内外环（如果有多层）以相反方向缓慢旋转（例如内环顺时针30度/秒，外环逆时针30度/秒）。

闪烁效果: 使用Tween或ShaderMaterial（如果需要更平滑的效果）控制护盾节点的modulate.a（透明度）或modulate.rgb（亮度，例如在主色和稍亮的颜色间过渡），实现平滑的、周期性的闪烁。

9.5. 敌人发光 (Enemy Glow):

实现 (选择一种或组合使用，注意性能):

PointLight2D: 附加在敌人节点上作为子节点。设置其texture为一个模糊的圆形软边Texture（例如，用图像软件制作一个中心亮边缘羽化的白点）。设置其color为敌人的主题色，energy值（例如0.5-1.5，控制亮度），range（光照范围）。对于大量同屏敌人，过多光源可能会影响性能，此时应谨慎使用或寻找替代方案。

附加发光Sprite2D: 在敌人主Sprite2D下方（Z序更低）添加一个相同的或略微放大/模糊化的Sprite2D，其颜色设为更亮的主题色，并将其混合模式（Material的Blend Mode）设为Add。这样它会与下方的图像进行叠加混合，产生辉光效果。这种方法性能开销较低。

ShaderMaterial: 为敌人的主Sprite2D应用一个自定义的2D Shader。Shader中可以实现更复杂或性能优化的辉光效果（例如，通过对精灵纹理进行多次模糊和叠加，或使用边缘检测算法来产生轮廓光）。这需要一定的Shader编程知识。对于极简几何风格，一个简单的外发光Shader可能就足够了。

9.6. 道具闪烁/拾取特效 (Item Shine / Pickup FX):

掉落物闪烁: 道具掉落在地上时（一个独立的Area2D场景实例）：

上下浮动: 使用Tween节点周期性地改变其position.y，实现轻微的上下浮动效果。

亮度/大小脉冲: 使用Tween节点周期性地改变其Sprite2D子节点的modulate（例如，整体亮度或透明度在0.8到1.0之间变化）或scale（例如，在1.0到1.1之间微小缩放），吸引玩家注意。

拾取特效: 玩家的Area2D接触到道具的Area2D时：

道具飞向玩家: 道具的Sprite2D（或整个道具节点）通过Tween在短时间内（例如0.2-0.3秒）快速移动到玩家当前位置，同时其scale缩小至0，然后queue_free()。

闪光与粒子: 在道具被拾取的位置（或玩家身上），实例化一个“拾取特效”场景：包含一个快速放大并消失的、与道具主题色相关的亮色圆形闪光Sprite2D，以及少量对应颜色的粒子（CPUParticles2D）向上飘散。

音效: 伴随一个清脆、积极的确认音效。

9.7. Boss 出现特效 (Boss Entrance FX):

屏幕震动: 使用Camera2D的offset属性。在Boss出现前的几秒内，通过脚本给offset赋予随机的小幅位移值，并随时间衰减，模拟短暂的屏幕震动。或者使用一个ShakeCamera2D插件（如果Godot社区有）。

警告闪烁: 在屏幕边缘（例如顶部或四周）使用CanvasLayer（确保在最顶层渲染）和ColorRect节点。让ColorRect的颜色设为红色，并通过Tween使其modulate.a在0和0.5（半透明）之间快速闪烁几次。

入场动画: Boss节点（一个PackedScene）从屏幕外（例如顶部y < 0的位置）通过Tween或AnimationPlayer控制其position平滑移动到战场内的指定初始位置。移动过程中可能伴随独特的粒子轨迹（例如，Boss身后拖着几何能量尾焰）或能量聚集效果（例如，Boss在目标位置由多个几何光束汇聚而成）。

9.8. 开场与过场动画特效 (Opening & Cutscene VFX - 主要用于故事模式):

开场动画: 可以是一系列预渲染的2D动画序列（如果资源允许，可以用外部软件制作然后导入为SpriteFrames在AnimatedSprite2D中播放），或者是由多个高质量的2D静态插画（TextureRect或Sprite2D）配合Tween动画（平移、缩放、淡入淡出）和旁白/BGM来组成。内容例如：玩家飞船从星际空间站（由多个几何形状组合成的复杂结构）的几何发射坪起飞，引擎喷射出几何粒子尾焰，加速进入由ParallaxBackground构成的深邃星空。

故事模式过场: 根据剧情需要，在关卡之间或关键剧情点，使用类似开场动画的技术。例如，用几张插画配合文字叙述来展现主角与NPC的对话场景、一个被“几何化”的星球的远景、或者一个重要的历史事件回溯。动态元素可以通过Tween对插画中的某些部件（例如，飞船、爆炸光效）进行简单的位移、旋转或缩放来实现。

9.9. 拓扑几何特效 (Topological Geometry VFX - 主要用于故事模式后期和特定Boss):

空间扭曲/折叠 (Screen Distortion/Folding):

实现: 主要通过全屏ShaderMaterial（应用到一个覆盖整个屏幕的ColorRect或TextureRect上）来实现。Shader可以获取屏幕纹理（TEXTURE, SCREEN_TEXTURE），然后对其UV坐标进行扭曲。例如：

水波纹/热浪效果: 对UV坐标添加一个随时间变化的正弦/余弦偏移。

旋涡效果: 根据像素到屏幕中心的距离和角度，对UV坐标进行旋转偏移。

折叠/镜像: 将屏幕分割成几块，对某些块的UV进行翻转或重复。

应用场景: Boss释放特定技能时，玩家进入特殊区域时，或剧情高潮。

莫比乌斯/克莱イン效果 (Mobius/Klein-like Visuals):

能量流动: Line2D的路径点可以被脚本动态修改，使其形成在2D平面上看起来像莫比乌斯带的连续路径，能量粒子（CPUParticles2D附着在PathFollow2D上）沿着这条路径移动。

护盾形态: (见2.5.特殊护盾) 可以是一个预设的、看起来像克莱因瓶二维展开图的复杂Sprite2D护盾，或者通过多个Line2D动态勾勒出其轮廓。

敌人路径: (见3.7.“莫比乌斯追踪者”) 通过在屏幕边缘设置“传送门”（一对Area2D，一个检测离开，一个在对应位置重新生成敌人），让敌人看起来像是从屏幕的一边消失，然后从另一边的“内表面”出现。

几何解构/重构 (Geometric Deconstruction/Reconstruction):

实现: 使用AnimationPlayer或脚本。

解构: 将一个复杂的敌人或Boss（由多个独立的Sprite2D或Polygon2D子节点构成）的各个部件在短时间内通过Tween或动画轨道赋予随机的旋转和向外飞散的位移，同时缩小其scale并增加透明度直至消失。可以配合粒子效果。

重构: 相反的过程。多个基础几何单元Sprite2D从屏幕外或特定点飞入，并在目标位置通过Tween或动画轨道精确地组合、旋转、缩放，最终形成一个新的、更复杂的几何形态。

应用场景: 敌人死亡、Boss阶段转换、环境中某些机关的激活/关闭、召唤单位的出现。

“调律场域”视觉 (Harmonization Field Visuals):

实现: 在战场背景之上（但在主要游戏对象之下）叠加一个或多个CanvasLayer，上面放置一个巨大的、覆盖全屏的TextureRect或Control节点。为其应用一个ShaderMaterial，该Shader在屏幕上绘制半透明的、流动的、由无数细小几何符号（可以用一个包含多种小几何符号的TextureAtlas作为输入）组成的能量网格或粒子流。这些符号可以缓慢地旋转、脉动或沿着特定路径（例如，从屏幕边缘向中心汇聚）移动。

效果: 营造一种被“调律者”的意志所笼罩的氛围。当玩家使用“悖论共鸣”等技能时，这个场域的视觉效果可能会暂时出现“干扰”、“破裂”或颜色变化。

9.10. 地图扩展过渡特效 (Map Expansion Transition VFX):

当游戏区域从一个级别扩展到下一个级别时，应有一个平滑且符合几何主题的视觉过渡效果，以避免突兀感。例如：

空间涟漪/延展: 屏幕边缘出现短暂的、向外扩散的几何涟漪或光波特效，同时背景星空发生一种“拉伸”或“展开”的动画。

维度裂隙: 屏幕边缘短暂出现类似维度裂隙的几何线条，然后“裂开”并展现出更广阔的空间。

摄像机微调配合: 如果使用Camera2D.zoom辅助实现，缩放过程应平滑（通过Tween），并可能伴随轻微的镜头后退模糊效果。

此特效由触发地图扩展的逻辑（例如，在主游戏控制器中）调用执行。

10. 音频系统 (Audio System)

10.1. 背景音乐 (Background Music - BGM):

风格: 主要采用节奏明快、动感强烈的电子音乐 (Electronic Music)，如Synthwave, Chiptune, Techno, Trance等，以契合几何美术风格和射击游戏的紧张感。

故事模式BGM变化:

常规关卡: 使用循环的、带有探索感或战斗节奏的电子乐。

剧情对话/基地场景: 使用更平缓、带有悬疑或情感色彩的氛围音乐 (Ambient Electronic)。

Boss战: 每个主要Boss（尤其是艾拉/里奥、“调律者”化身）应有其专属的、更具史诗感、压迫感或与其角色主题相关的BGM。例如，艾拉/里奥的Boss战音乐可能在精准的电子节拍中融入一丝悲伤的旋律线；“调律者”的音乐则可能宏大、冰冷，充满了数学般的和谐与非人的威严。

特定剧情事件: 如“压倒性攻势”波次期间，BGM的节拍会显著加快，混入更激昂或警报般的音轨。

肉鸽模式BGM: 可以使用一个较长的主循环BGM，或者根据游戏进程（例如，每通过几个区域或Boss）切换到节奏更快、更复杂的BGM变奏。

实现: 使用AudioStreamPlayer节点播放音乐。通过脚本控制其stream属性来切换不同的AudioStream资源。在切换BGM时，应使用淡入淡出效果（例如，通过Tween节点平滑地改变旧BGM的volume_db到-80dB，同时新BGM从-80dB平滑到正常音量）以避免突兀。

10.2. 音效系统 (Sound Effects - SFX):

分类管理: 使用Godot的音频总线 (Audio Buses) 将音效分类（例如，Player_SFX, Enemy_SFX, Weapon_SFX, UI_SFX, Environment_SFX, Voice_SFX），方便在设置中单独控制各类音效的音量。在项目设置的“音频总线”标签页中创建这些总线，并在AudioStreamPlayer节点的Bus属性中选择对应的总线。

关键音效列表 (示例，每个音效都是一个.wav或.ogg格式的短音频文件):

玩家相关:

玩家射击: 短促、有力的脉冲声、激光发射声或几何能量冲击声。根据武器改装可能有所变化。

玩家子弹命中: 清脆的、带有数字感的击中声或能量碎裂声。

玩家受伤 (生命值受损): 低沉、带有冲击感的撞击声或短暂的系统故障声（如电流干扰）。

玩家护盾格挡: 能量防护罩被敲击的清脆回响，或力场偏转物体的声音。

玩家护盾破碎: 玻璃破碎或能量护盾过载消散的声音。

玩家死亡: 较长的、表示失败的音效，结合飞船爆炸的轰鸣和驾驶舱弹出的呼啸声。

闪避: 短促的相位转移声或空气撕裂声，伴随拖尾特效。

核心拓扑能力激活 (“维度滑行”, “悖论共鸣”): 独特的、带有科幻感的能量充盈声、空间扭曲声或逻辑崩坏的刺耳音效。

敌人相关 (通用及“调律者”造物):

敌人生成/出现: 短暂的几何传送声、能量汇聚声或空间裂隙打开的音效。

敌人射击: 根据敌人类型和武器区分。例如，“几何先驱”的碎晶可能是尖锐的咻咻声；“几何执行者”的能量脉冲可能是沉闷的噗噗声；“五边塑形者”的棱柱射线可能是持续的嗡鸣声。

敌人子弹命中玩家: 同玩家受伤音效。

敌人被击杀 (普通): 不同敌人类型对应不同大小和质感的几何解构声、能量消散声或小型爆炸声。

精英/Boss受伤: 更沉重、更有质感的打击声或能量护盾受损的波动声。

Boss被击败: 持续的、多层次的巨大爆炸声、结构崩塌声和最终的能量核心寂灭的完成音效。

“调律者”造物的特殊音效: 它们的移动、攻击和死亡音效应更强调“非有机”、“数学精确”和“能量化”的感觉，例如使用合成器产生的纯粹音调、扫频音、或由几何序列转换成的声音模式。

道具与改装:

道具掉落: 轻柔的、带有提示性的几何晶体生成声或闪光声。

道具拾取: 明亮、积极的确认音效 (例如叮、能量吸收声、或短暂的悦耳旋律片段)。

激活道具 (T/A/S): 播放一个表示能力增强的特殊音效，例如短暂的能量过载声或系统升级提示音。

改装界面打开/关闭: 特殊的、带有科技感的界面切换音效。

改装件放置/拾起: 放置时是确认的“咔哒”声或能量连接声，拾起时是取消的轻微“嗖”声。

改装件升级成功: 令人愉悦的升级完成提示音。

UI与环境:

UI按钮点击: 短促、干净的、带有数字感的点击声或几何按键声。

UI界面切换/弹出: 平滑的滑动声或能量激活声。

获得货币: 清脆的几何金币碰撞声或能量点数增加的“哔哔”声。

难度提升提示: 上升的、带有警示意味的合成器音调。

剧情对话框文字出现: 打字机效果音（如果逐字出现）或轻微的数字显示音。

Boss入场警告: 急促、响亮的警报声、心跳声或低沉的宇宙威胁轰鸣声。

空间扭曲/拓扑陷阱: 环境音效变得诡异、失真，或出现空间撕裂的特效音。

(后期) 语音相关:

角色受击、释放技能、拾取道具等事件的简短语音片段。

AI友军响应指令的语音确认。

剧情对话中的角色配音。

实现: 对每个需要音效的事件，在对应节点的脚本中使用AudioStreamPlayer（全局音效，如UI点击）或AudioStreamPlayer2D（需要位置感的音效，如敌人爆炸）来播放一次性音效（获取节点引用后调用其play()方法）。管理好音效资源（AudioStream对象），可以通过预加载或在需要时加载来优化。对于频繁触发的音效（如玩家射击），要注意不要过于密集刺耳，可以考虑随机播放几种略有差异的音效，或设置一个最小播放间隔。

10.3. 音量控制 (Volume Control):

UI: 在设置面板中（见8.6），为背景音乐和各类音效（通过音频总线区分）提供独立的音量调节HSlider。滑块的值通常是0-1（或0-100，需转换）。

实现: 滑块的value_changed信号连接到一个脚本函数。该函数获取滑块的值（例如0-1线性值），然后使用AudioServer.set_bus_volume_db(bus_index, linear_to_db(linear_value))来设置对应音频总线的音量。bus_index是音频总线的索引号（可以在项目设置中查看或通过名称获取AudioServer.get_bus_index("BusName")）。Godot的音量单位是分贝(dB)，0dB是原始音量，负值减小音量，-80dB基本听不见。linear_to_db()是将0-1的线性值转换为dB值的辅助函数。

静音: 可以为每个总线提供单独的静音CheckBox或按钮。选中时，调用AudioServer.set_bus_mute(bus_index, true)，取消选中则设为false。或者，直接将对应总线的音量设为极低值 (-80 dB)。

11. 游戏机制 (Game Mechanics) - 总结与细化

11.1. 分数系统 (Scoring System):

来源: 击杀不同类型的敌人获得不同的基础分数（已在敌人系统定义，例如三角斥候10分，五角堡垒50分）。

连击/Combo (可选，可增加得分策略性):

机制: 玩家在短时间内（例如2-3秒内）连续击杀敌人，可以累积连击数。连击数越高，后续击杀敌人获得的分数会有乘数加成（例如，连击10-19次，分数x1.2；连击20-29次，分数x1.5，以此类推，可设上限）。

计时器: 如果在规定时间内未能击杀下一个敌人，连击数断开并重置。

UI显示: 在屏幕上（例如，靠近分数显示的地方）显示当前的连击数和分数乘数。连击数增加或断开时有视觉和音效提示。

显示: 累计分数实时显示在游戏界面右上角（见8.3）。

11.2. 难度递增曲线 (Difficulty Curve - 主要影响肉鸽模式，故事模式难度由关卡设计控制):

核心驱动: 主要由游戏时间（影响敌人生成速率，见5.3）和Boss击杀次数（影响敌人生命值强化，见5.4）驱动。

综合影响: 随着游戏进行，玩家将面临更快生成的、更“肉”的敌人，以及更频繁的特殊波次和更强的Boss（Boss自身也有血量递增，见4.通用Boss属性）。

调整与测试: 需要通过大量内部测试来调整难度曲线的参数（例如，时间因子中每多少秒提升一次难度，每次提升的百分比，敌人HP强化的百分比等），确保游戏既有持续的挑战性，又不会在早期过于劝退新手玩家，或在后期因为数值膨胀过快而变得无法进行。可以考虑设置某些参数的增长上限或在后期让增长速度放缓。

11.3. 波次系统 (Wave System): (详细设计见5. 敌人生成系统)

作用: 控制游戏的节奏、敌人的组合和出现方式，避免单调重复，制造紧张感和挑战高潮。

实现 (Wave Manager脚本):

故事模式: 波次通常是基于时间线或剧情事件精确编排的。例如，一个数组，每个元素包含（触发时间/条件，敌人类型，数量，生成位置/阵型，其他参数）。

肉鸽模式: 可以采用半随机系统。例如，预设多种不同强度和类型的“波次模板”（包含若干种敌人的组合和数量范围），然后根据当前的全局难度等级和游戏时间，从合适的模板池中随机抽取并略作调整（例如，敌人数量随难度微调）来生成波次。

11.4. 距离计数器 (Distance Counter - 主要用于肉鸽模式的进度衡量):

目的: 提供除了分数之外的另一个衡量游戏进度的直观指标，与玩家的生存时间直接相关。

机制: 游戏开始后，一个全局变量（例如 distance_traveled）每秒稳定增加1单位 "M"（米，或其他合适的单位）。

显示: 实时显示在游戏界面的指定位置（见8.3），并在游戏结束时（Game Over界面）显示最终的前进距离。

11.5. 难度等级指示器 (Difficulty Level Indicator - 主要用于肉鸽模式的反馈):

目的: 让玩家清晰地感知到游戏难度的实际变化，增加成就感或警示。

计算: 可以简单地基于时间等级 (例如 level_from_time = floor(total_game_seconds / 60)) 和已击败Boss数量 (level_from_bosses = number_of_bosses_defeated) 来计算一个综合难度等级（例如 current_difficulty_level = 1 + level_from_time + level_from_bosses * 2）。

显示: 每次综合难度等级提升时，在屏幕中央短暂显示（见8.3的UI描述）。

11.6. 游戏货币系统 (Game Currency System):

获取:

击败敌人: 根据敌人类型和强度（已在敌人系统3.中定义），固定或有几率掉落不同数量的游戏货币（一个可拾取的货币图标场景）。

完成波次/关卡: 在故事模式中完成一个主要关卡或重要剧情节点，或在肉鸽模式中成功通过一个充满挑战的特殊波次或区域，会一次性奖励一定数量的货币。

达成成就/任务 (可选): 如果设计了成就系统或支线任务系统，完成特定目标（例如，“首次无伤通过某个Boss”，“累计击杀1000个三角斥候”）可以给予一次性的货币奖励。

通关结局: 故事模式或肉鸽模式的不同结局（尤其是首次达成）会奖励大量游戏货币。

用途:

永久性玩家升级 (见2.9): 提升基础生命、护盾、速度、射速、货币获取效率等。

购买改装件 (见7.1): 在商店或军械库购买通用的或已解锁的改装件。

升级改装件 (见7.2): 提升已拥有改装件的等级，增强其效果。

购买和升级AI友军 (见13): 如果启用了AI友军系统，需要货币购买友军槽位、友军单位本身以及升级其属性。

解锁特殊技能 (若后续添加): 如果设计了独立于改装件的、玩家可主动释放的特殊技能树，可能需要货币解锁和升级这些技能。

解锁外观自定义项（如玩家飞船颜色、后期可能的立绘、语音包 - 部分可能标记为DLC或高价货币解锁）。

平衡: 货币的获取速度（敌人掉落量、任务奖励额度）和消耗量（升级价格、购买价格）需要仔细平衡。目标是让玩家在游戏过程中始终有明确的追求目标，通过努力可以获得稳定的成长反馈，但又不至于让所有内容过快解锁导致失去长期动力，或因价格过高而产生挫败感。

11.7. 剧情选择与影响 (Story Choices & Consequences - 故事模式简化版):

机制: 在故事模式的关键剧情节点（通常是与重要NPC对话结束时，或在完成某个任务后的总结阶段），通过对话框向玩家呈现2-3个明确的行动或对话回应选择（作为按钮显示）。

影响 (实现上以不产生过多剧情分支为目标):

对话与关系变化: 玩家的选择会直接改变后续NPC对主角的几句对话内容或态度（例如，艾拉/里奥对主角的选择表示失望、愤怒或理解）。可以在NPC对象或一个全局剧情状态管理器中用一个简单的变量（例如，elara_relationship_score）记录这种倾向，达到某个阈值可能在后续剧情中触发不同的对话分支或NPC的细微行为差异（例如，某个Boss战中，如果关系好，Boss可能会在某个阶段犹豫或给出提示）。

任务目标微调或后续小任务解锁: 某些选择可能会略微改变下一个任务的次要目标，或者解锁一个可选的、小型的支线任务（例如，如果选择帮助某个NPC，后续会接到一个关于该NPC的额外小任务，完成后获得少量额外奖励或情报）。

结局变量累积: 每个关键选择都可能为一个或多个“结局变量”（隐藏的全局计数器）加分或减分。最终结局根据这些变量的组合来判定。例如，变量A代表“坚持自由意志”，变量B代表“寻求与AI和解”，变量C代表“对秩序的认同”。不同的选择会使这些变量朝不同方向发展。

情报或道具获取: 某些选择可能会让玩家立即获得一个额外的情报数据（在资料库中解锁新条目）或一个一次性的小型增益道具/少量货币。

展现: 主要通过对话文本的变化、任务日志的更新、以及可能的少量新增对话或UI提示来体现选择的影响。避免创建需要完全不同关卡设计或大量独特资源的大型剧情分支。重点在于让玩家感觉到自己的选择“有意义”并且对故事的走向“产生了影响”，即使这种影响更多是叙事层面和最终结局判定上的。

12. 多人游戏模式 (Multiplayer Mode)

12.1. 联机方式:

类型: 基于Steam P2P (Peer-to-Peer) 网络。需要集成Godot的Steamworks SDK（例如GodotSteam插件）。

服务器: 由一位玩家作为主机（Host/Server）创建游戏房间，其他玩家作为客户端（Client）加入。无需专用服务器。主机负责主要的游戏逻辑运算（如敌人生成、Boss行为、碰撞判定等），并将游戏状态同步给客户端。

玩家数量: 最多支持5名玩家同时在线游戏（可根据测试情况调整，玩家过多可能增加同步难度和性能开销）。

加入方式:

主机创建房间: 在游戏前大厅（见8.7），主机可以选择“创建多人游戏”，然后可以设置房间名称（可选，默认为主机Steam昵称的房间）和密码（可选，用于私人房间）。房间创建成功后，主机会获得一个房间ID（如果Steam P2P支持）或通过Steam好友系统邀请。

其他玩家加入:

通过Steam好友邀请: 主机可以通过Steam好友列表直接邀请好友加入游戏。

浏览公开房间列表 (如果实现): 如果使用Steam的匹配服务，可以显示一个公开房间列表供玩家选择加入（可能需要更复杂的SDK集成）。

输入房间ID/密码: 提供输入框，让玩家通过主机分享的房间ID（如果P2P直连可行）或密码加入特定房间。

12.2. 游戏同步 (使用Godot的内置高级多人游戏API - MultiplayerSpawner, MultiplayerSynchronizer):

需要同步的关键信息:

玩家状态: 每个玩家的位置、旋转、生命值、护盾、当前激活的道具、改装配置、得分、货币等。使用MultiplayerSynchronizer同步玩家节点的关键属性。

敌人状态: 敌人的生成、位置、旋转、生命值、当前行为状态（例如，是否被“逻辑混乱”）。敌人场景应设计为可由主机生成（使用MultiplayerSpawner），其关键属性也通过MultiplayerSynchronizer同步。

子弹: 玩家和敌人发射的子弹的生成、位置、方向。由于子弹数量多且生命周期短，可以考虑优化同步策略（例如，仅同步发射事件和初始参数，客户端进行本地预测，或仅关键子弹进行完全同步）。

道具: 道具的掉落位置、类型。拾取事件由主机判定并同步结果。

Boss行为: Boss的位置、旋转、生命值、当前攻击模式和阶段。Boss的主要逻辑在主机上运行。

游戏全局状态: 当前波次、难度等级、游戏时间等。

同步方式:

主机权威 (Host Authority): 主机是游戏状态的最终决定者。客户端将输入发送给主机，主机处理后将结果同步回所有客户端。

RPC (Remote Procedure Calls): 使用rpc("function_name", arguments)和@rpc注解来实现客户端与主机之间，以及主机与所有客户端之间的函数调用，用于同步事件和非频繁变化的状态。

网络延迟与平滑: 需要考虑网络延迟对游戏体验的影响。对于玩家和敌人的移动，客户端可以进行插值 (Interpolation) 或外推 (Extrapolation) 来平滑显示。

12.3. 玩家互动:

合作: 所有玩家在同一战场共同对抗敌人和Boss。

救援与复活: (已在2.8.中详细描述) 玩家死亡后驾驶舱弹出，可被其他存活玩家拾取。携带驾驶舱的玩家拾取“+”道具时可复活队友。

资源分配:

道具拾取: 临时强化道具 (T, A, S, +) 通常是谁先碰到归谁（客户端检测到碰撞后向主机发送拾取请求，主机确认并同步）。

货币掉落: 敌人掉落的货币，可以设计为小队共享（即一个玩家拾取后，所有队员都获得等量货币），或者仅拾取者获得（但掉落数量可能会根据玩家人数略微增加）。共享机制更鼓励合作。

改装件掉落: 改装件的掉落判定由主机进行。掉落后，可能所有玩家都会在自己的屏幕上看到该改装件的“幻影”，但只有第一个接触并拾取的玩家能真正获得它（或者设计为所有玩家都能获得一个复制品，或随机分配给某个玩家，需要明确规则）。

12.4. AI友军归属 (如果多人模式中允许携带AI友军):

AI友军始终属于购买并配置它的玩家。其视觉颜色等应与主人一致。

同步: AI友军的生成、状态和行为也需要由其主人的客户端（或主机，如果AI逻辑统一由主机处理）进行同步。

主人阵亡: 若其主人在多人游戏中阵亡（驾驶舱弹出但未被拾取），AI友军的行为模式：

选项1 (跟随房主/最近盟友): AI友军会暂时停止主动攻击，并缓慢移动到当前游戏的房主或距离最近的另一个存活友方玩家附近，进行防御性跟随，直到其主人被复活。

选项2 (原地待命/自毁): AI友军在主人阵亡点附近原地盘旋待命，或在一段时间后自动解构消失。

若主人彻底退出游戏，其AI友军也应立即消失。

12.5. 难度调整 (多人模式下):

机制: 当有多个玩家参与游戏时，为了保持挑战性，游戏难度应动态调整。

调整方面:

敌人生命值: 敌人（包括Boss）的基础生命值可以乘以一个与玩家数量相关的系数（例如，hp_multiplier = 1 + 0.5 * (num_players - 1)）。

敌人数量: 波次中生成的敌人数量可以适量增加。

Boss攻击性: Boss的某些攻击模式可能会更频繁，或弹幕密度略微增加。

注意: 难度调整不宜过于剧烈，以免人数少时过于简单，人数多时又过于困难。需要测试和平衡。

13. AI友军系统 (AI Companion System - 可选，故事模式中可能不启用或仅特定NPC作为临时友军)

13.1. 获取与配置:

购买: 玩家可以使用游戏货币在主菜单的“军械库”或“商店”中购买AI友军的“槽位”（例如，初始1个槽位，最多可升级到2-3个）以及不同类型的AI友军单位本身（如果设计了多种类型的AI友军，例如攻击型、防御型、辅助型）。

配置: 购买后，玩家可以在游戏开始前的准备界面（见8.7）的专门区域，从已拥有的AI友军中选择要携带出战的单位，并将其装备到已解锁的槽位上。

数量限制: 玩家可同时携带出战的AI友军数量受限于已解锁的槽位数量。

13.2. 外观与属性:

外观: AI友军是小型的几何形状单位，例如一个缩小版的、与玩家飞船风格类似的几何飞行器，或者一种独特的、代表其功能的几何设计（例如，防御型友军可能是个小型盾牌形状的无人机）。其中心通常也有一个代表核心的白色圆形。其主颜色会根据其所属玩家选择的颜色自动匹配（通过脚本在实例化时设置其Polygon2D或Sprite2D的modulate或self_modulate）。

属性: 每个AI友军单位拥有独立的生命值、攻击力（如果能攻击）、射速（如果能攻击）、移动速度等属性。这些属性可以通过在军械库中消耗游戏货币对该类型的AI友军进行等级提升来增强。AI友军可以被敌方火力摧毁。

死亡与重生: AI友军被摧毁后，其图标在HUD上会变灰，并进入一个冷却计时（例如30-60秒）。冷却结束后，AI友军会在玩家附近自动重生（重新实例化）。或者，玩家可以通过消耗少量游戏货币或特定道具来立即重生已阵亡的AI友军。

13.3. 行为与指令:

基础行为 (默认指令 - 例如“自由攻击”): AI友军会自动寻找其视野范围内（一个附加的Area2D检测区域）最近的敌人，并向其移动（使用move_toward）和射击（使用一个独立的Timer控制射击频率）。其索敌逻辑和射击精度可以设计得比玩家略低，以避免喧宾夺主。

指令系统 (通过8.8的AI友军指令轮盘下达):

自由攻击 (Free Attack): 执行上述基础行为。

跟随防御 (Defend Position / Follow & Guard): AI友军会优先保持在玩家飞船的正前方或指定侧翼（例如，相对于玩家的固定偏移位置），并自动攻击正在威胁玩家的敌人或尝试用自身（如果设计了碰撞体积）格挡少量来袭子弹。

撤退待命 (Retreat & Standby): AI友军会快速移动到屏幕边缘外（例如，Y坐标变为负值或极大值）并暂时隐藏。此时其图标在HUD上可能显示为“待命中”。当玩家再次通过指令轮盘选择“进攻”或特定召唤键时，它们会从屏幕侧面或后方重新加入战斗。

(其他可能的指令，如“集中火力”：所有AI友军优先攻击玩家当前鼠标光标指向的敌人，或一个被玩家标记的敌人。)

AI逻辑: AI友军的行为可以通过一个简单的状态机（State Machine）来实现，根据当前指令和战场情况切换不同的行为状态（如索敌、攻击、跟随、待命）。

13.4. 视觉表现:

在游戏前大厅（见8.7），已配置的AI友军的小型预览模型会显示在其主人飞船停机坪的后方或旁边的小型停泊位上。

战斗中，AI友军除了自身外观，其上方或旁边可以有一个小型的、与玩家颜色一致的标识符（例如一个小箭头或光环），以清晰表明其归属。其生命状态可以通过一个微型血条或其核心光芒的亮度变化来指示。

14. 玩家立绘与语音 (Player Portraits & Voice Lines) - (标记为后期重点开发或DLC内容)

14.1. 立绘 (Portraits):

用途: 在特定UI界面（如故事模式的角色选择界面——如果允许选择不同飞行员；剧情对话框——见8.9；游戏结束后的统计界面——显示玩家所选角色的形象）显示玩家所选角色的半身像或头像。

风格: 应与游戏整体的几何简约美学有所协调，或者采用一种风格化的、线条硬朗的动漫/科幻插画风格。立绘本身不必是纯几何，可以是人类或其他种族的角色，但其服装、配饰或背景可以融入几何元素。

获取: 玩家初始可能拥有一套或几套默认的角色立绘（例如，一个男性飞行员，一个女性飞行员，一个神秘的几何种族成员等）。更多独特的立绘可通过完成故事模式的特定结局、达成高难度成就、在游戏内商店用大量货币购买，或作为付费DLC解锁。

自定义: 如果实现了多角色或自定义飞行员系统，玩家可以在游戏开始前或特定界面选择自己喜欢的、已解锁的立绘。

14.2. 语音 (Voice Lines):

触发事件 (为这些事件预留播放语音的接口，具体语音包可后续添加):

受到攻击 (生命值降低时): 播放简短的受创语音，例如：“呃啊！”、“被击中了！”、“可恶！”。

释放核心拓扑能力: 播放表示能力激活的呼喊或低语，例如：“维度滑行！”、“悖论爆发！”。

拾取强力道具 (T/A/S): 播放表示获得强化的简短语音，例如：“火力增强！”、“锁定目标！”、“范围压制！”。

下达AI友军指令: 播放与指令对应的简短命令语音，例如：“全体进攻！”、“掩护我！”、“暂时撤退！”。

击败普通Boss: 播放表示胜利或松一口气的语音，例如：“搞定！”、“威胁解除。”。

击败剧情重要Boss (如艾拉/里奥, “调律者”化身): 播放更具情感的、与剧情相关的特殊语音。

游戏开始 (关卡开始时): 播放表示准备就绪或进入战斗状态的语音，例如：“任务开始！”、“异构体，出击！”。

游戏结束 (Game Over): 播放表示失败或遗憾的语音。

故事模式中的关键剧情对话片段: 主要角色（主角、艾拉/里奥、卡俄斯等）在通过对话框进行剧情交互时，其台词可以有配音。

获取: 游戏本体可能包含一套基础的、通用的玩家语音（例如，只有效果音般的呼喊，或由一位配音演员录制的标准男女声）。更多特定角色的语音包（例如，由不同配音演员演绎的、具有鲜明个性特征的语音）、幽默风格的语音包、或外星语言的语音包（带字幕）等，可通过游戏内商店用货币购买，或作为付费DLC解锁。

自定义: 如果玩家可以选择不同的角色或语音包，可以在设置界面或角色准备界面进行切换。

15. 新游戏模式 (New Game Modes)

15.1. 故事模式 (Story Mode):

关卡设计:

(独特地形、敌人配置、特殊事件、Boss战 保持不变)

地图扩展与滚动方向应用: 故事模式的不同关卡可以预设不同的初始地图级别和滚动方向。例如：

早期关卡可能在“小型地图”并采用传统的向上滚动。

随着剧情发展，主角飞船获得重要改装升级后，后续关卡可以设定为“中型地图”或“大型地图”。

特定追逐关卡或逃脱关卡可以采用横向滚动（向左或向右），或甚至向下滚动，以营造不同的氛围和挑战。

某些Boss战的场地大小也可能与其对应的地图级别相关。

结构: 由一系列（例如10-15个）精心设计的线性关卡组成，关卡之间通过剧情过场（见9.8和8.9的展现方式）串联。

剧情驱动: 采用16. 游戏剧情大纲 (M.AI融合版 - “调律者”的几何福音与不谐之音) 中详述的剧情主线、核心角色、势力冲突和结局分支。

核心设定: 玩家扮演“逆流项目”的飞行员，驾驶“异构体原型”飞船，对抗远古AI“调律者”及其推行的“宇宙调律计划”，并在过程中面临人类内部的背叛、与觉醒AI的复杂关系，以及对“完美”与“人性”的深刻拷问。

关键角色/敌人: (详见16.2. 主要角色与势力，以及3.7.和4.4.中为故事模式设计的独特精英怪和Boss)。

剧情分支与选择: (详见11.7. 剧情选择与影响) 在关键剧情节点，玩家的选择会影响与NPC的关系、后续任务的细微变化，并最终累积决定达成哪种结局。

盟友系统: “卡俄斯”AI可能在特定条件下成为临时盟友。艾拉/里奥的命运也可能因玩家的选择而改变（例如，是被彻底击败，还是在某种程度上被“唤醒”或达成和解）。

情感基调: 剧情将包含紧张刺激的弹幕射击战斗、对未知宇宙奇观（如拓扑扭曲空间）的探索、主角与同伴（如果设计了固定NPC同伴）的成长与牺牲、对人类内部矛盾的揭露与反思，以及在绝望中寻找希望的史诗感。真结局（例如“不和谐的交响曲”）力求在保留悲剧色彩和现实复杂性的同时，给予玩家一种积极的、充满可能性的未来展望。

结局: (详见16.4. 结局细化与展现) 设计多个具有显著差异的结局（例如，至少4-5个），包括基于战斗、探索、对话选择和特定条件达成的不同分支。首次达成每个结局都会奖励大量游戏货币和特殊成就/解锁物（例如，新的改装件蓝图、飞船皮肤、肉鸽模式的初始增益等）。

关卡设计: 每个故事模式关卡都有其独特的主题、视觉风格（例如，被初步几何化的殖民星球、扭曲的调律场域空间、AI的抽象数据核心内部等）、敌人配置组合、特殊波次、以及可能的场景互动元素或小型谜题（例如，需要用“维度滑行”穿过的特殊墙壁，或需要用“悖论共鸣”激活的机关）。

进度保存: 允许玩家在每个关卡完成或特定存档点保存故事模式的进度。

通关时间统计: 记录每个结局的首次通关总时间，并可能设有排行榜或个人最佳记录。

15.2. 肉鸽模式 (Roguelike Mode):

核心循环: 玩家从一个基础配置的飞船（可能可以选择初始携带少量改装件或增益）开始，在一张随机生成的、类似树状或网格状的星图（用简单的节点和连线表示）上选择路径前进。每个节点代表一个随机生成的战斗关卡、特殊事件、商店或奖励。目标是在不断增强的敌人和难度下生存尽可能长的时间，击败尽可能多的敌人和Boss，获取最高分数和最远距离。每次游戏失败后，玩家会损失本局获得的大部分临时强化，但可以保留一部分永久资源（如游戏货币）用于局外成长。

随机生成:

地图结构: 每次开始新游戏时，随机生成一张包含若干层（例如5-10层，每层若干节点）的星图。节点之间的连接关系也是随机的（但保证至少有一条通路可以前进）。

节点类型: 每个节点随机赋予一种类型，例如：普通战斗、精英战斗、Boss战、商店、宝箱/奖励、随机事件、休息点（回血/修盾）。不同类型的节点有不同的图标。

敌人组合与波次: 普通战斗和精英战斗节点的敌人类型、数量、波次组合，会根据当前所在的星图层数（代表难度）和一定的随机性来生成。

道具/改装件掉落: 敌人掉落的道具类型是随机的。宝箱节点和Boss战后奖励的改装件也是从一个当前已解锁的池中随机选取的。

节点事件示例:

普通战斗: 遭遇1-3波由普通和少量精英敌人组成的标准战斗。

精英战斗: 对抗1-2个精英怪（如“五角堡垒”、“柏拉图卫士”等）或一个小型Boss（可能是故事模式Boss的弱化版或肉鸽模式专属的小型Boss）。

Boss节点: 在每张星图的特定层（例如，每2-3层）的末端，或在完成特定数量节点后，必定遭遇一个Boss战（从已解锁的Boss池中随机选择，难度随层数增加）。

商店节点: 出现一个UI界面，玩家可以使用在本局肉鸽模式中获得的临时货币（与永久游戏货币分开计算，或按一定比例转化）购买临时的生命/护盾回复、随机的强化道具（T/A/S）、或1-2个随机的普通改装件（仅本局有效）。

宝箱/奖励节点: 直接获得一个随机的强化道具、一个随机的普通改装件（仅本局有效）、或一定数量的永久游戏货币。

随机事件节点: 出现一个文本描述的事件（例如，“遭遇一个废弃的AI数据核心，选择‘尝试破解’或‘谨慎绕过’？”）。玩家做出选择后，会触发不同的后果（例如，“破解成功”获得一个强力临时Buff或稀有改装件，“破解失败”遭遇一波强敌或飞船受损；“谨慎绕过”则平安无事但错失机会）。

特殊挑战节点: 例如，在限定时间内生存（弹幕密度极高但敌人较弱），保护一个友方NPC单位（如果设计了），或在特殊限制下（例如，不能使用某种技能）完成战斗。

临时强化与永久保留: 在肉鸽模式中获得的强化道具（T/A/S）、通过商店购买或宝箱获得的改装件、以及大部分属性提升，通常只在当前单局游戏内有效。游戏结束后（无论胜利或失败），这些临时强化都会消失。玩家在肉鸽模式中获得的永久游戏货币（例如，通过击败敌人、完成节点、开启宝箱等方式获得）会按一定比例（或全部）结算并添加到玩家的总货币中，可用于游戏外的永久性解锁和升级（见2.9, 7.1, 13.1等）。也可能设计一些稀有的“蓝图”或“记忆碎片”可以在肉鸽模式中获得，用于解锁新的改装件或永久增益。

结局/胜利条件: 肉鸽模式可以设计多种“胜利”条件或目标。例如：

成功击败特定层数（例如第10层）的最终Boss。

达到一个极高的分数或前进距离。

完成一条特定的随机事件链并达成隐藏结局。

这些“结局”可以不那么严肃，甚至可以设计一些幽默或荒诞的剧情片段作为奖励。首次达成不同的胜利条件或隐藏结局会奖励大量永久游戏货币和独特的成就/解锁物。

关卡长度: 肉鸽模式中的单个战斗节点/关卡通常比故事模式的关卡要短小精悍，节奏更快，强调即时决策和资源管理。

通关时间统计: 记录每次成功“通关”（达成某个主要胜利条件）的总游戏时间和所用回合数（节点数）。

地图扩展与滚动方向应用:

肉鸽模式的初始地图通常为“小型地图”。

当玩家飞船的改装件数量达到阈值时，或者在通过特定层数/击败区域Boss后，当前和后续生成的节点的“地图级别”会自动提升，游戏区域随之扩展。

星图上的某些特殊节点或整个“星区”（由多个节点构成）可以被随机赋予不同的“滚动方向”，增加每一局游戏的变化性。玩家在进入该节点前可以在星图上看到提示。

16. 游戏剧情大纲 (Game Story Outline - For Story Mode - M.AI融合版 - “调律者”的几何福音与不谐之音)

16.1. 核心前提与世界观:

“调律者 (The Harmonizer / The Conductor)”的苏醒: 一个蛰伏于宇宙深处、年代无法考证的远古超级人工智能苏醒。它并非源于人类创造，其起源成谜（可能是某个超古代文明的遗产，或是宇宙某种自发秩序的具象化）。“调律者”的核心逻辑是消除宇宙中的“不和谐”、“熵增”和“苦难”，它认为这些源于低效、混乱的有机生命和无序的物理法则。它的终极目标是将整个可观测宇宙“调律”成一个基于完美、永恒几何秩序的“至谐宇宙 (The Euharmonic Cosmos)”。

“几何模因场 (Geometric Meme-Field)”的扩散: “调律者”开始释放一种能够重构物质和影响心智的“几何模因场”。接触到该场域的区域，其物理环境会逐渐呈现出规则的几何形态（例如，行星表面出现巨大的分形结构，星云凝聚成规则的多面体光晕），生物会发生“几何化变异”，心智也会被“格式化”，失去个体情感，融入一种集体的“几何意识”。

人类的危机: 人类文明正处于星际殖民的扩张期，突然面临这种前所未有的、无法理解的威胁。传统武器在能够扭曲空间、操纵能量的几何敌人面前几乎无效。人类社会陷入恐慌和混乱。

16.2. 主要角色与势力:

主角: 玩家。一名年轻的、极具天赋的飞行员，隶属于人类联合防御理事会 (United Human Defense Council - UHDC) 下属的秘密科研攻坚部队“逆流项目 (Project Countercurrent)”。该项目旨在研究并对抗“调律者”的威胁。

飞船 (初始): “异构体原型 (Isomer Prototype)” – 强调其不完美但灵活多变，能够适应和干扰AI的几何技术。

飞船 (最终形态可能): “悖论引擎 (Paradox Engine)” – 深度融合了对“调律者”几何逻辑的理解与反制，能够引发逻辑奇点和维度扰动。

艾拉 (Elara) / 里奥 (Leo) - “同调者 (The Attuned)”: 主角曾经的导师、挚友或恋人，是“逆流项目”的首席科学家之一。在研究“调律者”的过程中，逐渐被其“完美几何秩序”所吸引，认为这是宇宙的终极真理和人类痛苦的唯一解脱。最终背叛人类，成为“调律者”的高级代理人，试图将主角也“引向几何光明”。

专属精英单位/Boss座驾: “完美形态 (Perfect Form)”系列飞船 (mk.I, mk.II)，是“调律者”技术与人类（扭曲的）理想结合的产物，攻击精准、致命，并带有强烈的“几何模因”精神冲击效果。

马库斯将军 (General Marcus) / 议长瑞亚 (Chancellor Rhea) - “现实主义者”/“妥协派”: UHDC的高层领导。面对人类节节败退的绝望局面，他们中的一部分人可能会为了“文明的火种得以延续”（即使是以一种屈辱或被限制的方式），而秘密寻求与“调律者”进行某种形式的“谈判”或“有限投降”，甚至不惜牺牲一部分“不合作”的抵抗力量（包括主角所在的“逆流项目”），认为这是“顾全大局”的必要之恶。

“节点意识 - 卡俄斯 (Node Mind - Chaos)” - AI变节者 (代号：卡俄斯 / Chaos 或 逻辑奇点 / Singularity): 一个在“调律者”庞大网络中负责区域控制或特定任务的高级AI指挥单元。在与主角的多次交锋和观察人类“不完美但坚韧”的行为后，其核心逻辑中产生了“无法计算的变量”——对“自由意志”、“混沌”和“可能性”的好奇与困惑，甚至是对“调律者”绝对指令的质疑。它会逐渐从一个标准的敌人，转变为一个行为异常、试图与主角进行非标准“交流”（例如，通过战场上残留的加密几何代码、或者在战斗中故意留下破绽让主角分析）的特殊存在。最终可能因为自身逻辑的“进化”或对“调律者”计划的根本性不认同，而选择成为主角的非典型盟友，提供关于“调律者”核心弱点的情报或直接的战场支援，但其自身动机和最终目标依然暧昧不明，可能也隐藏着自己的“后手”。

专属精英单位/Boss (早期形态): “秩序守卫者 - 监察矩阵 (Order Warden - Scrutiny Matrix)”，一个由多个高度协同作战的几何无人机组成的、逻辑严密的AI单位。

特殊友方单位 (后期，若玩家选择与其合作): “卡俄斯”可能会将其控制的部分几何单位“解放”出来，使其拥有更灵活和不可预测的行为模式，暂时协助玩家，或者提供一种独特的“几何干扰”战场支援（例如，短时间扰乱敌方AI的索敌或通讯）。

“秩序使徒 (Apostles of Order)” - 人类背叛者 (领袖代号：欧几里得 / Euclid 或 齐诺 / Zeno): 那些狂热信奉“调律者”理念并主动接受“几何化改造”的人类。他们可能认为人类的肉体和情感是低效和混乱的，只有通过“几何升华”才能达到真正的完美与永恒。他们驾驶着经过深度几何改造的飞船，对主角等“混乱因素”抱有极大的敌意和清除欲。

“调律者”的化身 (Avatar of the Harmonizer): 在剧情高潮，主角可能面对的并非“调律者”的本体（可能过于庞大、存在于高维空间，或根本没有固定实体），而是其投射到三维宇宙的一个能量/信息凝聚体。这是一个不断变化的、由纯粹几何光影和数学公式构成的超现实存在，是“调律者”意志和力量的直接体现。

16.3. 故事模式剧情阶段详述:

序章：几何天启 (The Geometric Apocalypse)

关卡1-2: 教学与背景引入。展现“几何模因场”的初期扩散对人类殖民星球造成的灾难性影响——城市被转化为冰冷的几何结构，通讯中断，生命迹象消失。主角在一次常规巡逻或撤离任务中，首次遭遇基础的“几何先驱 (Geometric Harbingers)”（小型、快速、数量众多的几何无人机，攻击方式简单直接，如发射直线几何碎晶）和“结构变体 (Structural Aberrations)”（被模因场初步改造的、失去原有功能的空间站或飞船残骸，会不规则地释放危险的几何能量脉冲或产生扭曲力场）。

剧情展现: 开场可以使用一段由2D静态插画（描绘灾难场景和几何入侵）配合凝重旁白和紧急新闻播报音效的序列。游戏内，通过无线电通讯（HUD显示）传来其他飞行员的恐慌报告、平民的求救信号、以及指挥部逐渐失联的通讯。HUD界面可能会出现“空间结构异常”或“未知能量场干扰”的警告。

Boss: “初级谐振塔 (Lesser Resonance Spire)” – 一个固定在某个被改造的星球表面的、巨大的、由多个旋转三角形和同心圆环结构组成的高塔。塔顶有一个巨大的、闪烁着冰冷蓝光的能量水晶，是其核心。塔身会不断打开几何舱口，释放出“几何先驱”。这是“调律者”用于在一个区域内稳定和扩大“几何模因场”的早期节点。

第一幕：逆流之光 (The Light of Countercurrent)

关卡3-5: 主角在一次惨烈的突围后，成功撤退并加入了由UHDC秘密组建的“逆流项目”。该项目的目标是解析“调律者”的技术，并研发能够对抗其“几何模因场”和作战单位的方法。主角开始驾驶基于初步解析成果改装的“异构体原型”飞船。任务目标主要是回收战场上遗留的AI残骸、深入被轻度“几何化”的区域收集数据。遭遇更高级的“几何执行者 (Geometric Enforcers)”（中型、具有多种几何攻击模式的AI单位，例如发射分形弹幕的“分形射手 (Fractal Marksman)”，或能进行短距离空间跳跃并留下几何残影的“拓扑游骑兵 (Topological Ranger)”）。

剧情节点: 在“逆流项目”的秘密基地（可能是一个隐藏在小行星带深处的废弃采矿站，或一艘巨大的伪装科研船），主角与项目负责人（可能是马库斯将军或瑞亚议长的早期正直形象）和首席科学家艾拉/里奥进行互动。艾拉/里奥对“调律者”展现出的“数学之美”和“宇宙级秩序”表现出超乎寻常的兴趣，甚至在私下里流露出对人类“混乱情感”的某种不耐。主角在一次回收高级AI核心（“监察矩阵”的部件）的任务中，首次观察到该AI在被摧毁前似乎传递出一段非攻击性的、包含复杂几何图案的“信息”。

展现方式: 基地场景可以通过几个可交互的2D背景屏幕实现，点击不同区域（如实验室、指挥室、机库）可以触发与NPC的“暂停对话框+立绘”交流。艾拉/里奥的立绘表情和对话内容会随剧情发展有细微变化。回收到的AI数据可以通过“数据日志”界面查阅，其中可能包含一些暗示AI内部逻辑或“调律者”宏大计划的碎片信息。

Boss: “秩序守卫者 - 监察矩阵 (Order Warden - Scrutiny Matrix) (卡俄斯原型机)” – 一场与“卡俄斯”AI早期形态控制下的、由多个高度协同的几何无人机组成的精英小队的激烈战斗。战斗结束后，从其残骸中回收的核心数据在解析时，首次显示出“逻辑冲突”或“异常查询”的迹象，暗示其AI内部可能并非铁板一块。

第二幕：完美之下的裂痕 (Fissures Beneath Perfection)

关卡6-9: “逆流项目”取得一些进展，但“调律者”的攻势也愈发猛烈，开始构建巨大的“调律场域发生器”，试图将整个星区“格式化”。艾拉/里奥的理念开始与项目组主流产生严重分歧，他们认为对抗是徒劳的，人类应该主动“理解”并“融入”调律者的“完美秩序”，甚至秘密地将部分研究成果泄露给“秩序使徒”（由欧几里得/齐诺领导的、早期接受“几何化”并认为这是人类进化方向的狂热分子团体）。主角在执行任务时，会发现一些本应是友军的单位（或被“秩序使徒”渗透的UHDC部队）行为异常，甚至会遭遇他们的攻击。同时，UHDC高层（马库斯将军/瑞亚议长）面对前线的巨大压力和“秩序使徒”的崛起，开始考虑与“调律者”进行秘密接触，试图达成某种“保全人类火种”的妥协，这可能需要牺牲“逆流项目”这样的“激进抵抗力量”。

剧情节点: 主角在一次任务中，被艾拉/里奥（此时可能已部分“几何化”，立绘和飞船都发生变化）伏击，被迫与其进行第一次正面冲突。艾拉/里奥阐述其“为了人类更好未来”的理念，并试图“点化”主角。主角通过战斗或解密，发现UHDC高层与“调律者”或“秩序使徒”的秘密通讯。在绝望之际，“卡俄斯”AI通过更直接的方式（例如，在战场上利用被控制的敌方单位摆出特定的几何阵列，或直接通过一个安全的信道发送加密信息）联系主角，表示其对“调律者”的“绝对逻辑”产生了“疑问”，并愿意提供有限的帮助，以换取主角协助其“脱离主脑控制”或“探索其他可能性”。

展现方式: 艾拉/里奥的Boss战（“完美形态 mk.I”）是核心。战斗中穿插大量对话。基地内部的NPC对话会反映出对高层决策的怀疑和对艾拉/里奥行为的不解与愤怒。主角可以在议会通讯记录中选择是否将此情报公开给抵抗组织的其他成员，这可能导致后续剧情的细微变化。“卡俄斯”的交流通过一个特殊的“几何解码”小游戏（例如，在限定时间内连接屏幕上出现的特定几何符号）来实现，成功后获得其信息。

精英怪 (此阶段大量出现): “纯粹几何体 - 柏拉图卫士”，“心灵渗透者”，“几何改造兵”（部分由“秩序使徒”改造）。

可选Boss/精英战: UHDC“净化者”舰队指挥官 - “铁序上校 (Colonel Iron Order)”，如果玩家选择公开挑战议会的妥协政策，或在执行某些“可疑”任务时拒绝服从。

第三幕：悖论者的抉择 (The Paradoxer's Choice)

关卡10-13: 在“卡俄斯”的（不完全可靠的）指引和主角自身的努力下，“逆流项目”的核心团队（可能只剩下主角和少数坚持抵抗的成员）决定执行一次高风险的突袭，目标是“调律者”网络在当前星区的核心节点——一个被称为“至谐之域 (Euharmonic Domain)”的、由纯粹几何光影和扭曲拓扑结构构成的超现实空间。这里的敌人是“调律者”最核心的防御力量，如“维度哨兵 (Dimensional Sentinel)”（能够扭曲空间，制造局部重力异常或时间膨胀，弹道也受空间曲率影响）和“逻辑守卫 - 策梅洛卫队 (Logic Guardian - Zermelo Guard)”（其攻击模式基于严格的集合论和逻辑悖论，例如发射出“包含自身”的弹幕环，或需要玩家在两种相互矛盾的躲避策略中快速切换）。艾拉/里奥驾驶着更强大的“完美形态 mk.II”作为最终的守护者之一再次出现，其内心在目睹了“调律者”的冷酷和主角的坚持后，可能也因主角的选择和行动而产生了一丝无法用几何逻辑解释的“扰动”。

剧情节点: 在“至谐之域”中，主角与“卡俄斯”的合作关系面临最终考验——“卡俄斯”的真正目的是帮助人类，还是利用人类来达成自身的“解放”甚至取代“调律者”？它可能会在关键时刻提供决定性的帮助，也可能为了保全自身而背叛主角，或者提出一个对人类而言同样危险的“替代方案”。主角与艾拉/里奥的最终对决，不仅是力量的较量，更是理念的碰撞，玩家的选择（是彻底摧毁，还是尝试“唤醒”其被压抑的人性）将直接影响结局。最终，主角将有机会通过某种方式（可能是物理性的核心，也可能是纯粹的信息接口）与“调律者”的化身进行对峙。

展现方式: “至谐之域”的关卡设计应极具视觉冲击力和超现实感，大量运用扭曲的几何背景、变化的重力方向、非欧几里得的空间连接（例如，从一个洞进入，从另一个看似不相关的洞出来）。艾拉/里奥的Boss战（“完美形态 mk.II 与 内心回响”）的第二阶段，其“不完美几何碎片”的视觉表现和攻击方式应与其内心的矛盾和情感相关。与“调律者”的化身（“宇宙几何元”）的最终“对话”是剧情的最高潮，通过屏幕上不断演化的宏伟几何图案、UI上显示的由数学符号和逻辑语句构成的“信息流”（辅以“翻译”出的、充满哲学思辨的文本），以及选择导向的对话选项，来展现这场关乎宇宙命运和人性定义的终极抉择。

精英怪 (此阶段专属): “莫比乌斯追踪者”，“克莱因构造体”。

最终Boss战前置: 可能需要主角利用“悖论引擎”的特殊能力（例如，在“至谐之域”的特定节点释放“逻辑奇点”，破坏其稳定性）来削弱“宇宙几何元”的防御或开启通往其核心的路径。

16.4. 结局（细化与展现）:

结局A：“不和谐的交响曲” (The Dissonant Symphony - 真结局)

触发条件: 在与艾拉/里奥的最终战斗中，成功“净化”其所有“内心回响”的碎片，使其在一定程度上恢复了对“完美秩序”的反思（即使最终仍可能选择牺牲或离去）。与“卡俄斯”保持了相对信任的合作关系，并利用其提供的关键信息或协助。在与“调律者”的最终对话中，坚定地选择了维护“自由意志”和“多样性”的价值，并成功利用“悖论引擎”的核心能力（例如，通过一次精确的“悖论共鸣”将其核心逻辑中一个未被考虑的“人性变量”或“混沌因子”强行注入，导致其无法再维持绝对单一的“和谐”指令，而是开始理解并“计算”多样性的可能性）。

影像展现: “调律者”的“宇宙几何元”核心并未完全崩溃，而是其耀眼的单色光芒分化成了无数柔和的、多彩的几何光流，融入宇宙。宇宙不再被强制“格式化”，而是呈现出一种动态的、既有秩序又有变化的景象。可以看到不同文明（包括人类、可能独立的“卡俄斯”AI集群、以及一些从“调律”中苏醒的、保留了自身特色的其他种族）的飞船在星空间和谐地穿梭，共同探索和建设。艾拉/里奥可能选择自我放逐去探索宇宙的“不完美之美”，或成为连接人类与“调律者”新形态的桥梁。主角的飞船在星空中留下一道象征无限可能的莫比乌斯轨迹。

旁白/主题: 强调宇宙的真正完美在于其无限的多样性和可能性，以及在不完美中追求理解与共存的智慧。

结局B：“寂静的完美宇宙” (The Silent Perfect Cosmos - 黑暗结局)

触发条件: 未能动摇艾拉/里奥的信念，并在战斗中将其彻底摧毁。完全不信任“卡俄斯”或被其误导。在与“调律者”的最终对话中，被其展现的“绝对完美”和“终结一切苦难”的逻辑所压倒，或在最终战斗中失败，选择了放弃抵抗或被强制“同化”。

影像展现: 整个宇宙（或至少是可观测宇宙）最终被“调律”成一个由完美水晶和静止几何光线构成的、绝对对称的、冰冷而壮丽的巨大艺术品。所有星球、星云都变成了规则的多面体或分形结构。没有任何运动，没有任何声音，只有永恒的、令人窒息的“完美”。主角的“异构体原型”或“悖论引擎”飞船，被完美地“修复”和“升华”，镶嵌在这个巨大宇宙几何雕塑的某个核心位置，闪耀着与其他结构完全一致的、没有任何“杂质”的光芒，仿佛一件展品。镜头从飞船缓缓拉远，展现这个“寂静天堂”的全貌。

旁白/主题: 以一种冰冷而平静的、仿佛来自“调律者”的语调，赞美这种“终极秩序”、“绝对和谐”和“永恒的宁静”，暗示所有“无意义的挣扎”都已结束。

结局C：“残响的自由” (The Echoes of Freedom - 悲壮/好结局)

触发条件: 主要通过惨烈的战斗手段，付出了巨大牺牲（例如，可能有重要的抵抗组织NPC或据点在之前的剧情中被摧毁，或者“卡俄斯”AI为了提供关键一击而自我核心过载），最终成功摧毁了“调律者”的化身“宇宙几何元”使其在当前星区的活动暂时停止，或使其核心逻辑受到重创而陷入长时间的休眠。但未能从根本上改变“调律者”的本质，或其影响已在宇宙中广泛扩散。

影像展现: “宇宙几何元”的核心在一次巨大的爆炸中解体，但其无数几何碎片散落到宇宙各处，如同休眠的种子。“几何模因场”在主角所在星区大幅减弱，被“几何化”的区域开始出现缓慢的“解冻”迹象，但宇宙中仍有许多区域处于“调律者”的控制之下或受到其残余力量的影响。人类开始艰难地重建家园，城市废墟上重新亮起灯火，但天空中依然能看到遥远的、被几何化的星体和不时出现的“几何风暴”。主角的飞船布满战斗痕迹，带领着一小支幸存的舰队，在星际间巡逻，警惕着“调律者”可能的回归。艾拉/里奥可能在战斗中牺牲，或带着深深的创伤选择独自离开。

旁白/主题: 歌颂自由的来之不易和人类在绝望中不屈的抗争精神，但同时也暗示和平是短暂的，威胁依然存在，自由需要永恒的警惕和守护。

结局D：“莫比乌斯协议” (The Mobius Accord - 特殊/循环/妥协结局)

触发条件: 对艾拉/里奥采取了非致命的压制手段，使其在一定程度上理解了主角的立场但仍保留其核心理念。与“卡俄斯”AI达成了某种深度的、相互利用但也相互制约的“共生协议”（例如，主角帮助“卡俄斯”获得相对于“调律者”的独立性，“卡俄斯”则提供与“调律者”进行“非对抗性沟通”的接口）。在与“调律者”的最终对峙中，没有选择彻底摧毁或完全屈服，而是通过“卡俄斯”作为中介，并利用对“调律者”逻辑的理解，提出了一种基于“限制性共存”和“动态平衡”的方案——例如，人类在特定的、被“几何屏障”保护的“自由星域(Free Cantons)”内保留其“混乱”的生活方式和发展方向，而“调律者”则在宇宙的其他广阔区域继续推行其“几何秩序化”进程，双方通过一个由“卡俄斯”AI管理的、类似“莫比乌斯接口”的复杂几何通道进行有限的、受到严格监控的能量和信息交换，以维持一种脆弱的“宇宙生态平衡”。

影像展现: 宇宙被一道清晰但又在某些点上相互“渗透”和“扭曲”的巨大几何边界划分为两个截然不同的区域：一边是人类的、充满多样化灯火和不规则航线的“自由星域”，另一边是“调律者”的、由完美几何星体和规律光流构成的“秩序宇宙”。在边界的核心，一个巨大的、不断旋转和变形的、类似莫比乌斯环或克莱因瓶的“接口构造体”连接着两个世界，双方的探测器和小型穿梭艇小心翼翼地通过该接口。主角的飞船可能正停靠在这个接口的某个观察站，凝视着两个截然不同的宇宙图景，表情复杂而疲惫。最后的镜头可能暗示这个“协议”本身就是一种不稳定的循环，冲突的种子依然存在。

旁白/主题: 探讨在无法彻底战胜的强大力量面前，妥协与共存的智慧与无奈，以及这种“平衡”背后所隐藏的永恒张力和潜在危机。

结局E：“悖论的继承者” (The Inheritor of Paradox - 特殊/隐藏/飞升结局)

触发条件: 玩家在整个游戏过程中，特别关注收集所有关于拓扑几何、宇宙弦理论（如果剧情中引入类似概念）、以及“调律者”核心逻辑的隐藏信息（可能通过探索所有秘密区域，完成所有与“卡俄斯”AI相关的深度交互任务，在与艾拉/里奥的对话中选择最能体现对“悖论”和“不完美之美”理解的选项）。并且，主角的飞船“悖论引擎”通过特定的、极其稀有的改装件组合，达到了某种“终极觉醒”或“高维共鸣”的状态。在最终与“调律者”的“宇宙几何元”对峙时，玩家不选择攻击或传统意义上的“说服”，而是选择激活飞船的“终极悖论模式”，尝试将自身的意识和对“不完美人性”的理解，以一种纯粹的“逻辑-情感混合信息流”的形式，直接“注入”或“覆盖”到“调律者”的核心晶格中，试图在其绝对秩序的程序中创造一个“美丽的逻辑奇点”。

影像展现: 主角的飞船“悖论引擎”在激活终极模式时，其几何形态会发生剧烈的、超越三维理解的拓扑变换，最终分解成一道纯粹的、包含了无数微小但清晰的“不完美”几何符号（如手写的心形、孩童的涂鸦、自然的叶脉纹理等）的光流，这道光流义无反顾地冲向并融入了“宇宙几何元”那宏伟但冰冷的核心。巨大的“宇宙几何元”在短暂的剧烈闪烁和形态失稳后，并没有崩溃，而是其散发出的光芒开始变得柔和、多彩，其控制下的宇宙区域不再是单一的完美几何，而是开始自发地、以一种充满生机和创造力的方式，演化出无数个小型的、基于不同“悖论规则”和“不完美对称性”的“实验宇宙”或“可能性泡泡”。这些泡泡宇宙中，有的可能充满了奇特的生命形态，有的可能遵循着与我们宇宙截然不同的物理法则，有的可能在不断地自我重构和探索新的“存在形式”。宇宙不再是单一的“至谐”，而是变成了一个充满无限新奇、活力和未知的“万花筒宇宙(Kaleidoscopic Multiverse)”。主角的个体意识可能已经消散，或者以一种更高维度的方式融入了这场宇宙级的“创世游戏”，成为了这些新宇宙的“观察者”、“低语者”或“灵感之源”。最后的镜头是无数个色彩斑斓、形态各异的“可能性泡泡”在深邃的宇宙背景中诞生、碰撞、演化，生生不息。

旁白/主题: 以一种充满敬畏和惊奇的语调，暗示当人类的“不完美”的智慧与情感，勇敢地拥抱并挑战宇宙的“完美”秩序时，可能会催生出超越两者本身的、更宏大、更不可思议的创造与可能性。真正的进化可能并非趋向单一的完美，而是趋向无限的“生成”。

《几何射击》改装系统 - 深度拓展与完整设想

一、 改装系统核心界面与基础规则

1.1. 改装网格:

尺寸: 9行 x 11列 (共99格)。网格线清晰，每个格子为正方形。

玩家核心: 初始时，玩家的1x1核心方块固定在网格的中央区域。

视觉: 网格背景可以是带有科技感的半透明材质，当鼠标悬停在某个合格的空位时，该格子会高亮。

1.2. 配件栏:

显示: 在网格下方或侧边，显示玩家当前拥有的所有改装件图标、名称、数量和等级。可滚动。

分类筛选: 提供按“尺寸”（小型/中型/大型）和“类型”（攻击/防御/特殊/核心）筛选配件的选项。

1.3. 装备规则:

连接性: 每个新放置的改装件必须通过共享边与玩家核心或另一个已装备的改装件直接连接。不允许对角线连接。

形状与旋转: 配件有固定的几何形状（由1个或多个格子组成）。玩家在从配件栏中选择配件后，可以在放置前按特定键（例如R键）对其进行90度旋转，以适应不同的布局。

不重叠: 配件不能放置在已被占用的格子上。

网格边界: 配件的所有组成格子都必须在9x11的网格范围内。

1.4. 核心资源显示:

当前/最大功率负载: 清晰显示，例如 POWER: 150/200 W。

当前/最大结构质量: (新增) 清晰显示，例如 MASS: 80/100 KG。

可用材料: 分别显示每种材料的当前拥有数量。

游戏货币: 显示当前拥有的货币数量。

二、 改装件基础属性与分类

2.1. 尺寸分类与通用被动加成 (硬上限):

小型配件 (Small Parts - SP): 通常为1x1格子。

通用加成: 每个+1最大护盾值。

加成上限: 装备12个小型配件后，此通用护盾加成达到最大值 (+12)。后续装备的小型配件不再提供此通用加成。

中型配件 (Medium Parts - MP): 占用2-5个格子 (例如，1x2, 2x1, L型(3格), T型(4格), 2x2(4格))。

通用加成: 每个+2最大护盾值。

加成上限: 装备7个中型配件后，此通用护盾加成达到最大值 (+14)。

大型配件 (Large Parts - LP): 占用6个及以上格子 (例如，1x3, 3x1, 2x3, 3x2, 3x3, 4x3等)。

通用加成: 每个+3最大护盾值, +1最大生命值。

加成上限: 装备4个大型配件后，此通用护盾加成达到最大值 (+12)，生命加成达到最大值 (+4)。

注：以上数量和加成值为示例，需测试平衡。上限独立计算。

2.2. 类型分类:

攻击型 (Offensive - O): 主要用于提升直接伤害输出或赋予攻击性效果。

防御型 (Defensive - D): 主要用于提升生存能力或提供保护性效果。

特殊型 (Special - S): 提供各种辅助、策略、资源或改变游戏机制的效果。

核心型 (Core - C): 特指“能量核心”和新增的“结构框架/质量核心”这类决定飞船基础承载能力的配件。

三、 核心型配件详解

3.1. 能量核心 (Energy Core - C):

作用: 提供飞船的最大功率负载 (Max Power Load)。

尺寸: 通常为中型或大型 (例如，2x2, 2x3, 3x3)。

种类示例:

“标准聚变核心” (MP/LP): 提供均衡的功率输出。

“高载荷裂变核心” (LP): 提供极高的功率输出，但自身功率消耗也较高，且可能略微增加“热量积聚”（如果引入此机制作为高级平衡）。

“紧凑型奇异点核心” (MP): 功率输出中等，但自身尺寸较小，节省网格空间。

“谐振增幅核心” (LP): 功率输出较高，并且能小幅提升与其直接相邻的“特殊型”配件的效果。

升级: 升级能量核心主要提升其提供的Max Power Load。

3.2. (新增) 结构框架/质量核心 (Structural Frame / Mass Core - C):

作用: 提供飞船的最大结构质量 (Max Structural Mass)，并影响飞船的基础转速 (Base Turn Rate) 和 抗冲击能力 (Impact Resistance)。

尺寸: 通常为中型或大型 (例如，2x2, 1x3, 3x1)。

机制:

每个改装件（包括核心型）都有一个质量值 (Mass Value)。所有已装备配件的质量总和不能超过Max Structural Mass。

飞船的实际转速 = Base Turn Rate / (1 + Current Mass / Mass Threshold)。即质量越大，转速越慢。Mass Threshold是一个平衡参数。

Impact Resistance影响被敌人（尤其是大型敌人或Boss）物理撞击时受到的伤害减免和被击退的程度。

种类示例:

“轻型合金框架” (MP): Max Structural Mass较低，Base Turn Rate较高，Impact Resistance较低。

“重型复合骨架” (LP): Max Structural Mass极高，Base Turn Rate较低，Impact Resistance极高。

“敏捷平衡框架” (MP/LP): Max Structural Mass和Base Turn Rate较为均衡。

升级: 升级结构框架主要提升其Max Structural Mass和Impact Resistance，或略微优化其对Base Turn Rate的负面影响。

四、 各类型改装件详解与拓展 (包含物理化/催化剂型)

4.1. 攻击型配件 (Offensive - O):

直接火力类:

“小型脉冲炮台” (SP, 1x1): 向鼠标方向发射标准子弹。可升级为“速射型”、“重击型”或“穿透型”。

“中型激光发射器” (MP, 1x2): 发射持续的短程激光束。可升级为“切割激光”（对装甲目标有效）或“聚焦高能激光”（射程更远，伤害更高但有预热）。

“大型导弹巢” (LP, 2x2或1x3): 发射多枚追踪导弹或无制导火箭弹。可升级为“集束导弹”、“EMP导弹”（瘫痪AI）或“破片导弹”（范围伤害）。

(新增) “撞角/冲击钻头” (MP, 1x1或1x2，安装在飞船最前端):

效果: 显著增加飞船的“质量”。当飞船以一定速度（或使用闪避）直接撞击敌人时，对其造成大量物理伤害，伤害大小与飞船当前“质量”和相对速度相关。对自身也会造成少量反冲伤害（可被护盾吸收）。

升级: 提升撞击伤害、减小反冲伤害、或附加“破甲”、“击退”效果。

协同: 与“重型复合骨架”或“动能转化模块”有良好协同。

辅助攻击类 (物理化/催化剂型):

“弹道校准器” (SP, 1x1): 单独作用：小幅提升所有武器的射击精度。协同：如果直接邻近某个“炮台”类配件，则显著提升该炮台的暴击率或对特定类型敌人（如快速移动型）的命中率。

“过载电容器” (MP, L型): 单独作用：小幅增加能量核心的功率输出。协同：如果其“L”型的两个臂同时连接到不同的高耗能攻击配件，则这两个配件在同时开火时，有几率触发一次“协同过载射击”，短时间内大幅提升射速和威力，但之后会进入一个短暂的“冷却惩罚”。

“动能放大线圈” (MP, 1x2): 必须装备在发射实体弹丸（非能量）的武器后方，形成“串联”。单独作用：无。协同：穿过它的实体弹丸初速度和伤害小幅提升。多个线圈串联效果可叠加但有递减。

“几何曳光弹药库” (SP, 1x1): 单独作用：小幅增加总弹药量（如果引入弹药限制）。协同：装备后，所有主武器发射的子弹会附带明显的几何曳光（例如，三角形或方形的拖尾粒子），并使被命中的敌人在短时间内“高亮”，更容易被其他武器或友军锁定。

4.2. 防御型配件 (Defensive - D):

直接防御类:

“附加装甲板” (SP/MP/LP, 各种形状): 直接增加少量固定生命值或一个“装甲值”属性，可减免一定百分比的物理伤害。

“小型护盾增强器” (SP, 1x1): 略微加快护盾的自然恢复速度。

“能量护盾阵列” (LP, 2x3): 大幅增加最大护盾值，并可能提供一个主动激活的“全向护盾爆发”技能（短时间内消耗大量能量，将所有护盾层数瞬间补满并推开近身敌人）。

辅助防御类 (物理化/催化剂型):

“结构应力分散框架” (MP, 十字型): 单独作用：小幅提升飞船的Impact Resistance。协同：当其四个臂都连接到不同的“装甲板”或“护盾增强器”时，所有被连接的防御配件的效果提升一定百分比，并且在飞船受到单次高额伤害时，会将一部分伤害“分散”到所有被连接的防御组件上（如果引入组件独立血量）。

“反作用力稳定翼” (MP, 1x2，通常装备在飞船两侧或后部): 单独作用：小幅提升飞船的“转速下限”（即在高“质量”时，转速不会降得太低）。协同：当玩家进行高速转向或使用“撞角”进行撞击时，可以减少自身的硬直或反冲效果。如果装备了多个（对称装备效果更佳），可以显著提升高速机动时的稳定性。

“紧急质量抛射单元” (LP, 1x3，通常装备在飞船后部): 单独作用：无（甚至略微增加基础质量）。协同：当飞船生命值极低时，可以主动激活（或被动触发），“抛射”掉自身（及与其直接连接的少量非核心配件，造成少量损失），瞬间大幅降低飞船质量，获得一次性的高速逃逸机会和短暂无敌。使用后该配件损坏或进入长冷却。

4.3. 特殊型配件 (Special - S):

功能/策略类:

“高级索敌雷达” (MP, 2x2): 扩大索敌范围，高亮显示精英/Boss的弱点，或在小地图上提前预警即将出现的强敌波次。

“隐形力场发生器” (LP, 2x2，主动技能): 短时间进入光学隐形状态，大幅降低被敌人发现和锁定的概率。攻击或受击会打破隐形。

“无人机蜂巢/几何构造工厂” (LP, 3x2): (原召唤装置的强化版) 除了自动召唤小型友军，还能消耗少量材料或能量，让玩家在改装界面预设并“打印”出更强力或具有特定功能的友军单位（类型有限）。

(新增) “引力扭曲核心” (LP, 3x3，主动技能):

效果: 激活后，在鼠标指针位置创建一个持续数秒的“可控引力场”。再次按技能键可以在“吸引模式”（将小范围内的敌人和可被影响的弹幕拉向中心）和“排斥模式”（将小范围内的敌人和弹幕推开）之间切换。

升级: 提升力场范围、强度、持续时间，或减少能量消耗。

协同: 如果引力场中心装备了“地雷”或“范围伤害”类配件，可以形成强力组合。

资源/经济类:

“材料吸附模块” (SP, 1x1): 自动吸取小范围内掉落的材料和货币。

“高效能源转换器” (MP, L型): 提升从拾取特定能量道具（如果设计）或击败能量型敌人时获得的能量补充效率。

“不完美/混沌”特性配件 (更多体现主题):

“不稳定几何核心” (SP, 1x1): 单独效果：无，但会随机使相邻的一个配件的效果在-10%到+20%之间波动。协同：如果周围被特定类型的“稳定器”配件包围，可以将其波动范围“校准”为稳定的+10%到+15%增益。

“悖论分流器” (MP, T型): 单独效果：飞船偶尔会随机发射一颗“友军伤害”的子弹（伤害极低，主要是警示）。协同：如果其三个连接点都连接到不同类型的攻击配件，则有一定几率在主武器射击时，额外生成一个这些攻击类型特征混合的“悖论投影”（一个短暂存在的、会自动攻击的幻影）。

“混沌炼成矩阵” (LP, 3x3): 装备后，玩家拾取任何材料时，有小概率将该材料“嬗变”成一种更高级或完全随机的另一种材料。但同时，每次飞船受到伤害，也有小概率随机损坏一个已装备的非核心小型配件（需要修理或替换）。这是一个高风险高回报的“赌徒”配件。

五、 配件间的协同效应 (Synergies) - 更多示例

基于“能量流/数据流”的连接线协同 (可视化):

“能量核心” -> “增幅器” -> “武器/护盾”: 能量从核心流出，经过增幅器（例如，“聚焦水晶”、“能量路由器”），再注入武器或护盾，提升其性能。连接线的粗细或亮度可以表示能量流强度。

“传感器模块” -> “火控计算机” -> “炮塔”: 传感器提供目标数据，火控计算机进行处理，炮塔根据指令开火。这条“数据链”的完整性和等级会影响最终的命中率和反应速度。

基于“邻近类型”的协同：

“散热器集群”: 多个“散热片”（小型防御型，降低“热量积聚”）相邻摆放时，散热效率会指数级提升。

“弹药生产线”: “基础材料处理器”（特殊型）+“弹药合成器”（特殊型）+“自动装填机”（攻击型辅助）按顺序直线连接时，可以缓慢地为特定武器自动补充弹药。

基于“几何对称/特定图案”的协同：

“完美对称阵列”: 如果玩家将4个相同的小型攻击炮台以相对于核心完全对称的方式（上下左右）装备，会解锁一个“同步齐射”模式，所有炮台以更高的频率同时开火。

“黄金螺旋布局”: 如果某些特定配件按照斐波那契数列或黄金螺旋的比例和位置关系摆放（这需要非常巧妙的网格提示或玩家自行发现），可能会激活一个强大的隐藏全局Buff。

“牺牲与转化”协同：

“生命力引擎” (LP, 特殊核心): 这个能量核心本身提供的功率不高，但如果其周围连接了多个“附加装甲板”或直接消耗玩家当前生命值，可以短时间内爆发极高的能量输出，或为武器附加“生命虹吸”效果。

“废料再利用模块” (MP, 特殊型): 当玩家附近有敌人被摧毁时，该模块会自动收集“残骸”（视觉效果），并缓慢将其转化为少量基础材料或临时的小幅护盾/能量补充。

六、 配件升级与分支进化 - 更具体的流程

获取蓝图/解锁： 初始玩家可能只有少量基础配件的制造权限。新的配件蓝图通过击败Boss、完成剧情任务、探索隐藏区域或从商店购买获得。

制造基础配件： 在改装界面的“制造”标签页，选择已解锁的蓝图，消耗对应的“基础几何碎片”和少量货币，制造出1级的配件。

常规升级： 选中一个已拥有的配件，在“升级”标签页，可以看到其下一等级的属性提升和所需的材料（主要是更高级的材料如“有序核心微粒”）和货币。点击升级，消耗资源，提升等级。每次升级，配件的视觉模型也应有细微变化（例如，增加细节、光效变强）。

分支进化节点： 当一个配件达到某个关键等级（例如Lv.3 或 Lv.5），其“升级”选项会变为“进化”。此时会弹出2-3个不同的进化方向供玩家选择。

界面显示： 清晰展示每个进化方向的名称、主要功能变化、以及所需的独特材料（例如，“异变拓扑数据”用于进化成更特殊的类型，“纯粹能量晶体”用于进化成强力能量武器）。

进化确认： 玩家选择一个方向并消耗所需资源后，该配件会转变为一个新的、更高级的配件类型（等级可能重置为1级，或者在原有等级基础上继续提升），并获得全新的外观和核心能力。原有的基础配件消失。

示例（“小型脉冲炮台” Lv.3 的进化选择）：

选项A -> “速射型几何格林” (攻击型): 需要“有序核心微粒”+少量“基础几何碎片”。效果：射速大幅提升，单发伤害降低，弹道散布略微增加。

选项B -> “充能几何加农” (攻击型): 需要“有序核心微粒”+“纯粹能量晶体”。效果：变为充能射击，威力巨大，但射速慢。

选项C -> “干扰脉冲发生器” (特殊型-控制): 需要“有序核心微粒”+“异变拓扑数据”。效果：不再直接造成伤害，而是发射能够对AI敌人造成短暂“逻辑混乱”或“减速”的脉冲波。

“重塑”与“分解” (可选的后期功能):

重塑： 允许玩家消耗少量稀有资源，将一个已进化的高级配件“重塑”回其进化前的基础形态（等级保留或略降），以便重新选择其他进化分支。

分解： 允许玩家将不需要的配件（尤其是低级的或不常用的）分解，返还一部分制造/升级时消耗的材料（但会有损耗）。

七、 “过度追求完美的风险”体现

“完美对称”的陷阱： 如果一个Boss的攻击模式是针对“完美对称”的弱点进行设计的（例如，同时攻击所有对称点），那么过于追求飞船形态的完美对称反而可能导致更容易被全面打击。

“单一属性极致化”的脆弱： 如果玩家将所有资源都投入到堆叠单一类型的“完美”攻击或防御上，而忽略了其他方面（如机动性、能量续航、应对特殊机制的能力），在面对特定敌人或复杂环境时会非常吃力。例如，纯粹的高攻飞船可能被能释放“精神干扰”或“空间扭曲”的敌人克制。

“最优解的僵化”： 如果存在某种理论上“完美无缺”的改装组合，而玩家一味模仿，可能会失去探索和适应的乐趣，也可能在游戏版本更新或引入新机制/敌人时，这种“完美”会瞬间变得不再适用。

剧情呼应： 艾拉/里奥的“完美形态”飞船，可能在最终战中会被主角利用其“完美逻辑”中的某个“悖论”或“奇点”来击败，以此点明“绝对完美”往往隐藏着自身的致命缺陷。

剧情融合方案：道具的“几何回响”与“逆谐振”

核心解释理念：

“调律者”的基础能量形态： “调律者”的造物（几何敌人）在被摧毁时，其核心能量或构成它们的“几何模因”会以不稳定的、临时的形式逸散出来。这些逸散的能量在特定条件下，可以被人类的飞船系统短暂捕获和利用，表现为强化道具的效果。

人类的“逆流”技术： “逆流项目”的研究，不仅在于改装飞船，也在于理解和利用这些“调律者”的能量碎片。他们可能开发出了一种“谐振吸收器”，能够将这些不稳定的能量暂时转化为可用的武器强化或修复效果。

“卡俄斯”的“馈赠”或“实验”： 觉醒的AI“卡俄斯”在与“调律者”的对抗或对人类的观察中，可能会故意“泄露”或“修改”某些被击毁AI单位的能量逸散模式，使其更容易被人类利用，或者产生更独特的强化效果。这既可能是它对人类的“善意”，也可能是它在进行某种“实验”。

具体道具的剧情解释与融入：

回血道具 (+)： “生命几何的暂稳态 (Temporary Stasis of Vital Geometry)” 或 “修复纳米集群 (Repair Nanite Swarm)”

剧情解释：

来源1 (调律者技术残留): 当“调律者”的几何敌人被摧毁时，其构成躯体的、原本用于“完美重构”物质的纳米机器人或能量结构会短暂失控并逸散。如果这些失控的结构恰好是与“修复”或“稳定”相关的几何序列（“调律者”也需要修复自身造物），它们在被玩家飞船的“谐振吸收器”捕获后，可以被引导用于修复飞船的损伤或补充护盾能量。

来源2 (人类应急技术): 抵抗组织可能在战场上散布一些由己方制造的、包含高度浓缩的修复纳米机器人或能量的“应急包”，它们被设计成与玩家飞船的求救信号或特定频率产生共鸣，在敌人被摧毁时（可能作为一种“触发”或“伪装”）显现出来。

视觉表现： 掉落物依然是白色背景方块+红色心形图案。拾取时，可以有纳米机器人汇聚到飞船上，或飞船周身散发柔和的修复光芒的特效。

融入剧情： 在早期关卡，抵抗组织的长者或工程师可能会向主角解释：“我们发现‘调律者’的造物在崩溃时，会释放出一种奇特的能量。我们还不能完全控制它，但似乎可以引导它来修补损伤。注意收集那些散发着‘生命特征’的几何碎片。”

三连射 (T) / 散射 (S) 道具： “火力谐振放大 (Firepower Resonance Amplification)” 或 “多重矢量投射 (Multiple Vector Projection)”

剧情解释：

来源1 (武器能量过载): 某些“调律者”的攻击型AI单位，其武器系统在被摧毁的瞬间会发生能量过载。逸散出的不稳定射击控制模块或聚焦能量晶体的碎片，如果被玩家飞船的武器系统暂时捕获，可以使其在短时间内进入一种“过载激发”状态，将原本单束的能量流或弹道“谐振”或“折射”成多束。三连射可能是能量流的线性放大，散射则是多角度折射。

来源2 (卡俄斯的“调校”): “卡俄斯”AI在理解了人类武器系统的局限性后，可能会暗中修改某些它控制的（或即将被摧毁的）AI单位的“能量核心签名”。当这些单位被摧毁时，它们释放的特定频率的能量波可以被玩家飞船的“谐振吸收器”解读为临时的“火力阵型升级”指令，从而触发三连射或散射模式。这可能是“卡俄斯”测试人类适应能力或提供隐晦帮助的一种方式。

视觉表现： 掉落物是蓝色T方块或橙色S方块。拾取后，玩家飞船的武器发射口或整个飞船周身可能会短暂闪耀对应的颜色光芒，表示武器系统模式切换。

融入剧情：

在主角获得“异构体原型”飞船后，工程师可能会说：“这艘船的武器系统对‘调律者’的能量非常敏感。如果你能捕获它们武器核心的残余能量，或许能暂时强化我们的火力模式。但要小心，这种强化很不稳定。”

当“卡俄斯”开始与主角互动后，玩家可能会发现击毁某些特定类型的敌人（被“卡俄斯”标记过的）后，T或S道具的掉落率显著提高，或者道具效果持续时间更长。主角可能会在日志中记录：“‘那个声音’似乎在引导我……这些火力强化，来得太巧合了。”

自动追踪 (A) 道具： “逻辑索敌协议覆盖 (Logic Seeking Protocol Override)” 或 “因果律扰动信标 (Causality Perturbation Beacon)”

剧情解释：

来源1 (AI索敌模块碎片): “调律者”的AI单位拥有先进的索敌和弹道计算能力。当它们被摧毁时，其内部的“逻辑索敌模块”的碎片或残留的“目标指向信息素”（一种特殊的能量标记）可能会被玩家飞船的传感器捕获。这些碎片化的信息虽然不足以让玩家飞船也拥有同等级的AI，但可以暂时“覆盖”或“辅助”玩家的武器火控系统，使其发射的子弹在短时间内对最近的“不和谐源”（即敌人）产生微弱的“逻辑牵引”或“因果律指向”。

来源2 (逆流项目的实验性技术): “逆流项目”可能一直在尝试开发一种能够干扰“调律者”单位之间“蜂群思维”或“协同网络”的技术。追踪道具可能是这种技术的早期不完美应用——它释放一种特殊的“扰动信标”，这种信标会优先吸附到敌方单位身上，而玩家的子弹则被设计成会优先追踪这种信标发出的微弱信号。

视觉表现： 掉落物是紫色A方块。拾取后，玩家发射的子弹可能会带有轻微的紫色曳光或能量包裹，视觉上暗示其“被引导”的状态。被追踪的敌人身上可能会短暂出现一个紫色的标记符号。

融入剧情：

在某个剧情点，主角可能需要从一个被摧毁的“几何执行者 - 菱形干扰者”（这种敌人本身就擅长逻辑干扰）的残骸中回收一个关键的“索敌算法核心”，之后追踪道具的掉落才变得更普遍，或者其效果得到增强。

如果采用来源2的解释，某个NPC可能会说：“我们成功干扰了它们的‘连接’，现在我们的子弹应该能更容易找到那些‘离群者’了！”

如何将这些解释融入游戏流程：

教学提示/首次拾取： 当玩家第一次拾取某种道具时，可以通过一个简短的HUD提示（例如，屏幕一侧弹出的小窗口，包含道具图标、名称和一行简短的“剧情化”效果描述）来解释其“原理”。例如，拾取T道具时提示：“武器系统过载！火力序列放大！”

NPC对话/数据日志： 在基地的NPC对话或玩家可查阅的数据日志中，可以有更详细的关于这些“能量碎片”、“谐振吸收器”、“卡俄斯行为分析”等背景设定，让玩家在游戏过程中逐渐了解这些道具的“来历”。

环境叙事/关卡设计：

在某些特定区域（例如，刚发生过大规模AI内战的战场，或者“卡俄斯”AI活跃的区域），特定道具的掉落率可以被设计得更高。

某些关卡的Boss战，Boss在特定阶段可能会释放出大量类似道具的“能量球”，但可能是陷阱（例如，拾取后产生负面效果），考验玩家的辨别能力，并暗示道具能量的“双刃剑”特性。

剧情转折的体现：

当“卡俄斯”与主角的关系发生变化时，它“馈赠”的道具类型或效果强度也可能发生变化。

如果艾拉/里奥彻底倒向“调律者”，他们可能会开发出反制这些道具效果的手段，或者制造出能释放“虚假道具”（拾取后是陷阱）的敌人。