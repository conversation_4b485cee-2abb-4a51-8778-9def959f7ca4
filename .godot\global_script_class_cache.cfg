list=[{
"base": &"RefCounted",
"class": &"ModGridLegacy",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ModificationGrid.gd"
}, {
"base": &"Control",
"class": &"ModInventoryUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/ui/mod_inventory_ui.gd"
}, {
"base": &"Node",
"class": &"TooltipManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/ui/tooltip_manager.gd"
}]
