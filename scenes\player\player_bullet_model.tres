[gd_resource type="GradientTexture2D" load_steps=2 format=3 uid="uid://cjjg6xb3w1ks1"]

[sub_resource type="Gradient" id="Gradient_qkx1h"]
offsets = PackedFloat32Array(0, 1)
colors = PackedColorArray(0.4, 0.7, 1, 1, 0.2, 0.5, 0.9, 0.8)

[resource]
gradient = SubResource("Gradient_qkx1h")
width = 16
height = 16
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(0.5, 0)
metadata/_generator_type = &"player_bullet" 