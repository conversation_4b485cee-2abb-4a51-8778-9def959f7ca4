extends Node

# 游戏枚举定义
# 集中管理所有游戏中使用的枚举类型

# 道具类型枚举
enum PowerUpType {
	TRIPLE_SHOT = 0,  # T - 连续射击
	AUTO_AIM = 1,     # A - 追踪子弹
	SPREAD_SHOT = 2,  # S - 散射
	HEALTH_UP = 3,    # + - 回血
	BOMB = 4,         # B - 炸弹
	SPEED_BOOST = 5   # > - 速度提升
}

# 敌人移动模式枚举
enum EnemyMovementMode {
	RANDOM_DRIFT = 0,    # 随机漂移（默认）
	STRAIGHT_DOWN = 1,   # 直线向下
	LEFT_DRIFT = 2,      # 向左漂移
	RIGHT_DRIFT = 3,     # 向右漂移
	ZIGZAG = 4,          # 之字形移动
	SINE_WAVE = 5        # 正弦波移动
}

# 资源类型枚举
enum ResourceType {
	HEALTH = 0,
	PLAYER = 1,
	ENEMY_TRIANGLE = 2,
	PLAYER_BULLET = 3,
	ENEMY_BULLET = 4
}

# 获取道具类型名称
static func get_powerup_type_name(type: int) -> String:
	match type:
		PowerUpType.TRIPLE_SHOT:
			return "Triple Shot"
		PowerUpType.AUTO_AIM:
			return "Auto Aim"
		PowerUpType.SPREAD_SHOT:
			return "Spread Shot"
		PowerUpType.HEALTH_UP:
			return "Health Up"
		PowerUpType.BOMB:
			return "Bomb"
		PowerUpType.SPEED_BOOST:
			return "Speed Boost"
		_:
			return "Unknown"

# 获取道具类型资源路径名称
static func get_powerup_resource_name(type: int) -> String:
	match type:
		PowerUpType.TRIPLE_SHOT:
			return "triple_shot"
		PowerUpType.AUTO_AIM:
			return "auto_aim"
		PowerUpType.SPREAD_SHOT:
			return "spread_shot"
		PowerUpType.HEALTH_UP:
			return "health_up"
		PowerUpType.BOMB:
			return "bomb"
		PowerUpType.SPEED_BOOST:
			return "speed_boost"
		_:
			return "unknown" 