extends Node

# 资源加载器
# 负责从自定义文件夹加载图片资源

# 基础路径
const CUSTOM_RESOURCES_PATH = "res://custom_resources/"

# 道具路径
const POWERUP_PATHS = {
	"triple_shot": CUSTOM_RESOURCES_PATH + "powerups/triple_shot/",
	"auto_aim": CUSTOM_RESOURCES_PATH + "powerups/auto_aim/",
	"spread_shot": CUSTOM_RESOURCES_PATH + "powerups/spread_shot/",
	"health_up": CUSTOM_RESOURCES_PATH + "powerups/health_up/",
	"bomb": CUSTOM_RESOURCES_PATH + "powerups/bomb/",
	"speed_boost": CUSTOM_RESOURCES_PATH + "powerups/speed_boost/"
}

# 支持的图片格式
const SUPPORTED_FORMATS = ["png", "jpg", "jpeg", "webp"]

# 加载道具图片
func load_powerup_texture(powerup_type: String) -> Texture2D:
	# 检查道具类型是否有效
	if not POWERUP_PATHS.has(powerup_type):
		push_error("无效的道具类型: " + powerup_type)
		return null
	
	# 获取道具路径
	var path = POWERUP_PATHS[powerup_type]
	
	# 查找文件夹中的第一个图片
	var dir = DirAccess.open(path)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		
		while file_name != "":
			# 检查是否是文件且是支持的图片格式
			if not dir.current_is_dir():
				var extension = file_name.get_extension().to_lower()
				if SUPPORTED_FORMATS.has(extension):
					var texture = load(path + file_name)
					dir.list_dir_end()
					print("已加载自定义道具图片: " + path + file_name)
					return texture
			
			file_name = dir.get_next()
		
		dir.list_dir_end()
	
	# 如果没有找到自定义图片，返回null
	print("未找到自定义道具图片: " + powerup_type)
	return null

# 检查是否有自定义道具图片
func has_custom_powerup_texture(powerup_type: String) -> bool:
	# 检查道具类型是否有效
	if not POWERUP_PATHS.has(powerup_type):
		return false
	
	# 获取道具路径
	var path = POWERUP_PATHS[powerup_type]
	
	# 查找文件夹中是否有图片
	var dir = DirAccess.open(path)
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		
		while file_name != "":
			# 检查是否是文件且是支持的图片格式
			if not dir.current_is_dir():
				var extension = file_name.get_extension().to_lower()
				if SUPPORTED_FORMATS.has(extension):
					dir.list_dir_end()
					return true
			
			file_name = dir.get_next()
		
		dir.list_dir_end()
	
	return false 