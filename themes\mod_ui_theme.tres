[gd_resource type="Theme" load_steps=10 format=3 uid="uid://dd8xdg3twycsx"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_rnxpw"]
content_margin_left = 16.0
content_margin_top = 8.0
content_margin_right = 16.0
content_margin_bottom = 8.0
bg_color = Color(0.168627, 0.188235, 0.25098, 0.901961)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.294118, 0.419608, 0.6, 0.501961)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ql7u0"]
content_margin_left = 16.0
content_margin_top = 8.0
content_margin_right = 16.0
content_margin_bottom = 8.0
bg_color = Color(0.227451, 0.290196, 0.403922, 0.901961)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.294118, 0.541176, 0.823529, 0.752941)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f0unt"]
content_margin_left = 16.0
content_margin_top = 8.0
content_margin_right = 16.0
content_margin_bottom = 8.0
bg_color = Color(0.137255, 0.164706, 0.25098, 0.901961)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.294118, 0.419608, 0.6, 0.752941)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6hxce"]
content_margin_left = 16.0
content_margin_top = 8.0
content_margin_right = 16.0
content_margin_bottom = 8.0
bg_color = Color(0.109804, 0.14902, 0.231373, 0.901961)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.313726, 0.568627, 0.827451, 1)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kkf2h"]
content_margin_left = 16.0
content_margin_top = 8.0
content_margin_right = 16.0
content_margin_bottom = 8.0
bg_color = Color(0.192157, 0.223529, 0.309804, 0.901961)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.152941, 0.219608, 0.317647, 0.752941)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="FontVariation" id="FontVariation_fmxes"]
variation_embolden = 0.5
spacing_glyph = 1
spacing_space = 1

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_jgtsd"]
content_margin_left = 10.0
content_margin_top = 4.0
content_margin_right = 10.0
content_margin_bottom = 4.0
bg_color = Color(0.141176, 0.168627, 0.243137, 0.941176)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.294118, 0.419608, 0.6, 0.501961)
corner_radius_top_left = 3
corner_radius_top_right = 3
corner_radius_bottom_right = 3
corner_radius_bottom_left = 3

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_lqpdu"]
content_margin_left = 12.0
content_margin_top = 12.0
content_margin_right = 12.0
content_margin_bottom = 12.0
bg_color = Color(0.141176, 0.168627, 0.243137, 0.901961)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.294118, 0.419608, 0.6, 0.501961)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uotwm"]
bg_color = Color(0.141176, 0.168627, 0.243137, 0.901961)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.294118, 0.419608, 0.6, 0.501961)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[resource]
default_font = SubResource("FontVariation_fmxes")
default_font_size = 18
Button/colors/font_color = Color(0.882353, 0.882353, 0.882353, 1)
Button/colors/font_focus_color = Color(0.952941, 0.952941, 0.952941, 1)
Button/colors/font_hover_color = Color(1, 1, 1, 1)
Button/colors/font_hover_pressed_color = Color(0.929412, 0.929412, 0.929412, 1)
Button/colors/font_outline_color = Color(0.156863, 0.156863, 0.2, 1)
Button/colors/font_pressed_color = Color(0.929412, 0.929412, 0.929412, 1)
Button/constants/outline_size = 1
Button/font_sizes/font_size = 36
Button/styles/disabled = SubResource("StyleBoxFlat_rnxpw")
Button/styles/focus = SubResource("StyleBoxFlat_ql7u0")
Button/styles/hover = SubResource("StyleBoxFlat_f0unt")
Button/styles/normal = SubResource("StyleBoxFlat_6hxce")
Button/styles/pressed = SubResource("StyleBoxFlat_kkf2h")
Label/colors/font_color = Color(0.882353, 0.882353, 0.882353, 1)
Label/colors/font_outline_color = Color(0.156863, 0.156863, 0.2, 1)
Label/colors/font_shadow_color = Color(0, 0, 0, 0.2)
Label/constants/line_spacing = 2
Label/constants/outline_size = 1
Label/constants/shadow_offset_x = 1
Label/constants/shadow_offset_y = 1
Label/constants/shadow_outline_size = 1
Label/font_sizes/normal = 40
Label/font_sizes/title = 48
Label/font_sizes/value = 42
Label/fonts/bold = SubResource("FontVariation_fmxes")
LineEdit/colors/font_color = Color(0.882353, 0.882353, 0.882353, 1)
LineEdit/colors/font_outline_color = Color(0.156863, 0.156863, 0.2, 1)
LineEdit/constants/outline_size = 1
LineEdit/font_sizes/font_size = 16
LineEdit/styles/normal = SubResource("StyleBoxFlat_jgtsd")
Panel/styles/panel = SubResource("StyleBoxFlat_lqpdu")
PanelContainer/styles/panel = SubResource("StyleBoxFlat_uotwm")
RichTextLabel/colors/default_color = Color(0.882353, 0.882353, 0.882353, 1)
RichTextLabel/colors/font_outline_color = Color(0.156863, 0.156863, 0.2, 1)
RichTextLabel/constants/outline_size = 1
RichTextLabel/font_sizes/bold_font_size = 18
RichTextLabel/font_sizes/normal_font_size = 16
RichTextLabel/fonts/bold_font = SubResource("FontVariation_fmxes")
RichTextLabel/fonts/normal_font = SubResource("FontVariation_fmxes")
