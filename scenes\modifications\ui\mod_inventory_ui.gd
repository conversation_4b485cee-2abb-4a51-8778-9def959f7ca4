extends Control

class_name ModInventoryUI

# 信号
signal mod_item_selected(mod_resource)

# 组件引用 - 只保留必要的引用
@onready var scroll_container = %ScrollContainer
@onready var item_grid = %ItemGrid

# 预加载脚本
const ModInventoryItemEntryScript = preload("res://scenes/modifications/ui/mod_inventory_item_entry.gd")

# 改装件列表
var all_modifications = []
var displayed_modifications = []

# 当前筛选
var current_size_filter = "all"
var current_type_filter = "all"

# 数据管理器
var mod_data_manager = null

# 常量
const MOD_ENTRY_SIZE = Vector2(110, 140) # 减小物品尺寸以适应更小的垂直空间
const GRID_SPACING = 10 # 保持较小的网格间距

# 准备
func _ready():
	print("ModInventoryUI: 初始化开始")
	
	# 加载管理器
	_load_data_manager()
	
	# 检查数据管理器是否成功加载
	if not mod_data_manager:
		push_error("ModInventoryUI: 无法加载数据管理器")
		return
	
	# 加载改装件
	_load_modifications()
	
	# 设置初始筛选状态
	_apply_filters()
	
	print("ModInventoryUI: 初始化完成")

# 加载数据管理器
func _load_data_manager():
	# 使用preload而不是load，避免循环引用问题
	var ModificationDataManagerClass = preload("res://scenes/modifications/data/modification_data_manager.gd")
	mod_data_manager = ModificationDataManagerClass.get_instance()

# 设置筛选 - 这些方法现在将由父级调用
func set_size_filter(size_filter: String):
	current_size_filter = size_filter
	_apply_filters()

# 设置类型筛选
func set_type_filter(type_filter: String):
	current_type_filter = type_filter
	_apply_filters()

# 应用筛选并刷新显示
func _apply_filters():
	# 清空显示列表
	displayed_modifications.clear()
	
	# 应用筛选
	for mod_data in all_modifications:
		var mod = mod_data["resource"]
		
		# 尺寸筛选
		var size_match = current_size_filter == "all"
		
		if current_size_filter == "small" and mod.has_effect_tag("size_small"):
			size_match = true
		elif current_size_filter == "medium" and mod.has_effect_tag("size_medium"):
			size_match = true
		elif current_size_filter == "large" and mod.has_effect_tag("size_large"):
			size_match = true
		
		# 类型筛选
		var type_match = current_type_filter == "all"
		
		if current_type_filter == "attack" and mod.has_effect_tag("type_attack"):
			type_match = true
		elif current_type_filter == "defense" and mod.has_effect_tag("type_defense"):
			type_match = true
		elif current_type_filter == "special" and mod.has_effect_tag("type_special"):
			type_match = true
		elif current_type_filter == "core" and mod.has_effect_tag("type_core"):
			type_match = true
		
		# 如果同时匹配尺寸和类型筛选，则添加到显示列表
		if size_match and type_match:
			displayed_modifications.append(mod_data)
	
	# 刷新显示
	_refresh_display()

# 加载改装件
func _load_modifications():
	# 清空当前列表
	all_modifications.clear()
	
	# 加载模块，使用preload避免运行时加载问题
	var ModInventoryClass = preload("res://scenes/modifications/data/mod_inventory.gd")
	var mod_inventory = ModInventoryClass.get_instance()
	
	# 获取所有改装件ID
	var mod_ids = mod_inventory.get_all_modification_ids()
	
	print("获取到的改装件ID: ", mod_ids)
	
	# 加载每个改装件数据
	for id in mod_ids:
		var mod_resource = mod_data_manager.get_modification_data(id)
		if mod_resource:
			# 设置数量和等级属性
			var quantity = mod_inventory.get_modification_quantity(id)
			var level = mod_inventory.get_modification_level(id)
			
			# 添加到全部改装件列表
			all_modifications.append({
				"resource": mod_resource,
				"quantity": quantity,
				"level": level
			})
	
	print("ModInventoryUI: 已加载 " + str(all_modifications.size()) + " 个改装件")

# 刷新显示
func _refresh_display():
	# 清空网格
	for child in item_grid.get_children():
		child.queue_free()
	
	# 填充网格
	for mod_data in displayed_modifications:
		_add_mod_to_grid(mod_data)
	
	print("ModInventoryUI: 显示 " + str(displayed_modifications.size()) + " 个改装件")

# 添加改装件到网格
func _add_mod_to_grid(mod_data):
	# 创建物品条目
	var item = Panel.new()
	item.custom_minimum_size = MOD_ENTRY_SIZE
	item.set_script(ModInventoryItemEntryScript)
	
	# 设置物品数据
	item.mod_resource = mod_data["resource"]
	item.mod_selected.connect(_on_mod_selected)
	
	# 如果有数量和等级信息，设置到物品条目
	if mod_data.has("quantity"):
		item.quantity = mod_data["quantity"]
	
	if mod_data.has("level"):
		item.level = mod_data["level"]
	
	# 添加到网格
	item.update_display()  # 确保更新显示
	item_grid.add_child(item)

# 改装件被选中
func _on_mod_selected(mod_resource):
	emit_signal("mod_item_selected", mod_resource)

# 刷新物品栏 - 用于外部调用
func refresh():
	print("ModInventoryUI: 刷新开始")
	
	# 检查数据管理器是否已加载
	if not mod_data_manager:
		# 尝试重新加载数据管理器
		_load_data_manager()
		if not mod_data_manager:
			push_error("ModInventoryUI: 刷新时无法加载数据管理器")
			return
	
	# 重新加载改装件
	_load_modifications()
	
	# 重新应用筛选
	_apply_filters()
	
	print("ModInventoryUI: 刷新完成")

# 从显示中移除改装件
func remove_modification_from_display(mod_name: String):
	# 从显示列表中移除
	for i in range(displayed_modifications.size() - 1, -1, -1):
		var mod_data = displayed_modifications[i]
		if mod_data["resource"].name == mod_name:
			displayed_modifications.remove_at(i)
			print("ModInventoryUI: 从显示中移除 " + mod_name)
			break

	# 刷新显示
	_refresh_display()

# 添加改装件到显示
func add_modification_to_display(mod_name: String, mod_color: Color):
	# 查找对应的改装件数据
	for mod_data in all_modifications:
		if mod_data["resource"].name == mod_name:
			# 检查是否已经在显示列表中
			var already_displayed = false
			for displayed_mod in displayed_modifications:
				if displayed_mod["resource"].name == mod_name:
					already_displayed = true
					break

			# 如果不在显示列表中，添加它
			if not already_displayed:
				displayed_modifications.append(mod_data)
				print("ModInventoryUI: 添加到显示 " + mod_name)
				# 刷新显示
				_refresh_display()
			break