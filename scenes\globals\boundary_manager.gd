extends Node

# 边界管理器
# 提供通用的边界检查和限制方法

# 屏幕尺寸
var screen_width: float = 1280  # 默认值
var screen_height: float = 720  # 默认值
var sidebar_width: float = 0    # 侧边栏宽度

# 初始化
func _ready():
	# 在游戏启动时获取屏幕尺寸
	update_screen_size()
	
	# 监听窗口大小变化
	get_viewport().size_changed.connect(update_screen_size)

# 更新屏幕尺寸
func update_screen_size():
	var viewport_rect = get_viewport().get_visible_rect()
	screen_width = viewport_rect.size.x
	screen_height = viewport_rect.size.y
	print("BoundaryManager: 屏幕尺寸更新为 ", screen_width, "x", screen_height)

# 设置侧边栏宽度
func set_sidebar_width(width: float):
	sidebar_width = width

# 限制位置在屏幕内
func clamp_position(position: Vector2, object_size: float = 0) -> Vector2:
	var new_position = position
	new_position.x = clamp(new_position.x, sidebar_width + object_size, screen_width - sidebar_width - object_size)
	new_position.y = clamp(new_position.y, object_size, screen_height - object_size)
	return new_position

# 限制位置在屏幕内（考虑侧边栏）
func clamp_position_with_sidebar(position: Vector2, object_size: float = 0) -> Vector2:
	return clamp_position(position, object_size)

# 检查位置是否在屏幕内
func is_inside_screen(position: Vector2, margin: float = 0) -> bool:
	return position.x >= (sidebar_width - margin) and \
		   position.x <= (screen_width - sidebar_width + margin) and \
		   position.y >= -margin and \
		   position.y <= (screen_height + margin)

# 检查位置是否在屏幕外
func is_outside_screen(position: Vector2, margin: float = 0) -> bool:
	return !is_inside_screen(position, margin)

# 获取屏幕矩形
func get_screen_rect() -> Rect2:
	return Rect2(0, 0, screen_width, screen_height)

# 获取可玩区域矩形（考虑侧边栏）
func get_playable_rect() -> Rect2:
	return Rect2(sidebar_width, 0, screen_width - 2 * sidebar_width, screen_height)

# 获取随机位置（在可玩区域内）
func get_random_position(object_size: float = 0) -> Vector2:
	var x = randf_range(sidebar_width + object_size, screen_width - sidebar_width - object_size)
	var y = randf_range(object_size, screen_height - object_size)
	return Vector2(x, y)

# 获取屏幕顶部随机位置（用于生成敌人）
func get_random_top_position(object_size: float = 0) -> Vector2:
	var x = randf_range(sidebar_width + object_size, screen_width - sidebar_width - object_size)
	return Vector2(x, -object_size)

# 获取屏幕底部随机位置
func get_random_bottom_position(object_size: float = 0) -> Vector2:
	var x = randf_range(sidebar_width + object_size, screen_width - sidebar_width - object_size)
	return Vector2(x, screen_height + object_size)

# 获取屏幕左侧随机位置
func get_random_left_position(object_size: float = 0) -> Vector2:
	var y = randf_range(object_size, screen_height - object_size)
	return Vector2(sidebar_width - object_size, y)

# 获取屏幕右侧随机位置
func get_random_right_position(object_size: float = 0) -> Vector2:
	var y = randf_range(object_size, screen_height - object_size)
	return Vector2(screen_width - sidebar_width + object_size, y) 
