extends Node

# 全局单例ModificationManager实例
var modification_manager = null

func _ready():
	# 动态加载ModificationManager类
	var script = load("res://scenes/modifications/scripts/modification_system.gd")
	modification_manager = script.new()
	
	# 将ModificationManager添加为子节点
	add_child(modification_manager)
	print("全局ModificationManager已初始化")
	
	# 延迟设置玩家引用，确保玩家已经加载
	call_deferred("_setup_player_reference")

# 设置玩家引用
func _setup_player_reference():
	# 等待一帧，确保场景树已完全加载
	await get_tree().process_frame
	
	# 尝试获取玩家节点
	var player = get_tree().get_first_node_in_group("player")
	if player:
		# 设置玩家引用
		modification_manager.player = player
		print("ModificationManager: 已设置玩家引用")
	else:
		# 如果没有找到玩家，延迟再次尝试
		print("ModificationManager: 未找到玩家，将在稍后再次尝试")
		await get_tree().create_timer(0.5).timeout
		_setup_player_reference()

# 获取ModificationManager实例
func get_manager():
	return modification_manager 
