class_name ModGridLegacy
extends RefCounted

# 网格尺寸
const GRID_WIDTH = 11  # 横向11列
const GRID_HEIGHT = 9  # 垂直9行

# 网格单元格状态
enum CellState {EMPTY, OCCUPIED}

# 网格数据结构
var grid = []

# 存储已放置的配件及其位置信息
var placed_modifications = {}  # 格式: {mod_id: {modification: mod_obj, position: Vector2i, rotation: int}}

# 初始化网格
func _init():
	reset_grid()

# 重置网格
func reset_grid():
	grid = []
	for y in range(GRID_HEIGHT):
		var row = []
		for x in range(GRID_WIDTH):
			row.append(CellState.EMPTY)
		grid.append(row)
	
	placed_modifications.clear()

# 检查坐标是否在网格范围内
func is_within_grid(x: int, y: int) -> bool:
	return x >= 0 and x < GRID_WIDTH and y >= 0 and y < GRID_HEIGHT

# 获取单元格状态
func get_cell_state(x: int, y: int) -> int:
	if not is_within_grid(x, y):
		return CellState.OCCUPIED  # 网格外的单元格视为已占用
	return grid[y][x]

# 设置单元格状态
func set_cell_state(x: int, y: int, state: int) -> void:
	if is_within_grid(x, y):
		grid[y][x] = state

# 检查指定位置是否可以放置配件
func can_place_modification(mod: ModificationResource, pos_x: int, pos_y: int) -> bool:
	var shape = mod.get_current_shape()
	
	# 检查每个形状点是否可放置
	for point in shape:
		var x = pos_x + point[0]
		var y = pos_y + point[1]
		
		# 检查是否在网格范围内且单元格为空
		if not is_within_grid(x, y) or get_cell_state(x, y) != CellState.EMPTY:
			return false
	
	# 检查连接性规则（至少与一个已放置的配件共享边）
	# 如果是第一个放置的配件，则不需要检查连接性
	if placed_modifications.size() == 0:
		return true
	
	# 检查是否与已放置的配件共享边
	for point in shape:
		var x = pos_x + point[0]
		var y = pos_y + point[1]
		
		# 检查四个方向的相邻单元格
		var adjacent_positions = [
			Vector2i(x+1, y),
			Vector2i(x-1, y),
			Vector2i(x, y+1),
			Vector2i(x, y-1)
		]
		
		for adj_pos in adjacent_positions:
			if is_within_grid(adj_pos.x, adj_pos.y) and get_cell_state(adj_pos.x, adj_pos.y) == CellState.OCCUPIED:
				return true
	
	return false

# 放置配件
func place_modification(mod: ModificationResource, pos_x: int, pos_y: int) -> bool:
	# 检查是否可以放置
	if not can_place_modification(mod, pos_x, pos_y):
		return false
	
	# 获取当前形状
	var shape = mod.get_current_shape()
	
	# 占用网格单元格
	for point in shape:
		var x = pos_x + point[0]
		var y = pos_y + point[1]
		set_cell_state(x, y, CellState.OCCUPIED)
	
	# 存储配件及其位置信息
	placed_modifications[mod.id] = {
		"modification": mod,
		"position": Vector2i(pos_x, pos_y),
		"rotation": mod.rotation
	}
	
	return true

# 移除配件
func remove_modification(mod_id: String) -> bool:
	if not placed_modifications.has(mod_id):
		return false
	
	var mod_data = placed_modifications[mod_id]
	var mod = mod_data.modification
	var pos_x = mod_data.position.x
	var pos_y = mod_data.position.y
	
	# 恢复原始旋转状态（如果需要）
	var current_rotation = mod.rotation
	if current_rotation != mod_data.rotation:
		# 旋转回原始状态
		while mod.rotation != mod_data.rotation:
			mod.rotate_shape()
	
	# 获取形状
	var shape = mod.get_current_shape()
	
	# 释放网格单元格
	for point in shape:
		var x = pos_x + point[0]
		var y = pos_y + point[1]
		if is_within_grid(x, y):
			set_cell_state(x, y, CellState.EMPTY)
	
	# 从已放置配件列表中移除
	placed_modifications.erase(mod_id)
	
	# 恢复旋转状态（如果之前改变了）
	if current_rotation != mod_data.rotation:
		# 旋转回当前状态
		while mod.rotation != current_rotation:
			mod.rotate_shape()
	
	return true

# 旋转配件
func rotate_modification(mod_id: String) -> bool:
	if not placed_modifications.has(mod_id):
		return false
	
	var mod_data = placed_modifications[mod_id]
	var mod = mod_data.modification
	var pos_x = mod_data.position.x
	var pos_y = mod_data.position.y
	
	# 先从网格中移除配件
	remove_modification(mod_id)
	
	# 旋转配件
	mod.rotate_shape()
	
	# 尝试放回网格
	if not place_modification(mod, pos_x, pos_y):
		# 如果放置失败，恢复原来的旋转状态并重新放置
		mod.rotate_shape()
		mod.rotate_shape()
		mod.rotate_shape()  # 旋转回原始状态
		place_modification(mod, pos_x, pos_y)
		return false
	
	return true

# 移动配件
func move_modification(mod_id: String, new_pos_x: int, new_pos_y: int) -> bool:
	if not placed_modifications.has(mod_id):
		return false
	
	var mod_data = placed_modifications[mod_id]
	var mod = mod_data.modification
	
	# 先从网格中移除配件
	remove_modification(mod_id)
	
	# 尝试在新位置放置配件
	if not place_modification(mod, new_pos_x, new_pos_y):
		# 如果放置失败，恢复到原来的位置
		place_modification(mod, mod_data.position.x, mod_data.position.y)
		return false
	
	return true

# 获取已放置的配件列表
func get_placed_modifications() -> Dictionary:
	return placed_modifications

# 获取指定位置的配件ID
func get_modification_at_position(pos_x: int, pos_y: int) -> String:
	for mod_id in placed_modifications:
		var mod_data = placed_modifications[mod_id]
		var mod = mod_data.modification
		var mod_pos_x = mod_data.position.x
		var mod_pos_y = mod_data.position.y
		
		var shape = mod.get_current_shape()
		for point in shape:
			var x = mod_pos_x + point[0]
			var y = mod_pos_y + point[1]
			
			if x == pos_x and y == pos_y:
				return mod_id
	
	return ""

# 计算当前网格中所有配件的总质量值
func calculate_total_mass() -> float:
	var total_mass = 0.0
	
	for mod_id in placed_modifications:
		var mod = placed_modifications[mod_id].modification
		total_mass += mod.mass_value
	
	return total_mass

# 计算当前网格中所有配件的总功率消耗
func calculate_total_power_consumption() -> float:
	var total_power = 0.0
	
	for mod_id in placed_modifications:
		var mod = placed_modifications[mod_id].modification
		total_power += mod.power_consumption
	
	return total_power

# 获取特定类型的配件数量
func get_modification_count_by_type(type: int) -> int:
	var count = 0
	
	for mod_id in placed_modifications:
		var mod = placed_modifications[mod_id].modification
		if mod.type == type:
			count += 1
	
	return count

# 获取特定尺寸类别的配件数量
func get_modification_count_by_size(size_type: int) -> int:
	var count = 0
	
	for mod_id in placed_modifications:
		var mod = placed_modifications[mod_id].modification
		if mod.size_type == size_type:
			count += 1
	
	return count

# 打印网格状态（用于调试）
func print_grid():
	print("当前网格状态:")
	for y in range(GRID_HEIGHT):
		var row_str = ""
		for x in range(GRID_WIDTH):
			if grid[y][x] == CellState.EMPTY:
				row_str += ". "
			else:
				row_str += "X "
		print(row_str) 